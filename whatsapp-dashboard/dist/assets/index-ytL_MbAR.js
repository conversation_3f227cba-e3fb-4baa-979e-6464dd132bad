function Xy(a,o){for(var s=0;s<o.length;s++){const c=o[s];if(typeof c!="string"&&!Array.isArray(c)){for(const f in c)if(f!=="default"&&!(f in a)){const m=Object.getOwnPropertyDescriptor(c,f);m&&Object.defineProperty(a,f,m.get?m:{enumerable:!0,get:()=>c[f]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const f of document.querySelectorAll('link[rel="modulepreload"]'))c(f);new MutationObserver(f=>{for(const m of f)if(m.type==="childList")for(const h of m.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&c(h)}).observe(document,{childList:!0,subtree:!0});function s(f){const m={};return f.integrity&&(m.integrity=f.integrity),f.referrerPolicy&&(m.referrerPolicy=f.referrerPolicy),f.crossOrigin==="use-credentials"?m.credentials="include":f.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function c(f){if(f.ep)return;f.ep=!0;const m=s(f);fetch(f.href,m)}})();function Nv(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var Xs={exports:{}},_i={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lh;function Qy(){if(Lh)return _i;Lh=1;var a=Symbol.for("react.transitional.element"),o=Symbol.for("react.fragment");function s(c,f,m){var h=null;if(m!==void 0&&(h=""+m),f.key!==void 0&&(h=""+f.key),"key"in f){m={};for(var v in f)v!=="key"&&(m[v]=f[v])}else m=f;return f=m.ref,{$$typeof:a,type:c,key:h,ref:f!==void 0?f:null,props:m}}return _i.Fragment=o,_i.jsx=s,_i.jsxs=s,_i}var Bh;function Zy(){return Bh||(Bh=1,Xs.exports=Qy()),Xs.exports}var u=Zy(),Qs={exports:{}},ye={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kh;function Ky(){if(kh)return ye;kh=1;var a=Symbol.for("react.transitional.element"),o=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),m=Symbol.for("react.consumer"),h=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),N=Symbol.iterator;function C(E){return E===null||typeof E!="object"?null:(E=N&&E[N]||E["@@iterator"],typeof E=="function"?E:null)}var O={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,b={};function T(E,G,F){this.props=E,this.context=G,this.refs=b,this.updater=F||O}T.prototype.isReactComponent={},T.prototype.setState=function(E,G){if(typeof E!="object"&&typeof E!="function"&&E!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,E,G,"setState")},T.prototype.forceUpdate=function(E){this.updater.enqueueForceUpdate(this,E,"forceUpdate")};function L(){}L.prototype=T.prototype;function D(E,G,F){this.props=E,this.context=G,this.refs=b,this.updater=F||O}var B=D.prototype=new L;B.constructor=D,R(B,T.prototype),B.isPureReactComponent=!0;var K=Array.isArray,V={H:null,A:null,T:null,S:null,V:null},ee=Object.prototype.hasOwnProperty;function P(E,G,F,W,te,ge){return F=ge.ref,{$$typeof:a,type:E,key:G,ref:F!==void 0?F:null,props:ge}}function $(E,G){return P(E.type,G,void 0,void 0,void 0,E.props)}function se(E){return typeof E=="object"&&E!==null&&E.$$typeof===a}function J(E){var G={"=":"=0",":":"=2"};return"$"+E.replace(/[=:]/g,function(F){return G[F]})}var ue=/\/+/g;function oe(E,G){return typeof E=="object"&&E!==null&&E.key!=null?J(""+E.key):G.toString(36)}function ve(){}function pe(E){switch(E.status){case"fulfilled":return E.value;case"rejected":throw E.reason;default:switch(typeof E.status=="string"?E.then(ve,ve):(E.status="pending",E.then(function(G){E.status==="pending"&&(E.status="fulfilled",E.value=G)},function(G){E.status==="pending"&&(E.status="rejected",E.reason=G)})),E.status){case"fulfilled":return E.value;case"rejected":throw E.reason}}throw E}function Y(E,G,F,W,te){var ge=typeof E;(ge==="undefined"||ge==="boolean")&&(E=null);var ce=!1;if(E===null)ce=!0;else switch(ge){case"bigint":case"string":case"number":ce=!0;break;case"object":switch(E.$$typeof){case a:case o:ce=!0;break;case S:return ce=E._init,Y(ce(E._payload),G,F,W,te)}}if(ce)return te=te(E),ce=W===""?"."+oe(E,0):W,K(te)?(F="",ce!=null&&(F=ce.replace(ue,"$&/")+"/"),Y(te,G,F,"",function(Oe){return Oe})):te!=null&&(se(te)&&(te=$(te,F+(te.key==null||E&&E.key===te.key?"":(""+te.key).replace(ue,"$&/")+"/")+ce)),G.push(te)),1;ce=0;var I=W===""?".":W+":";if(K(E))for(var fe=0;fe<E.length;fe++)W=E[fe],ge=I+oe(W,fe),ce+=Y(W,G,F,ge,te);else if(fe=C(E),typeof fe=="function")for(E=fe.call(E),fe=0;!(W=E.next()).done;)W=W.value,ge=I+oe(W,fe++),ce+=Y(W,G,F,ge,te);else if(ge==="object"){if(typeof E.then=="function")return Y(pe(E),G,F,W,te);throw G=String(E),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(E).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return ce}function _(E,G,F){if(E==null)return E;var W=[],te=0;return Y(E,W,"","",function(ge){return G.call(F,ge,te++)}),W}function Z(E){if(E._status===-1){var G=E._result;G=G(),G.then(function(F){(E._status===0||E._status===-1)&&(E._status=1,E._result=F)},function(F){(E._status===0||E._status===-1)&&(E._status=2,E._result=F)}),E._status===-1&&(E._status=0,E._result=G)}if(E._status===1)return E._result.default;throw E._result}var k=typeof reportError=="function"?reportError:function(E){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof E=="object"&&E!==null&&typeof E.message=="string"?String(E.message):String(E),error:E});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",E);return}console.error(E)};function ie(){}return ye.Children={map:_,forEach:function(E,G,F){_(E,function(){G.apply(this,arguments)},F)},count:function(E){var G=0;return _(E,function(){G++}),G},toArray:function(E){return _(E,function(G){return G})||[]},only:function(E){if(!se(E))throw Error("React.Children.only expected to receive a single React element child.");return E}},ye.Component=T,ye.Fragment=s,ye.Profiler=f,ye.PureComponent=D,ye.StrictMode=c,ye.Suspense=y,ye.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=V,ye.__COMPILER_RUNTIME={__proto__:null,c:function(E){return V.H.useMemoCache(E)}},ye.cache=function(E){return function(){return E.apply(null,arguments)}},ye.cloneElement=function(E,G,F){if(E==null)throw Error("The argument must be a React element, but you passed "+E+".");var W=R({},E.props),te=E.key,ge=void 0;if(G!=null)for(ce in G.ref!==void 0&&(ge=void 0),G.key!==void 0&&(te=""+G.key),G)!ee.call(G,ce)||ce==="key"||ce==="__self"||ce==="__source"||ce==="ref"&&G.ref===void 0||(W[ce]=G[ce]);var ce=arguments.length-2;if(ce===1)W.children=F;else if(1<ce){for(var I=Array(ce),fe=0;fe<ce;fe++)I[fe]=arguments[fe+2];W.children=I}return P(E.type,te,void 0,void 0,ge,W)},ye.createContext=function(E){return E={$$typeof:h,_currentValue:E,_currentValue2:E,_threadCount:0,Provider:null,Consumer:null},E.Provider=E,E.Consumer={$$typeof:m,_context:E},E},ye.createElement=function(E,G,F){var W,te={},ge=null;if(G!=null)for(W in G.key!==void 0&&(ge=""+G.key),G)ee.call(G,W)&&W!=="key"&&W!=="__self"&&W!=="__source"&&(te[W]=G[W]);var ce=arguments.length-2;if(ce===1)te.children=F;else if(1<ce){for(var I=Array(ce),fe=0;fe<ce;fe++)I[fe]=arguments[fe+2];te.children=I}if(E&&E.defaultProps)for(W in ce=E.defaultProps,ce)te[W]===void 0&&(te[W]=ce[W]);return P(E,ge,void 0,void 0,null,te)},ye.createRef=function(){return{current:null}},ye.forwardRef=function(E){return{$$typeof:v,render:E}},ye.isValidElement=se,ye.lazy=function(E){return{$$typeof:S,_payload:{_status:-1,_result:E},_init:Z}},ye.memo=function(E,G){return{$$typeof:p,type:E,compare:G===void 0?null:G}},ye.startTransition=function(E){var G=V.T,F={};V.T=F;try{var W=E(),te=V.S;te!==null&&te(F,W),typeof W=="object"&&W!==null&&typeof W.then=="function"&&W.then(ie,k)}catch(ge){k(ge)}finally{V.T=G}},ye.unstable_useCacheRefresh=function(){return V.H.useCacheRefresh()},ye.use=function(E){return V.H.use(E)},ye.useActionState=function(E,G,F){return V.H.useActionState(E,G,F)},ye.useCallback=function(E,G){return V.H.useCallback(E,G)},ye.useContext=function(E){return V.H.useContext(E)},ye.useDebugValue=function(){},ye.useDeferredValue=function(E,G){return V.H.useDeferredValue(E,G)},ye.useEffect=function(E,G,F){var W=V.H;if(typeof F=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return W.useEffect(E,G)},ye.useId=function(){return V.H.useId()},ye.useImperativeHandle=function(E,G,F){return V.H.useImperativeHandle(E,G,F)},ye.useInsertionEffect=function(E,G){return V.H.useInsertionEffect(E,G)},ye.useLayoutEffect=function(E,G){return V.H.useLayoutEffect(E,G)},ye.useMemo=function(E,G){return V.H.useMemo(E,G)},ye.useOptimistic=function(E,G){return V.H.useOptimistic(E,G)},ye.useReducer=function(E,G,F){return V.H.useReducer(E,G,F)},ye.useRef=function(E){return V.H.useRef(E)},ye.useState=function(E){return V.H.useState(E)},ye.useSyncExternalStore=function(E,G,F){return V.H.useSyncExternalStore(E,G,F)},ye.useTransition=function(){return V.H.useTransition()},ye.version="19.1.0",ye}var qh;function Tu(){return qh||(qh=1,Qs.exports=Ky()),Qs.exports}var x=Tu();const Fn=Nv(x),Av=Xy({__proto__:null,default:Fn},[x]);var Zs={exports:{}},Oi={},Ks={exports:{}},Js={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vh;function Jy(){return Vh||(Vh=1,function(a){function o(_,Z){var k=_.length;_.push(Z);e:for(;0<k;){var ie=k-1>>>1,E=_[ie];if(0<f(E,Z))_[ie]=Z,_[k]=E,k=ie;else break e}}function s(_){return _.length===0?null:_[0]}function c(_){if(_.length===0)return null;var Z=_[0],k=_.pop();if(k!==Z){_[0]=k;e:for(var ie=0,E=_.length,G=E>>>1;ie<G;){var F=2*(ie+1)-1,W=_[F],te=F+1,ge=_[te];if(0>f(W,k))te<E&&0>f(ge,W)?(_[ie]=ge,_[te]=k,ie=te):(_[ie]=W,_[F]=k,ie=F);else if(te<E&&0>f(ge,k))_[ie]=ge,_[te]=k,ie=te;else break e}}return Z}function f(_,Z){var k=_.sortIndex-Z.sortIndex;return k!==0?k:_.id-Z.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;a.unstable_now=function(){return m.now()}}else{var h=Date,v=h.now();a.unstable_now=function(){return h.now()-v}}var y=[],p=[],S=1,N=null,C=3,O=!1,R=!1,b=!1,T=!1,L=typeof setTimeout=="function"?setTimeout:null,D=typeof clearTimeout=="function"?clearTimeout:null,B=typeof setImmediate<"u"?setImmediate:null;function K(_){for(var Z=s(p);Z!==null;){if(Z.callback===null)c(p);else if(Z.startTime<=_)c(p),Z.sortIndex=Z.expirationTime,o(y,Z);else break;Z=s(p)}}function V(_){if(b=!1,K(_),!R)if(s(y)!==null)R=!0,ee||(ee=!0,oe());else{var Z=s(p);Z!==null&&Y(V,Z.startTime-_)}}var ee=!1,P=-1,$=5,se=-1;function J(){return T?!0:!(a.unstable_now()-se<$)}function ue(){if(T=!1,ee){var _=a.unstable_now();se=_;var Z=!0;try{e:{R=!1,b&&(b=!1,D(P),P=-1),O=!0;var k=C;try{t:{for(K(_),N=s(y);N!==null&&!(N.expirationTime>_&&J());){var ie=N.callback;if(typeof ie=="function"){N.callback=null,C=N.priorityLevel;var E=ie(N.expirationTime<=_);if(_=a.unstable_now(),typeof E=="function"){N.callback=E,K(_),Z=!0;break t}N===s(y)&&c(y),K(_)}else c(y);N=s(y)}if(N!==null)Z=!0;else{var G=s(p);G!==null&&Y(V,G.startTime-_),Z=!1}}break e}finally{N=null,C=k,O=!1}Z=void 0}}finally{Z?oe():ee=!1}}}var oe;if(typeof B=="function")oe=function(){B(ue)};else if(typeof MessageChannel<"u"){var ve=new MessageChannel,pe=ve.port2;ve.port1.onmessage=ue,oe=function(){pe.postMessage(null)}}else oe=function(){L(ue,0)};function Y(_,Z){P=L(function(){_(a.unstable_now())},Z)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(_){_.callback=null},a.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<_?Math.floor(1e3/_):5},a.unstable_getCurrentPriorityLevel=function(){return C},a.unstable_next=function(_){switch(C){case 1:case 2:case 3:var Z=3;break;default:Z=C}var k=C;C=Z;try{return _()}finally{C=k}},a.unstable_requestPaint=function(){T=!0},a.unstable_runWithPriority=function(_,Z){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var k=C;C=_;try{return Z()}finally{C=k}},a.unstable_scheduleCallback=function(_,Z,k){var ie=a.unstable_now();switch(typeof k=="object"&&k!==null?(k=k.delay,k=typeof k=="number"&&0<k?ie+k:ie):k=ie,_){case 1:var E=-1;break;case 2:E=250;break;case 5:E=1073741823;break;case 4:E=1e4;break;default:E=5e3}return E=k+E,_={id:S++,callback:Z,priorityLevel:_,startTime:k,expirationTime:E,sortIndex:-1},k>ie?(_.sortIndex=k,o(p,_),s(y)===null&&_===s(p)&&(b?(D(P),P=-1):b=!0,Y(V,k-ie))):(_.sortIndex=E,o(y,_),R||O||(R=!0,ee||(ee=!0,oe()))),_},a.unstable_shouldYield=J,a.unstable_wrapCallback=function(_){var Z=C;return function(){var k=C;C=Z;try{return _.apply(this,arguments)}finally{C=k}}}}(Js)),Js}var Yh;function $y(){return Yh||(Yh=1,Ks.exports=Jy()),Ks.exports}var $s={exports:{}},mt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gh;function Wy(){if(Gh)return mt;Gh=1;var a=Tu();function o(y){var p="https://react.dev/errors/"+y;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var S=2;S<arguments.length;S++)p+="&args[]="+encodeURIComponent(arguments[S])}return"Minified React error #"+y+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var c={d:{f:s,r:function(){throw Error(o(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},f=Symbol.for("react.portal");function m(y,p,S){var N=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:f,key:N==null?null:""+N,children:y,containerInfo:p,implementation:S}}var h=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function v(y,p){if(y==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return mt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,mt.createPortal=function(y,p){var S=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(o(299));return m(y,p,null,S)},mt.flushSync=function(y){var p=h.T,S=c.p;try{if(h.T=null,c.p=2,y)return y()}finally{h.T=p,c.p=S,c.d.f()}},mt.preconnect=function(y,p){typeof y=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,c.d.C(y,p))},mt.prefetchDNS=function(y){typeof y=="string"&&c.d.D(y)},mt.preinit=function(y,p){if(typeof y=="string"&&p&&typeof p.as=="string"){var S=p.as,N=v(S,p.crossOrigin),C=typeof p.integrity=="string"?p.integrity:void 0,O=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;S==="style"?c.d.S(y,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:N,integrity:C,fetchPriority:O}):S==="script"&&c.d.X(y,{crossOrigin:N,integrity:C,fetchPriority:O,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},mt.preinitModule=function(y,p){if(typeof y=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var S=v(p.as,p.crossOrigin);c.d.M(y,{crossOrigin:S,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&c.d.M(y)},mt.preload=function(y,p){if(typeof y=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var S=p.as,N=v(S,p.crossOrigin);c.d.L(y,S,{crossOrigin:N,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},mt.preloadModule=function(y,p){if(typeof y=="string")if(p){var S=v(p.as,p.crossOrigin);c.d.m(y,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:S,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else c.d.m(y)},mt.requestFormReset=function(y){c.d.r(y)},mt.unstable_batchedUpdates=function(y,p){return y(p)},mt.useFormState=function(y,p,S){return h.H.useFormState(y,p,S)},mt.useFormStatus=function(){return h.H.useHostTransitionStatus()},mt.version="19.1.0",mt}var Xh;function Tv(){if(Xh)return $s.exports;Xh=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(o){console.error(o)}}return a(),$s.exports=Wy(),$s.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qh;function Py(){if(Qh)return Oi;Qh=1;var a=$y(),o=Tu(),s=Tv();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function m(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(m(e)!==e)throw Error(c(188))}function y(e){var t=e.alternate;if(!t){if(t=m(e),t===null)throw Error(c(188));return t!==e?null:e}for(var n=e,l=t;;){var i=n.return;if(i===null)break;var r=i.alternate;if(r===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===r.child){for(r=i.child;r;){if(r===n)return v(i),e;if(r===l)return v(i),t;r=r.sibling}throw Error(c(188))}if(n.return!==l.return)n=i,l=r;else{for(var d=!1,g=i.child;g;){if(g===n){d=!0,n=i,l=r;break}if(g===l){d=!0,l=i,n=r;break}g=g.sibling}if(!d){for(g=r.child;g;){if(g===n){d=!0,n=r,l=i;break}if(g===l){d=!0,l=r,n=i;break}g=g.sibling}if(!d)throw Error(c(189))}}if(n.alternate!==l)throw Error(c(190))}if(n.tag!==3)throw Error(c(188));return n.stateNode.current===n?e:t}function p(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=p(e),t!==null)return t;e=e.sibling}return null}var S=Object.assign,N=Symbol.for("react.element"),C=Symbol.for("react.transitional.element"),O=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),b=Symbol.for("react.strict_mode"),T=Symbol.for("react.profiler"),L=Symbol.for("react.provider"),D=Symbol.for("react.consumer"),B=Symbol.for("react.context"),K=Symbol.for("react.forward_ref"),V=Symbol.for("react.suspense"),ee=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),$=Symbol.for("react.lazy"),se=Symbol.for("react.activity"),J=Symbol.for("react.memo_cache_sentinel"),ue=Symbol.iterator;function oe(e){return e===null||typeof e!="object"?null:(e=ue&&e[ue]||e["@@iterator"],typeof e=="function"?e:null)}var ve=Symbol.for("react.client.reference");function pe(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ve?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case R:return"Fragment";case T:return"Profiler";case b:return"StrictMode";case V:return"Suspense";case ee:return"SuspenseList";case se:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case O:return"Portal";case B:return(e.displayName||"Context")+".Provider";case D:return(e._context.displayName||"Context")+".Consumer";case K:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case P:return t=e.displayName||null,t!==null?t:pe(e.type)||"Memo";case $:t=e._payload,e=e._init;try{return pe(e(t))}catch{}}return null}var Y=Array.isArray,_=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Z=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k={pending:!1,data:null,method:null,action:null},ie=[],E=-1;function G(e){return{current:e}}function F(e){0>E||(e.current=ie[E],ie[E]=null,E--)}function W(e,t){E++,ie[E]=e.current,e.current=t}var te=G(null),ge=G(null),ce=G(null),I=G(null);function fe(e,t){switch(W(ce,t),W(ge,e),W(te,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?fh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=fh(t),e=dh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(te),W(te,e)}function Oe(){F(te),F(ge),F(ce)}function Te(e){e.memoizedState!==null&&W(I,e);var t=te.current,n=dh(t,e.type);t!==n&&(W(ge,e),W(te,n))}function we(e){ge.current===e&&(F(te),F(ge)),I.current===e&&(F(I),Ni._currentValue=k)}var Ee=Object.prototype.hasOwnProperty,it=a.unstable_scheduleCallback,gt=a.unstable_cancelCallback,al=a.unstable_shouldYield,il=a.unstable_requestPaint,ut=a.unstable_now,Ro=a.unstable_getCurrentPriorityLevel,rl=a.unstable_ImmediatePriority,Qu=a.unstable_UserBlockingPriority,Vi=a.unstable_NormalPriority,Ap=a.unstable_LowPriority,Zu=a.unstable_IdlePriority,Tp=a.log,Cp=a.unstable_setDisableYieldValue,Ra=null,Et=null;function Cn(e){if(typeof Tp=="function"&&Cp(e),Et&&typeof Et.setStrictMode=="function")try{Et.setStrictMode(Ra,e)}catch{}}var Nt=Math.clz32?Math.clz32:Op,jp=Math.log,_p=Math.LN2;function Op(e){return e>>>=0,e===0?32:31-(jp(e)/_p|0)|0}var Yi=256,Gi=4194304;function ol(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Xi(e,t,n){var l=e.pendingLanes;if(l===0)return 0;var i=0,r=e.suspendedLanes,d=e.pingedLanes;e=e.warmLanes;var g=l&134217727;return g!==0?(l=g&~r,l!==0?i=ol(l):(d&=g,d!==0?i=ol(d):n||(n=g&~e,n!==0&&(i=ol(n))))):(g=l&~r,g!==0?i=ol(g):d!==0?i=ol(d):n||(n=l&~e,n!==0&&(i=ol(n)))),i===0?0:t!==0&&t!==i&&(t&r)===0&&(r=i&-i,n=t&-t,r>=n||r===32&&(n&4194048)!==0)?t:i}function Da(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Mp(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ku(){var e=Yi;return Yi<<=1,(Yi&4194048)===0&&(Yi=256),e}function Ju(){var e=Gi;return Gi<<=1,(Gi&62914560)===0&&(Gi=4194304),e}function Do(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function za(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Rp(e,t,n,l,i,r){var d=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var g=e.entanglements,w=e.expirationTimes,z=e.hiddenUpdates;for(n=d&~n;0<n;){var q=31-Nt(n),Q=1<<q;g[q]=0,w[q]=-1;var U=z[q];if(U!==null)for(z[q]=null,q=0;q<U.length;q++){var H=U[q];H!==null&&(H.lane&=-536870913)}n&=~Q}l!==0&&$u(e,l,0),r!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=r&~(d&~t))}function $u(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-Nt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|n&4194090}function Wu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var l=31-Nt(n),i=1<<l;i&t|e[l]&t&&(e[l]|=t),n&=~i}}function zo(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Uo(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Pu(){var e=Z.p;return e!==0?e:(e=window.event,e===void 0?32:Mh(e.type))}function Dp(e,t){var n=Z.p;try{return Z.p=e,t()}finally{Z.p=n}}var jn=Math.random().toString(36).slice(2),ft="__reactFiber$"+jn,pt="__reactProps$"+jn,Ml="__reactContainer$"+jn,Ho="__reactEvents$"+jn,zp="__reactListeners$"+jn,Up="__reactHandles$"+jn,Fu="__reactResources$"+jn,Ua="__reactMarker$"+jn;function Lo(e){delete e[ft],delete e[pt],delete e[Ho],delete e[zp],delete e[Up]}function Rl(e){var t=e[ft];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ml]||n[ft]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=gh(e);e!==null;){if(n=e[ft])return n;e=gh(e)}return t}e=n,n=e.parentNode}return null}function Dl(e){if(e=e[ft]||e[Ml]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ha(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function zl(e){var t=e[Fu];return t||(t=e[Fu]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function tt(e){e[Ua]=!0}var Iu=new Set,ef={};function cl(e,t){Ul(e,t),Ul(e+"Capture",t)}function Ul(e,t){for(ef[e]=t,e=0;e<t.length;e++)Iu.add(t[e])}var Hp=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),tf={},nf={};function Lp(e){return Ee.call(nf,e)?!0:Ee.call(tf,e)?!1:Hp.test(e)?nf[e]=!0:(tf[e]=!0,!1)}function Qi(e,t,n){if(Lp(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Zi(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function on(e,t,n,l){if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+l)}}var Bo,lf;function Hl(e){if(Bo===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Bo=t&&t[1]||"",lf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Bo+e+lf}var ko=!1;function qo(e,t){if(!e||ko)return"";ko=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(H){var U=H}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(H){U=H}e.call(Q.prototype)}}else{try{throw Error()}catch(H){U=H}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(H){if(H&&U&&typeof H.stack=="string")return[H.stack,U.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),d=r[0],g=r[1];if(d&&g){var w=d.split(`
`),z=g.split(`
`);for(i=l=0;l<w.length&&!w[l].includes("DetermineComponentFrameRoot");)l++;for(;i<z.length&&!z[i].includes("DetermineComponentFrameRoot");)i++;if(l===w.length||i===z.length)for(l=w.length-1,i=z.length-1;1<=l&&0<=i&&w[l]!==z[i];)i--;for(;1<=l&&0<=i;l--,i--)if(w[l]!==z[i]){if(l!==1||i!==1)do if(l--,i--,0>i||w[l]!==z[i]){var q=`
`+w[l].replace(" at new "," at ");return e.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",e.displayName)),q}while(1<=l&&0<=i);break}}}finally{ko=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Hl(n):""}function Bp(e){switch(e.tag){case 26:case 27:case 5:return Hl(e.type);case 16:return Hl("Lazy");case 13:return Hl("Suspense");case 19:return Hl("SuspenseList");case 0:case 15:return qo(e.type,!1);case 11:return qo(e.type.render,!1);case 1:return qo(e.type,!0);case 31:return Hl("Activity");default:return""}}function af(e){try{var t="";do t+=Bp(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function zt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function rf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function kp(e){var t=rf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,r=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(d){l=""+d,r.call(this,d)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(d){l=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ki(e){e._valueTracker||(e._valueTracker=kp(e))}function of(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),l="";return e&&(l=rf(e)?e.checked?"true":"false":e.value),e=l,e!==n?(t.setValue(e),!0):!1}function Ji(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var qp=/[\n"\\]/g;function Ut(e){return e.replace(qp,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Vo(e,t,n,l,i,r,d,g){e.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.type=d:e.removeAttribute("type"),t!=null?d==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+zt(t)):e.value!==""+zt(t)&&(e.value=""+zt(t)):d!=="submit"&&d!=="reset"||e.removeAttribute("value"),t!=null?Yo(e,d,zt(t)):n!=null?Yo(e,d,zt(n)):l!=null&&e.removeAttribute("value"),i==null&&r!=null&&(e.defaultChecked=!!r),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.name=""+zt(g):e.removeAttribute("name")}function cf(e,t,n,l,i,r,d,g){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.type=r),t!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||t!=null))return;n=n!=null?""+zt(n):"",t=t!=null?""+zt(t):n,g||t===e.value||(e.value=t),e.defaultValue=t}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=g?e.checked:!!l,e.defaultChecked=!!l,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.name=d)}function Yo(e,t,n){t==="number"&&Ji(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Ll(e,t,n,l){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&l&&(e[n].defaultSelected=!0)}else{for(n=""+zt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,l&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function sf(e,t,n){if(t!=null&&(t=""+zt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+zt(n):""}function uf(e,t,n,l){if(t==null){if(l!=null){if(n!=null)throw Error(c(92));if(Y(l)){if(1<l.length)throw Error(c(93));l=l[0]}n=l}n==null&&(n=""),t=n}n=zt(t),e.defaultValue=n,l=e.textContent,l===n&&l!==""&&l!==null&&(e.value=l)}function Bl(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Vp=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ff(e,t,n){var l=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,n):typeof n!="number"||n===0||Vp.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function df(e,t,n){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var i in t)l=t[i],t.hasOwnProperty(i)&&n[i]!==l&&ff(e,i,l)}else for(var r in t)t.hasOwnProperty(r)&&ff(e,r,t[r])}function Go(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Yp=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Gp=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function $i(e){return Gp.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Xo=null;function Qo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var kl=null,ql=null;function mf(e){var t=Dl(e);if(t&&(e=t.stateNode)){var n=e[pt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Vo(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ut(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var l=n[t];if(l!==e&&l.form===e.form){var i=l[pt]||null;if(!i)throw Error(c(90));Vo(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)l=n[t],l.form===e.form&&of(l)}break e;case"textarea":sf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Ll(e,!!n.multiple,t,!1)}}}var Zo=!1;function hf(e,t,n){if(Zo)return e(t,n);Zo=!0;try{var l=e(t);return l}finally{if(Zo=!1,(kl!==null||ql!==null)&&(zr(),kl&&(t=kl,e=ql,ql=kl=null,mf(t),e)))for(t=0;t<e.length;t++)mf(e[t])}}function La(e,t){var n=e.stateNode;if(n===null)return null;var l=n[pt]||null;if(l===null)return null;n=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(c(231,t,typeof n));return n}var cn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ko=!1;if(cn)try{var Ba={};Object.defineProperty(Ba,"passive",{get:function(){Ko=!0}}),window.addEventListener("test",Ba,Ba),window.removeEventListener("test",Ba,Ba)}catch{Ko=!1}var _n=null,Jo=null,Wi=null;function vf(){if(Wi)return Wi;var e,t=Jo,n=t.length,l,i="value"in _n?_n.value:_n.textContent,r=i.length;for(e=0;e<n&&t[e]===i[e];e++);var d=n-e;for(l=1;l<=d&&t[n-l]===i[r-l];l++);return Wi=i.slice(e,1<l?1-l:void 0)}function Pi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Fi(){return!0}function gf(){return!1}function yt(e){function t(n,l,i,r,d){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=r,this.target=d,this.currentTarget=null;for(var g in e)e.hasOwnProperty(g)&&(n=e[g],this[g]=n?n(r):r[g]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Fi:gf,this.isPropagationStopped=gf,this}return S(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Fi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Fi)},persist:function(){},isPersistent:Fi}),t}var sl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ii=yt(sl),ka=S({},sl,{view:0,detail:0}),Xp=yt(ka),$o,Wo,qa,er=S({},ka,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Fo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==qa&&(qa&&e.type==="mousemove"?($o=e.screenX-qa.screenX,Wo=e.screenY-qa.screenY):Wo=$o=0,qa=e),$o)},movementY:function(e){return"movementY"in e?e.movementY:Wo}}),pf=yt(er),Qp=S({},er,{dataTransfer:0}),Zp=yt(Qp),Kp=S({},ka,{relatedTarget:0}),Po=yt(Kp),Jp=S({},sl,{animationName:0,elapsedTime:0,pseudoElement:0}),$p=yt(Jp),Wp=S({},sl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Pp=yt(Wp),Fp=S({},sl,{data:0}),yf=yt(Fp),Ip={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},e0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},t0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function n0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=t0[e])?!!t[e]:!1}function Fo(){return n0}var l0=S({},ka,{key:function(e){if(e.key){var t=Ip[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Pi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?e0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Fo,charCode:function(e){return e.type==="keypress"?Pi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Pi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),a0=yt(l0),i0=S({},er,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),xf=yt(i0),r0=S({},ka,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Fo}),o0=yt(r0),c0=S({},sl,{propertyName:0,elapsedTime:0,pseudoElement:0}),s0=yt(c0),u0=S({},er,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),f0=yt(u0),d0=S({},sl,{newState:0,oldState:0}),m0=yt(d0),h0=[9,13,27,32],Io=cn&&"CompositionEvent"in window,Va=null;cn&&"documentMode"in document&&(Va=document.documentMode);var v0=cn&&"TextEvent"in window&&!Va,bf=cn&&(!Io||Va&&8<Va&&11>=Va),Sf=" ",wf=!1;function Ef(e,t){switch(e){case"keyup":return h0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Nf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Vl=!1;function g0(e,t){switch(e){case"compositionend":return Nf(t);case"keypress":return t.which!==32?null:(wf=!0,Sf);case"textInput":return e=t.data,e===Sf&&wf?null:e;default:return null}}function p0(e,t){if(Vl)return e==="compositionend"||!Io&&Ef(e,t)?(e=vf(),Wi=Jo=_n=null,Vl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return bf&&t.locale!=="ko"?null:t.data;default:return null}}var y0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Af(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!y0[e.type]:t==="textarea"}function Tf(e,t,n,l){kl?ql?ql.push(l):ql=[l]:kl=l,t=qr(t,"onChange"),0<t.length&&(n=new Ii("onChange","change",null,n,l),e.push({event:n,listeners:t}))}var Ya=null,Ga=null;function x0(e){rh(e,0)}function tr(e){var t=Ha(e);if(of(t))return e}function Cf(e,t){if(e==="change")return t}var jf=!1;if(cn){var ec;if(cn){var tc="oninput"in document;if(!tc){var _f=document.createElement("div");_f.setAttribute("oninput","return;"),tc=typeof _f.oninput=="function"}ec=tc}else ec=!1;jf=ec&&(!document.documentMode||9<document.documentMode)}function Of(){Ya&&(Ya.detachEvent("onpropertychange",Mf),Ga=Ya=null)}function Mf(e){if(e.propertyName==="value"&&tr(Ga)){var t=[];Tf(t,Ga,e,Qo(e)),hf(x0,t)}}function b0(e,t,n){e==="focusin"?(Of(),Ya=t,Ga=n,Ya.attachEvent("onpropertychange",Mf)):e==="focusout"&&Of()}function S0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return tr(Ga)}function w0(e,t){if(e==="click")return tr(t)}function E0(e,t){if(e==="input"||e==="change")return tr(t)}function N0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var At=typeof Object.is=="function"?Object.is:N0;function Xa(e,t){if(At(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!Ee.call(t,i)||!At(e[i],t[i]))return!1}return!0}function Rf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Df(e,t){var n=Rf(e);e=0;for(var l;n;){if(n.nodeType===3){if(l=e+n.textContent.length,e<=t&&l>=t)return{node:n,offset:t-e};e=l}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Rf(n)}}function zf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?zf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Uf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Ji(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ji(e.document)}return t}function nc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var A0=cn&&"documentMode"in document&&11>=document.documentMode,Yl=null,lc=null,Qa=null,ac=!1;function Hf(e,t,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ac||Yl==null||Yl!==Ji(l)||(l=Yl,"selectionStart"in l&&nc(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Qa&&Xa(Qa,l)||(Qa=l,l=qr(lc,"onSelect"),0<l.length&&(t=new Ii("onSelect","select",null,t,n),e.push({event:t,listeners:l}),t.target=Yl)))}function ul(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Gl={animationend:ul("Animation","AnimationEnd"),animationiteration:ul("Animation","AnimationIteration"),animationstart:ul("Animation","AnimationStart"),transitionrun:ul("Transition","TransitionRun"),transitionstart:ul("Transition","TransitionStart"),transitioncancel:ul("Transition","TransitionCancel"),transitionend:ul("Transition","TransitionEnd")},ic={},Lf={};cn&&(Lf=document.createElement("div").style,"AnimationEvent"in window||(delete Gl.animationend.animation,delete Gl.animationiteration.animation,delete Gl.animationstart.animation),"TransitionEvent"in window||delete Gl.transitionend.transition);function fl(e){if(ic[e])return ic[e];if(!Gl[e])return e;var t=Gl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Lf)return ic[e]=t[n];return e}var Bf=fl("animationend"),kf=fl("animationiteration"),qf=fl("animationstart"),T0=fl("transitionrun"),C0=fl("transitionstart"),j0=fl("transitioncancel"),Vf=fl("transitionend"),Yf=new Map,rc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");rc.push("scrollEnd");function Gt(e,t){Yf.set(e,t),cl(t,[e])}var Gf=new WeakMap;function Ht(e,t){if(typeof e=="object"&&e!==null){var n=Gf.get(e);return n!==void 0?n:(t={value:e,source:t,stack:af(t)},Gf.set(e,t),t)}return{value:e,source:t,stack:af(t)}}var Lt=[],Xl=0,oc=0;function nr(){for(var e=Xl,t=oc=Xl=0;t<e;){var n=Lt[t];Lt[t++]=null;var l=Lt[t];Lt[t++]=null;var i=Lt[t];Lt[t++]=null;var r=Lt[t];if(Lt[t++]=null,l!==null&&i!==null){var d=l.pending;d===null?i.next=i:(i.next=d.next,d.next=i),l.pending=i}r!==0&&Xf(n,i,r)}}function lr(e,t,n,l){Lt[Xl++]=e,Lt[Xl++]=t,Lt[Xl++]=n,Lt[Xl++]=l,oc|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function cc(e,t,n,l){return lr(e,t,n,l),ar(e)}function Ql(e,t){return lr(e,null,null,t),ar(e)}function Xf(e,t,n){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n);for(var i=!1,r=e.return;r!==null;)r.childLanes|=n,l=r.alternate,l!==null&&(l.childLanes|=n),r.tag===22&&(e=r.stateNode,e===null||e._visibility&1||(i=!0)),e=r,r=r.return;return e.tag===3?(r=e.stateNode,i&&t!==null&&(i=31-Nt(n),e=r.hiddenUpdates,l=e[i],l===null?e[i]=[t]:l.push(t),t.lane=n|536870912),r):null}function ar(e){if(50<gi)throw gi=0,hs=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Zl={};function _0(e,t,n,l){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Tt(e,t,n,l){return new _0(e,t,n,l)}function sc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function sn(e,t){var n=e.alternate;return n===null?(n=Tt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Qf(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ir(e,t,n,l,i,r){var d=0;if(l=e,typeof e=="function")sc(e)&&(d=1);else if(typeof e=="string")d=My(e,n,te.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case se:return e=Tt(31,n,t,i),e.elementType=se,e.lanes=r,e;case R:return dl(n.children,i,r,t);case b:d=8,i|=24;break;case T:return e=Tt(12,n,t,i|2),e.elementType=T,e.lanes=r,e;case V:return e=Tt(13,n,t,i),e.elementType=V,e.lanes=r,e;case ee:return e=Tt(19,n,t,i),e.elementType=ee,e.lanes=r,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case L:case B:d=10;break e;case D:d=9;break e;case K:d=11;break e;case P:d=14;break e;case $:d=16,l=null;break e}d=29,n=Error(c(130,e===null?"null":typeof e,"")),l=null}return t=Tt(d,n,t,i),t.elementType=e,t.type=l,t.lanes=r,t}function dl(e,t,n,l){return e=Tt(7,e,l,t),e.lanes=n,e}function uc(e,t,n){return e=Tt(6,e,null,t),e.lanes=n,e}function fc(e,t,n){return t=Tt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Kl=[],Jl=0,rr=null,or=0,Bt=[],kt=0,ml=null,un=1,fn="";function hl(e,t){Kl[Jl++]=or,Kl[Jl++]=rr,rr=e,or=t}function Zf(e,t,n){Bt[kt++]=un,Bt[kt++]=fn,Bt[kt++]=ml,ml=e;var l=un;e=fn;var i=32-Nt(l)-1;l&=~(1<<i),n+=1;var r=32-Nt(t)+i;if(30<r){var d=i-i%5;r=(l&(1<<d)-1).toString(32),l>>=d,i-=d,un=1<<32-Nt(t)+i|n<<i|l,fn=r+e}else un=1<<r|n<<i|l,fn=e}function dc(e){e.return!==null&&(hl(e,1),Zf(e,1,0))}function mc(e){for(;e===rr;)rr=Kl[--Jl],Kl[Jl]=null,or=Kl[--Jl],Kl[Jl]=null;for(;e===ml;)ml=Bt[--kt],Bt[kt]=null,fn=Bt[--kt],Bt[kt]=null,un=Bt[--kt],Bt[kt]=null}var vt=null,Ge=null,_e=!1,vl=null,Pt=!1,hc=Error(c(519));function gl(e){var t=Error(c(418,""));throw Ja(Ht(t,e)),hc}function Kf(e){var t=e.stateNode,n=e.type,l=e.memoizedProps;switch(t[ft]=e,t[pt]=l,n){case"dialog":Ae("cancel",t),Ae("close",t);break;case"iframe":case"object":case"embed":Ae("load",t);break;case"video":case"audio":for(n=0;n<yi.length;n++)Ae(yi[n],t);break;case"source":Ae("error",t);break;case"img":case"image":case"link":Ae("error",t),Ae("load",t);break;case"details":Ae("toggle",t);break;case"input":Ae("invalid",t),cf(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Ki(t);break;case"select":Ae("invalid",t);break;case"textarea":Ae("invalid",t),uf(t,l.value,l.defaultValue,l.children),Ki(t)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||l.suppressHydrationWarning===!0||uh(t.textContent,n)?(l.popover!=null&&(Ae("beforetoggle",t),Ae("toggle",t)),l.onScroll!=null&&Ae("scroll",t),l.onScrollEnd!=null&&Ae("scrollend",t),l.onClick!=null&&(t.onclick=Vr),t=!0):t=!1,t||gl(e)}function Jf(e){for(vt=e.return;vt;)switch(vt.tag){case 5:case 13:Pt=!1;return;case 27:case 3:Pt=!0;return;default:vt=vt.return}}function Za(e){if(e!==vt)return!1;if(!_e)return Jf(e),_e=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Os(e.type,e.memoizedProps)),n=!n),n&&Ge&&gl(e),Jf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Ge=Qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Ge=null}}else t===27?(t=Ge,Qn(e.type)?(e=zs,zs=null,Ge=e):Ge=t):Ge=vt?Qt(e.stateNode.nextSibling):null;return!0}function Ka(){Ge=vt=null,_e=!1}function $f(){var e=vl;return e!==null&&(St===null?St=e:St.push.apply(St,e),vl=null),e}function Ja(e){vl===null?vl=[e]:vl.push(e)}var vc=G(null),pl=null,dn=null;function On(e,t,n){W(vc,t._currentValue),t._currentValue=n}function mn(e){e._currentValue=vc.current,F(vc)}function gc(e,t,n){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===n)break;e=e.return}}function pc(e,t,n,l){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var r=i.dependencies;if(r!==null){var d=i.child;r=r.firstContext;e:for(;r!==null;){var g=r;r=i;for(var w=0;w<t.length;w++)if(g.context===t[w]){r.lanes|=n,g=r.alternate,g!==null&&(g.lanes|=n),gc(r.return,n,e),l||(d=null);break e}r=g.next}}else if(i.tag===18){if(d=i.return,d===null)throw Error(c(341));d.lanes|=n,r=d.alternate,r!==null&&(r.lanes|=n),gc(d,n,e),d=null}else d=i.child;if(d!==null)d.return=i;else for(d=i;d!==null;){if(d===e){d=null;break}if(i=d.sibling,i!==null){i.return=d.return,d=i;break}d=d.return}i=d}}function $a(e,t,n,l){e=null;for(var i=t,r=!1;i!==null;){if(!r){if((i.flags&524288)!==0)r=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var d=i.alternate;if(d===null)throw Error(c(387));if(d=d.memoizedProps,d!==null){var g=i.type;At(i.pendingProps.value,d.value)||(e!==null?e.push(g):e=[g])}}else if(i===I.current){if(d=i.alternate,d===null)throw Error(c(387));d.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(Ni):e=[Ni])}i=i.return}e!==null&&pc(t,e,n,l),t.flags|=262144}function cr(e){for(e=e.firstContext;e!==null;){if(!At(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function yl(e){pl=e,dn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function dt(e){return Wf(pl,e)}function sr(e,t){return pl===null&&yl(e),Wf(e,t)}function Wf(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},dn===null){if(e===null)throw Error(c(308));dn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else dn=dn.next=t;return n}var O0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},M0=a.unstable_scheduleCallback,R0=a.unstable_NormalPriority,Fe={$$typeof:B,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function yc(){return{controller:new O0,data:new Map,refCount:0}}function Wa(e){e.refCount--,e.refCount===0&&M0(R0,function(){e.controller.abort()})}var Pa=null,xc=0,$l=0,Wl=null;function D0(e,t){if(Pa===null){var n=Pa=[];xc=0,$l=Ss(),Wl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return xc++,t.then(Pf,Pf),t}function Pf(){if(--xc===0&&Pa!==null){Wl!==null&&(Wl.status="fulfilled");var e=Pa;Pa=null,$l=0,Wl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function z0(e,t){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var i=0;i<n.length;i++)(0,n[i])(t)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var Ff=_.S;_.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&D0(e,t),Ff!==null&&Ff(e,t)};var xl=G(null);function bc(){var e=xl.current;return e!==null?e:Be.pooledCache}function ur(e,t){t===null?W(xl,xl.current):W(xl,t.pool)}function If(){var e=bc();return e===null?null:{parent:Fe._currentValue,pool:e}}var Fa=Error(c(460)),ed=Error(c(474)),fr=Error(c(542)),Sc={then:function(){}};function td(e){return e=e.status,e==="fulfilled"||e==="rejected"}function dr(){}function nd(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(dr,dr),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,ad(e),e;default:if(typeof t.status=="string")t.then(dr,dr);else{if(e=Be,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=l}},function(l){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,ad(e),e}throw Ia=t,Fa}}var Ia=null;function ld(){if(Ia===null)throw Error(c(459));var e=Ia;return Ia=null,e}function ad(e){if(e===Fa||e===fr)throw Error(c(483))}var Mn=!1;function wc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Ec(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Rn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Dn(e,t,n){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Me&2)!==0){var i=l.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),l.pending=t,t=ar(e),Xf(e,null,n),t}return lr(e,l,t,n),ar(e)}function ei(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,Wu(e,n)}}function Nc(e,t){var n=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var d={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?i=r=d:r=r.next=d,n=n.next}while(n!==null);r===null?i=r=t:r=r.next=t}else i=r=t;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Ac=!1;function ti(){if(Ac){var e=Wl;if(e!==null)throw e}}function ni(e,t,n,l){Ac=!1;var i=e.updateQueue;Mn=!1;var r=i.firstBaseUpdate,d=i.lastBaseUpdate,g=i.shared.pending;if(g!==null){i.shared.pending=null;var w=g,z=w.next;w.next=null,d===null?r=z:d.next=z,d=w;var q=e.alternate;q!==null&&(q=q.updateQueue,g=q.lastBaseUpdate,g!==d&&(g===null?q.firstBaseUpdate=z:g.next=z,q.lastBaseUpdate=w))}if(r!==null){var Q=i.baseState;d=0,q=z=w=null,g=r;do{var U=g.lane&-536870913,H=U!==g.lane;if(H?(Ce&U)===U:(l&U)===U){U!==0&&U===$l&&(Ac=!0),q!==null&&(q=q.next={lane:0,tag:g.tag,payload:g.payload,callback:null,next:null});e:{var he=e,de=g;U=t;var Ue=n;switch(de.tag){case 1:if(he=de.payload,typeof he=="function"){Q=he.call(Ue,Q,U);break e}Q=he;break e;case 3:he.flags=he.flags&-65537|128;case 0:if(he=de.payload,U=typeof he=="function"?he.call(Ue,Q,U):he,U==null)break e;Q=S({},Q,U);break e;case 2:Mn=!0}}U=g.callback,U!==null&&(e.flags|=64,H&&(e.flags|=8192),H=i.callbacks,H===null?i.callbacks=[U]:H.push(U))}else H={lane:U,tag:g.tag,payload:g.payload,callback:g.callback,next:null},q===null?(z=q=H,w=Q):q=q.next=H,d|=U;if(g=g.next,g===null){if(g=i.shared.pending,g===null)break;H=g,g=H.next,H.next=null,i.lastBaseUpdate=H,i.shared.pending=null}}while(!0);q===null&&(w=Q),i.baseState=w,i.firstBaseUpdate=z,i.lastBaseUpdate=q,r===null&&(i.shared.lanes=0),Vn|=d,e.lanes=d,e.memoizedState=Q}}function id(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function rd(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)id(n[e],t)}var Pl=G(null),mr=G(0);function od(e,t){e=bn,W(mr,e),W(Pl,t),bn=e|t.baseLanes}function Tc(){W(mr,bn),W(Pl,Pl.current)}function Cc(){bn=mr.current,F(Pl),F(mr)}var zn=0,xe=null,De=null,$e=null,hr=!1,Fl=!1,bl=!1,vr=0,li=0,Il=null,U0=0;function Ze(){throw Error(c(321))}function jc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!At(e[n],t[n]))return!1;return!0}function _c(e,t,n,l,i,r){return zn=r,xe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,_.H=e===null||e.memoizedState===null?Xd:Qd,bl=!1,r=n(l,i),bl=!1,Fl&&(r=sd(t,n,l,i)),cd(e),r}function cd(e){_.H=Sr;var t=De!==null&&De.next!==null;if(zn=0,$e=De=xe=null,hr=!1,li=0,Il=null,t)throw Error(c(300));e===null||nt||(e=e.dependencies,e!==null&&cr(e)&&(nt=!0))}function sd(e,t,n,l){xe=e;var i=0;do{if(Fl&&(Il=null),li=0,Fl=!1,25<=i)throw Error(c(301));if(i+=1,$e=De=null,e.updateQueue!=null){var r=e.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}_.H=Y0,r=t(n,l)}while(Fl);return r}function H0(){var e=_.H,t=e.useState()[0];return t=typeof t.then=="function"?ai(t):t,e=e.useState()[0],(De!==null?De.memoizedState:null)!==e&&(xe.flags|=1024),t}function Oc(){var e=vr!==0;return vr=0,e}function Mc(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Rc(e){if(hr){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}hr=!1}zn=0,$e=De=xe=null,Fl=!1,li=vr=0,Il=null}function xt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return $e===null?xe.memoizedState=$e=e:$e=$e.next=e,$e}function We(){if(De===null){var e=xe.alternate;e=e!==null?e.memoizedState:null}else e=De.next;var t=$e===null?xe.memoizedState:$e.next;if(t!==null)$e=t,De=e;else{if(e===null)throw xe.alternate===null?Error(c(467)):Error(c(310));De=e,e={memoizedState:De.memoizedState,baseState:De.baseState,baseQueue:De.baseQueue,queue:De.queue,next:null},$e===null?xe.memoizedState=$e=e:$e=$e.next=e}return $e}function Dc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ai(e){var t=li;return li+=1,Il===null&&(Il=[]),e=nd(Il,e,t),t=xe,($e===null?t.memoizedState:$e.next)===null&&(t=t.alternate,_.H=t===null||t.memoizedState===null?Xd:Qd),e}function gr(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ai(e);if(e.$$typeof===B)return dt(e)}throw Error(c(438,String(e)))}function zc(e){var t=null,n=xe.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var l=xe.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Dc(),xe.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),l=0;l<e;l++)n[l]=J;return t.index++,n}function hn(e,t){return typeof t=="function"?t(e):t}function pr(e){var t=We();return Uc(t,De,e)}function Uc(e,t,n){var l=e.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=n;var i=e.baseQueue,r=l.pending;if(r!==null){if(i!==null){var d=i.next;i.next=r.next,r.next=d}t.baseQueue=i=r,l.pending=null}if(r=e.baseState,i===null)e.memoizedState=r;else{t=i.next;var g=d=null,w=null,z=t,q=!1;do{var Q=z.lane&-536870913;if(Q!==z.lane?(Ce&Q)===Q:(zn&Q)===Q){var U=z.revertLane;if(U===0)w!==null&&(w=w.next={lane:0,revertLane:0,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),Q===$l&&(q=!0);else if((zn&U)===U){z=z.next,U===$l&&(q=!0);continue}else Q={lane:0,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},w===null?(g=w=Q,d=r):w=w.next=Q,xe.lanes|=U,Vn|=U;Q=z.action,bl&&n(r,Q),r=z.hasEagerState?z.eagerState:n(r,Q)}else U={lane:Q,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},w===null?(g=w=U,d=r):w=w.next=U,xe.lanes|=Q,Vn|=Q;z=z.next}while(z!==null&&z!==t);if(w===null?d=r:w.next=g,!At(r,e.memoizedState)&&(nt=!0,q&&(n=Wl,n!==null)))throw n;e.memoizedState=r,e.baseState=d,e.baseQueue=w,l.lastRenderedState=r}return i===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Hc(e){var t=We(),n=t.queue;if(n===null)throw Error(c(311));n.lastRenderedReducer=e;var l=n.dispatch,i=n.pending,r=t.memoizedState;if(i!==null){n.pending=null;var d=i=i.next;do r=e(r,d.action),d=d.next;while(d!==i);At(r,t.memoizedState)||(nt=!0),t.memoizedState=r,t.baseQueue===null&&(t.baseState=r),n.lastRenderedState=r}return[r,l]}function ud(e,t,n){var l=xe,i=We(),r=_e;if(r){if(n===void 0)throw Error(c(407));n=n()}else n=t();var d=!At((De||i).memoizedState,n);d&&(i.memoizedState=n,nt=!0),i=i.queue;var g=md.bind(null,l,i,e);if(ii(2048,8,g,[e]),i.getSnapshot!==t||d||$e!==null&&$e.memoizedState.tag&1){if(l.flags|=2048,ea(9,yr(),dd.bind(null,l,i,n,t),null),Be===null)throw Error(c(349));r||(zn&124)!==0||fd(l,t,n)}return n}function fd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=xe.updateQueue,t===null?(t=Dc(),xe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function dd(e,t,n,l){t.value=n,t.getSnapshot=l,hd(t)&&vd(e)}function md(e,t,n){return n(function(){hd(t)&&vd(e)})}function hd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!At(e,n)}catch{return!0}}function vd(e){var t=Ql(e,2);t!==null&&Mt(t,e,2)}function Lc(e){var t=xt();if(typeof e=="function"){var n=e;if(e=n(),bl){Cn(!0);try{n()}finally{Cn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:hn,lastRenderedState:e},t}function gd(e,t,n,l){return e.baseState=n,Uc(e,De,typeof l=="function"?l:hn)}function L0(e,t,n,l,i){if(br(e))throw Error(c(485));if(e=t.action,e!==null){var r={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){r.listeners.push(d)}};_.T!==null?n(!0):r.isTransition=!1,l(r),n=t.pending,n===null?(r.next=t.pending=r,pd(t,r)):(r.next=n.next,t.pending=n.next=r)}}function pd(e,t){var n=t.action,l=t.payload,i=e.state;if(t.isTransition){var r=_.T,d={};_.T=d;try{var g=n(i,l),w=_.S;w!==null&&w(d,g),yd(e,t,g)}catch(z){Bc(e,t,z)}finally{_.T=r}}else try{r=n(i,l),yd(e,t,r)}catch(z){Bc(e,t,z)}}function yd(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){xd(e,t,l)},function(l){return Bc(e,t,l)}):xd(e,t,n)}function xd(e,t,n){t.status="fulfilled",t.value=n,bd(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,pd(e,n)))}function Bc(e,t,n){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=n,bd(t),t=t.next;while(t!==l)}e.action=null}function bd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Sd(e,t){return t}function wd(e,t){if(_e){var n=Be.formState;if(n!==null){e:{var l=xe;if(_e){if(Ge){t:{for(var i=Ge,r=Pt;i.nodeType!==8;){if(!r){i=null;break t}if(i=Qt(i.nextSibling),i===null){i=null;break t}}r=i.data,i=r==="F!"||r==="F"?i:null}if(i){Ge=Qt(i.nextSibling),l=i.data==="F!";break e}}gl(l)}l=!1}l&&(t=n[0])}}return n=xt(),n.memoizedState=n.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Sd,lastRenderedState:t},n.queue=l,n=Vd.bind(null,xe,l),l.dispatch=n,l=Lc(!1),r=Gc.bind(null,xe,!1,l.queue),l=xt(),i={state:t,dispatch:null,action:e,pending:null},l.queue=i,n=L0.bind(null,xe,i,r,n),i.dispatch=n,l.memoizedState=e,[t,n,!1]}function Ed(e){var t=We();return Nd(t,De,e)}function Nd(e,t,n){if(t=Uc(e,t,Sd)[0],e=pr(hn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=ai(t)}catch(d){throw d===Fa?fr:d}else l=t;t=We();var i=t.queue,r=i.dispatch;return n!==t.memoizedState&&(xe.flags|=2048,ea(9,yr(),B0.bind(null,i,n),null)),[l,r,e]}function B0(e,t){e.action=t}function Ad(e){var t=We(),n=De;if(n!==null)return Nd(t,n,e);We(),t=t.memoizedState,n=We();var l=n.queue.dispatch;return n.memoizedState=e,[t,l,!1]}function ea(e,t,n,l){return e={tag:e,create:n,deps:l,inst:t,next:null},t=xe.updateQueue,t===null&&(t=Dc(),xe.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(l=n.next,n.next=e,e.next=l,t.lastEffect=e),e}function yr(){return{destroy:void 0,resource:void 0}}function Td(){return We().memoizedState}function xr(e,t,n,l){var i=xt();l=l===void 0?null:l,xe.flags|=e,i.memoizedState=ea(1|t,yr(),n,l)}function ii(e,t,n,l){var i=We();l=l===void 0?null:l;var r=i.memoizedState.inst;De!==null&&l!==null&&jc(l,De.memoizedState.deps)?i.memoizedState=ea(t,r,n,l):(xe.flags|=e,i.memoizedState=ea(1|t,r,n,l))}function Cd(e,t){xr(8390656,8,e,t)}function jd(e,t){ii(2048,8,e,t)}function _d(e,t){return ii(4,2,e,t)}function Od(e,t){return ii(4,4,e,t)}function Md(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Rd(e,t,n){n=n!=null?n.concat([e]):null,ii(4,4,Md.bind(null,t,e),n)}function kc(){}function Dd(e,t){var n=We();t=t===void 0?null:t;var l=n.memoizedState;return t!==null&&jc(t,l[1])?l[0]:(n.memoizedState=[e,t],e)}function zd(e,t){var n=We();t=t===void 0?null:t;var l=n.memoizedState;if(t!==null&&jc(t,l[1]))return l[0];if(l=e(),bl){Cn(!0);try{e()}finally{Cn(!1)}}return n.memoizedState=[l,t],l}function qc(e,t,n){return n===void 0||(zn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Lm(),xe.lanes|=e,Vn|=e,n)}function Ud(e,t,n,l){return At(n,t)?n:Pl.current!==null?(e=qc(e,n,l),At(e,t)||(nt=!0),e):(zn&42)===0?(nt=!0,e.memoizedState=n):(e=Lm(),xe.lanes|=e,Vn|=e,t)}function Hd(e,t,n,l,i){var r=Z.p;Z.p=r!==0&&8>r?r:8;var d=_.T,g={};_.T=g,Gc(e,!1,t,n);try{var w=i(),z=_.S;if(z!==null&&z(g,w),w!==null&&typeof w=="object"&&typeof w.then=="function"){var q=z0(w,l);ri(e,t,q,Ot(e))}else ri(e,t,l,Ot(e))}catch(Q){ri(e,t,{then:function(){},status:"rejected",reason:Q},Ot())}finally{Z.p=r,_.T=d}}function k0(){}function Vc(e,t,n,l){if(e.tag!==5)throw Error(c(476));var i=Ld(e).queue;Hd(e,i,t,k,n===null?k0:function(){return Bd(e),n(l)})}function Ld(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:k,baseState:k,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:hn,lastRenderedState:k},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:hn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Bd(e){var t=Ld(e).next.queue;ri(e,t,{},Ot())}function Yc(){return dt(Ni)}function kd(){return We().memoizedState}function qd(){return We().memoizedState}function q0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Ot();e=Rn(n);var l=Dn(t,e,n);l!==null&&(Mt(l,t,n),ei(l,t,n)),t={cache:yc()},e.payload=t;return}t=t.return}}function V0(e,t,n){var l=Ot();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},br(e)?Yd(t,n):(n=cc(e,t,n,l),n!==null&&(Mt(n,e,l),Gd(n,t,l)))}function Vd(e,t,n){var l=Ot();ri(e,t,n,l)}function ri(e,t,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(br(e))Yd(t,i);else{var r=e.alternate;if(e.lanes===0&&(r===null||r.lanes===0)&&(r=t.lastRenderedReducer,r!==null))try{var d=t.lastRenderedState,g=r(d,n);if(i.hasEagerState=!0,i.eagerState=g,At(g,d))return lr(e,t,i,0),Be===null&&nr(),!1}catch{}finally{}if(n=cc(e,t,i,l),n!==null)return Mt(n,e,l),Gd(n,t,l),!0}return!1}function Gc(e,t,n,l){if(l={lane:2,revertLane:Ss(),action:l,hasEagerState:!1,eagerState:null,next:null},br(e)){if(t)throw Error(c(479))}else t=cc(e,n,l,2),t!==null&&Mt(t,e,2)}function br(e){var t=e.alternate;return e===xe||t!==null&&t===xe}function Yd(e,t){Fl=hr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Gd(e,t,n){if((n&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,Wu(e,n)}}var Sr={readContext:dt,use:gr,useCallback:Ze,useContext:Ze,useEffect:Ze,useImperativeHandle:Ze,useLayoutEffect:Ze,useInsertionEffect:Ze,useMemo:Ze,useReducer:Ze,useRef:Ze,useState:Ze,useDebugValue:Ze,useDeferredValue:Ze,useTransition:Ze,useSyncExternalStore:Ze,useId:Ze,useHostTransitionStatus:Ze,useFormState:Ze,useActionState:Ze,useOptimistic:Ze,useMemoCache:Ze,useCacheRefresh:Ze},Xd={readContext:dt,use:gr,useCallback:function(e,t){return xt().memoizedState=[e,t===void 0?null:t],e},useContext:dt,useEffect:Cd,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,xr(4194308,4,Md.bind(null,t,e),n)},useLayoutEffect:function(e,t){return xr(4194308,4,e,t)},useInsertionEffect:function(e,t){xr(4,2,e,t)},useMemo:function(e,t){var n=xt();t=t===void 0?null:t;var l=e();if(bl){Cn(!0);try{e()}finally{Cn(!1)}}return n.memoizedState=[l,t],l},useReducer:function(e,t,n){var l=xt();if(n!==void 0){var i=n(t);if(bl){Cn(!0);try{n(t)}finally{Cn(!1)}}}else i=t;return l.memoizedState=l.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},l.queue=e,e=e.dispatch=V0.bind(null,xe,e),[l.memoizedState,e]},useRef:function(e){var t=xt();return e={current:e},t.memoizedState=e},useState:function(e){e=Lc(e);var t=e.queue,n=Vd.bind(null,xe,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:kc,useDeferredValue:function(e,t){var n=xt();return qc(n,e,t)},useTransition:function(){var e=Lc(!1);return e=Hd.bind(null,xe,e.queue,!0,!1),xt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var l=xe,i=xt();if(_e){if(n===void 0)throw Error(c(407));n=n()}else{if(n=t(),Be===null)throw Error(c(349));(Ce&124)!==0||fd(l,t,n)}i.memoizedState=n;var r={value:n,getSnapshot:t};return i.queue=r,Cd(md.bind(null,l,r,e),[e]),l.flags|=2048,ea(9,yr(),dd.bind(null,l,r,n,t),null),n},useId:function(){var e=xt(),t=Be.identifierPrefix;if(_e){var n=fn,l=un;n=(l&~(1<<32-Nt(l)-1)).toString(32)+n,t="«"+t+"R"+n,n=vr++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=U0++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Yc,useFormState:wd,useActionState:wd,useOptimistic:function(e){var t=xt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Gc.bind(null,xe,!0,n),n.dispatch=t,[e,t]},useMemoCache:zc,useCacheRefresh:function(){return xt().memoizedState=q0.bind(null,xe)}},Qd={readContext:dt,use:gr,useCallback:Dd,useContext:dt,useEffect:jd,useImperativeHandle:Rd,useInsertionEffect:_d,useLayoutEffect:Od,useMemo:zd,useReducer:pr,useRef:Td,useState:function(){return pr(hn)},useDebugValue:kc,useDeferredValue:function(e,t){var n=We();return Ud(n,De.memoizedState,e,t)},useTransition:function(){var e=pr(hn)[0],t=We().memoizedState;return[typeof e=="boolean"?e:ai(e),t]},useSyncExternalStore:ud,useId:kd,useHostTransitionStatus:Yc,useFormState:Ed,useActionState:Ed,useOptimistic:function(e,t){var n=We();return gd(n,De,e,t)},useMemoCache:zc,useCacheRefresh:qd},Y0={readContext:dt,use:gr,useCallback:Dd,useContext:dt,useEffect:jd,useImperativeHandle:Rd,useInsertionEffect:_d,useLayoutEffect:Od,useMemo:zd,useReducer:Hc,useRef:Td,useState:function(){return Hc(hn)},useDebugValue:kc,useDeferredValue:function(e,t){var n=We();return De===null?qc(n,e,t):Ud(n,De.memoizedState,e,t)},useTransition:function(){var e=Hc(hn)[0],t=We().memoizedState;return[typeof e=="boolean"?e:ai(e),t]},useSyncExternalStore:ud,useId:kd,useHostTransitionStatus:Yc,useFormState:Ad,useActionState:Ad,useOptimistic:function(e,t){var n=We();return De!==null?gd(n,De,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:zc,useCacheRefresh:qd},ta=null,oi=0;function wr(e){var t=oi;return oi+=1,ta===null&&(ta=[]),nd(ta,e,t)}function ci(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Er(e,t){throw t.$$typeof===N?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Zd(e){var t=e._init;return t(e._payload)}function Kd(e){function t(j,A){if(e){var M=j.deletions;M===null?(j.deletions=[A],j.flags|=16):M.push(A)}}function n(j,A){if(!e)return null;for(;A!==null;)t(j,A),A=A.sibling;return null}function l(j){for(var A=new Map;j!==null;)j.key!==null?A.set(j.key,j):A.set(j.index,j),j=j.sibling;return A}function i(j,A){return j=sn(j,A),j.index=0,j.sibling=null,j}function r(j,A,M){return j.index=M,e?(M=j.alternate,M!==null?(M=M.index,M<A?(j.flags|=67108866,A):M):(j.flags|=67108866,A)):(j.flags|=1048576,A)}function d(j){return e&&j.alternate===null&&(j.flags|=67108866),j}function g(j,A,M,X){return A===null||A.tag!==6?(A=uc(M,j.mode,X),A.return=j,A):(A=i(A,M),A.return=j,A)}function w(j,A,M,X){var ne=M.type;return ne===R?q(j,A,M.props.children,X,M.key):A!==null&&(A.elementType===ne||typeof ne=="object"&&ne!==null&&ne.$$typeof===$&&Zd(ne)===A.type)?(A=i(A,M.props),ci(A,M),A.return=j,A):(A=ir(M.type,M.key,M.props,null,j.mode,X),ci(A,M),A.return=j,A)}function z(j,A,M,X){return A===null||A.tag!==4||A.stateNode.containerInfo!==M.containerInfo||A.stateNode.implementation!==M.implementation?(A=fc(M,j.mode,X),A.return=j,A):(A=i(A,M.children||[]),A.return=j,A)}function q(j,A,M,X,ne){return A===null||A.tag!==7?(A=dl(M,j.mode,X,ne),A.return=j,A):(A=i(A,M),A.return=j,A)}function Q(j,A,M){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return A=uc(""+A,j.mode,M),A.return=j,A;if(typeof A=="object"&&A!==null){switch(A.$$typeof){case C:return M=ir(A.type,A.key,A.props,null,j.mode,M),ci(M,A),M.return=j,M;case O:return A=fc(A,j.mode,M),A.return=j,A;case $:var X=A._init;return A=X(A._payload),Q(j,A,M)}if(Y(A)||oe(A))return A=dl(A,j.mode,M,null),A.return=j,A;if(typeof A.then=="function")return Q(j,wr(A),M);if(A.$$typeof===B)return Q(j,sr(j,A),M);Er(j,A)}return null}function U(j,A,M,X){var ne=A!==null?A.key:null;if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return ne!==null?null:g(j,A,""+M,X);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case C:return M.key===ne?w(j,A,M,X):null;case O:return M.key===ne?z(j,A,M,X):null;case $:return ne=M._init,M=ne(M._payload),U(j,A,M,X)}if(Y(M)||oe(M))return ne!==null?null:q(j,A,M,X,null);if(typeof M.then=="function")return U(j,A,wr(M),X);if(M.$$typeof===B)return U(j,A,sr(j,M),X);Er(j,M)}return null}function H(j,A,M,X,ne){if(typeof X=="string"&&X!==""||typeof X=="number"||typeof X=="bigint")return j=j.get(M)||null,g(A,j,""+X,ne);if(typeof X=="object"&&X!==null){switch(X.$$typeof){case C:return j=j.get(X.key===null?M:X.key)||null,w(A,j,X,ne);case O:return j=j.get(X.key===null?M:X.key)||null,z(A,j,X,ne);case $:var Se=X._init;return X=Se(X._payload),H(j,A,M,X,ne)}if(Y(X)||oe(X))return j=j.get(M)||null,q(A,j,X,ne,null);if(typeof X.then=="function")return H(j,A,M,wr(X),ne);if(X.$$typeof===B)return H(j,A,M,sr(A,X),ne);Er(A,X)}return null}function he(j,A,M,X){for(var ne=null,Se=null,re=A,me=A=0,at=null;re!==null&&me<M.length;me++){re.index>me?(at=re,re=null):at=re.sibling;var je=U(j,re,M[me],X);if(je===null){re===null&&(re=at);break}e&&re&&je.alternate===null&&t(j,re),A=r(je,A,me),Se===null?ne=je:Se.sibling=je,Se=je,re=at}if(me===M.length)return n(j,re),_e&&hl(j,me),ne;if(re===null){for(;me<M.length;me++)re=Q(j,M[me],X),re!==null&&(A=r(re,A,me),Se===null?ne=re:Se.sibling=re,Se=re);return _e&&hl(j,me),ne}for(re=l(re);me<M.length;me++)at=H(re,j,me,M[me],X),at!==null&&(e&&at.alternate!==null&&re.delete(at.key===null?me:at.key),A=r(at,A,me),Se===null?ne=at:Se.sibling=at,Se=at);return e&&re.forEach(function(Wn){return t(j,Wn)}),_e&&hl(j,me),ne}function de(j,A,M,X){if(M==null)throw Error(c(151));for(var ne=null,Se=null,re=A,me=A=0,at=null,je=M.next();re!==null&&!je.done;me++,je=M.next()){re.index>me?(at=re,re=null):at=re.sibling;var Wn=U(j,re,je.value,X);if(Wn===null){re===null&&(re=at);break}e&&re&&Wn.alternate===null&&t(j,re),A=r(Wn,A,me),Se===null?ne=Wn:Se.sibling=Wn,Se=Wn,re=at}if(je.done)return n(j,re),_e&&hl(j,me),ne;if(re===null){for(;!je.done;me++,je=M.next())je=Q(j,je.value,X),je!==null&&(A=r(je,A,me),Se===null?ne=je:Se.sibling=je,Se=je);return _e&&hl(j,me),ne}for(re=l(re);!je.done;me++,je=M.next())je=H(re,j,me,je.value,X),je!==null&&(e&&je.alternate!==null&&re.delete(je.key===null?me:je.key),A=r(je,A,me),Se===null?ne=je:Se.sibling=je,Se=je);return e&&re.forEach(function(Gy){return t(j,Gy)}),_e&&hl(j,me),ne}function Ue(j,A,M,X){if(typeof M=="object"&&M!==null&&M.type===R&&M.key===null&&(M=M.props.children),typeof M=="object"&&M!==null){switch(M.$$typeof){case C:e:{for(var ne=M.key;A!==null;){if(A.key===ne){if(ne=M.type,ne===R){if(A.tag===7){n(j,A.sibling),X=i(A,M.props.children),X.return=j,j=X;break e}}else if(A.elementType===ne||typeof ne=="object"&&ne!==null&&ne.$$typeof===$&&Zd(ne)===A.type){n(j,A.sibling),X=i(A,M.props),ci(X,M),X.return=j,j=X;break e}n(j,A);break}else t(j,A);A=A.sibling}M.type===R?(X=dl(M.props.children,j.mode,X,M.key),X.return=j,j=X):(X=ir(M.type,M.key,M.props,null,j.mode,X),ci(X,M),X.return=j,j=X)}return d(j);case O:e:{for(ne=M.key;A!==null;){if(A.key===ne)if(A.tag===4&&A.stateNode.containerInfo===M.containerInfo&&A.stateNode.implementation===M.implementation){n(j,A.sibling),X=i(A,M.children||[]),X.return=j,j=X;break e}else{n(j,A);break}else t(j,A);A=A.sibling}X=fc(M,j.mode,X),X.return=j,j=X}return d(j);case $:return ne=M._init,M=ne(M._payload),Ue(j,A,M,X)}if(Y(M))return he(j,A,M,X);if(oe(M)){if(ne=oe(M),typeof ne!="function")throw Error(c(150));return M=ne.call(M),de(j,A,M,X)}if(typeof M.then=="function")return Ue(j,A,wr(M),X);if(M.$$typeof===B)return Ue(j,A,sr(j,M),X);Er(j,M)}return typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint"?(M=""+M,A!==null&&A.tag===6?(n(j,A.sibling),X=i(A,M),X.return=j,j=X):(n(j,A),X=uc(M,j.mode,X),X.return=j,j=X),d(j)):n(j,A)}return function(j,A,M,X){try{oi=0;var ne=Ue(j,A,M,X);return ta=null,ne}catch(re){if(re===Fa||re===fr)throw re;var Se=Tt(29,re,null,j.mode);return Se.lanes=X,Se.return=j,Se}finally{}}}var na=Kd(!0),Jd=Kd(!1),qt=G(null),Ft=null;function Un(e){var t=e.alternate;W(Ie,Ie.current&1),W(qt,e),Ft===null&&(t===null||Pl.current!==null||t.memoizedState!==null)&&(Ft=e)}function $d(e){if(e.tag===22){if(W(Ie,Ie.current),W(qt,e),Ft===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ft=e)}}else Hn()}function Hn(){W(Ie,Ie.current),W(qt,qt.current)}function vn(e){F(qt),Ft===e&&(Ft=null),F(Ie)}var Ie=G(0);function Nr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Ds(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Xc(e,t,n,l){t=e.memoizedState,n=n(l,t),n=n==null?t:S({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Qc={enqueueSetState:function(e,t,n){e=e._reactInternals;var l=Ot(),i=Rn(l);i.payload=t,n!=null&&(i.callback=n),t=Dn(e,i,l),t!==null&&(Mt(t,e,l),ei(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var l=Ot(),i=Rn(l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Dn(e,i,l),t!==null&&(Mt(t,e,l),ei(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ot(),l=Rn(n);l.tag=2,t!=null&&(l.callback=t),t=Dn(e,l,n),t!==null&&(Mt(t,e,n),ei(t,e,n))}};function Wd(e,t,n,l,i,r,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,r,d):t.prototype&&t.prototype.isPureReactComponent?!Xa(n,l)||!Xa(i,r):!0}function Pd(e,t,n,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,l),t.state!==e&&Qc.enqueueReplaceState(t,t.state,null)}function Sl(e,t){var n=t;if("ref"in t){n={};for(var l in t)l!=="ref"&&(n[l]=t[l])}if(e=e.defaultProps){n===t&&(n=S({},n));for(var i in e)n[i]===void 0&&(n[i]=e[i])}return n}var Ar=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Fd(e){Ar(e)}function Id(e){console.error(e)}function em(e){Ar(e)}function Tr(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function tm(e,t,n){try{var l=e.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function Zc(e,t,n){return n=Rn(n),n.tag=3,n.payload={element:null},n.callback=function(){Tr(e,t)},n}function nm(e){return e=Rn(e),e.tag=3,e}function lm(e,t,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var r=l.value;e.payload=function(){return i(r)},e.callback=function(){tm(t,n,l)}}var d=n.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(e.callback=function(){tm(t,n,l),typeof i!="function"&&(Yn===null?Yn=new Set([this]):Yn.add(this));var g=l.stack;this.componentDidCatch(l.value,{componentStack:g!==null?g:""})})}function G0(e,t,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=n.alternate,t!==null&&$a(t,n,i,!0),n=qt.current,n!==null){switch(n.tag){case 13:return Ft===null?gs():n.alternate===null&&Xe===0&&(Xe=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===Sc?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([l]):t.add(l),ys(e,l,i)),!1;case 22:return n.flags|=65536,l===Sc?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([l]):n.add(l)),ys(e,l,i)),!1}throw Error(c(435,n.tag))}return ys(e,l,i),gs(),!1}if(_e)return t=qt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,l!==hc&&(e=Error(c(422),{cause:l}),Ja(Ht(e,n)))):(l!==hc&&(t=Error(c(423),{cause:l}),Ja(Ht(t,n))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,l=Ht(l,n),i=Zc(e.stateNode,l,i),Nc(e,i),Xe!==4&&(Xe=2)),!1;var r=Error(c(520),{cause:l});if(r=Ht(r,n),vi===null?vi=[r]:vi.push(r),Xe!==4&&(Xe=2),t===null)return!0;l=Ht(l,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,e=Zc(n.stateNode,l,e),Nc(n,e),!1;case 1:if(t=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(Yn===null||!Yn.has(r))))return n.flags|=65536,i&=-i,n.lanes|=i,i=nm(i),lm(i,e,n,l),Nc(n,i),!1}n=n.return}while(n!==null);return!1}var am=Error(c(461)),nt=!1;function rt(e,t,n,l){t.child=e===null?Jd(t,null,n,l):na(t,e.child,n,l)}function im(e,t,n,l,i){n=n.render;var r=t.ref;if("ref"in l){var d={};for(var g in l)g!=="ref"&&(d[g]=l[g])}else d=l;return yl(t),l=_c(e,t,n,d,r,i),g=Oc(),e!==null&&!nt?(Mc(e,t,i),gn(e,t,i)):(_e&&g&&dc(t),t.flags|=1,rt(e,t,l,i),t.child)}function rm(e,t,n,l,i){if(e===null){var r=n.type;return typeof r=="function"&&!sc(r)&&r.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=r,om(e,t,r,l,i)):(e=ir(n.type,null,l,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(r=e.child,!es(e,i)){var d=r.memoizedProps;if(n=n.compare,n=n!==null?n:Xa,n(d,l)&&e.ref===t.ref)return gn(e,t,i)}return t.flags|=1,e=sn(r,l),e.ref=t.ref,e.return=t,t.child=e}function om(e,t,n,l,i){if(e!==null){var r=e.memoizedProps;if(Xa(r,l)&&e.ref===t.ref)if(nt=!1,t.pendingProps=l=r,es(e,i))(e.flags&131072)!==0&&(nt=!0);else return t.lanes=e.lanes,gn(e,t,i)}return Kc(e,t,n,l,i)}function cm(e,t,n){var l=t.pendingProps,i=l.children,r=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=r!==null?r.baseLanes|n:n,e!==null){for(i=t.child=e.child,r=0;i!==null;)r=r|i.lanes|i.childLanes,i=i.sibling;t.childLanes=r&~l}else t.childLanes=0,t.child=null;return sm(e,t,l,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&ur(t,r!==null?r.cachePool:null),r!==null?od(t,r):Tc(),$d(t);else return t.lanes=t.childLanes=536870912,sm(e,t,r!==null?r.baseLanes|n:n,n)}else r!==null?(ur(t,r.cachePool),od(t,r),Hn(),t.memoizedState=null):(e!==null&&ur(t,null),Tc(),Hn());return rt(e,t,i,n),t.child}function sm(e,t,n,l){var i=bc();return i=i===null?null:{parent:Fe._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},e!==null&&ur(t,null),Tc(),$d(t),e!==null&&$a(e,t,l,!0),null}function Cr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(c(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function Kc(e,t,n,l,i){return yl(t),n=_c(e,t,n,l,void 0,i),l=Oc(),e!==null&&!nt?(Mc(e,t,i),gn(e,t,i)):(_e&&l&&dc(t),t.flags|=1,rt(e,t,n,i),t.child)}function um(e,t,n,l,i,r){return yl(t),t.updateQueue=null,n=sd(t,l,n,i),cd(e),l=Oc(),e!==null&&!nt?(Mc(e,t,r),gn(e,t,r)):(_e&&l&&dc(t),t.flags|=1,rt(e,t,n,r),t.child)}function fm(e,t,n,l,i){if(yl(t),t.stateNode===null){var r=Zl,d=n.contextType;typeof d=="object"&&d!==null&&(r=dt(d)),r=new n(l,r),t.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=Qc,t.stateNode=r,r._reactInternals=t,r=t.stateNode,r.props=l,r.state=t.memoizedState,r.refs={},wc(t),d=n.contextType,r.context=typeof d=="object"&&d!==null?dt(d):Zl,r.state=t.memoizedState,d=n.getDerivedStateFromProps,typeof d=="function"&&(Xc(t,n,d,l),r.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(d=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),d!==r.state&&Qc.enqueueReplaceState(r,r.state,null),ni(t,l,r,i),ti(),r.state=t.memoizedState),typeof r.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){r=t.stateNode;var g=t.memoizedProps,w=Sl(n,g);r.props=w;var z=r.context,q=n.contextType;d=Zl,typeof q=="object"&&q!==null&&(d=dt(q));var Q=n.getDerivedStateFromProps;q=typeof Q=="function"||typeof r.getSnapshotBeforeUpdate=="function",g=t.pendingProps!==g,q||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(g||z!==d)&&Pd(t,r,l,d),Mn=!1;var U=t.memoizedState;r.state=U,ni(t,l,r,i),ti(),z=t.memoizedState,g||U!==z||Mn?(typeof Q=="function"&&(Xc(t,n,Q,l),z=t.memoizedState),(w=Mn||Wd(t,n,w,l,U,z,d))?(q||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(t.flags|=4194308)):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=z),r.props=l,r.state=z,r.context=d,l=w):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{r=t.stateNode,Ec(e,t),d=t.memoizedProps,q=Sl(n,d),r.props=q,Q=t.pendingProps,U=r.context,z=n.contextType,w=Zl,typeof z=="object"&&z!==null&&(w=dt(z)),g=n.getDerivedStateFromProps,(z=typeof g=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(d!==Q||U!==w)&&Pd(t,r,l,w),Mn=!1,U=t.memoizedState,r.state=U,ni(t,l,r,i),ti();var H=t.memoizedState;d!==Q||U!==H||Mn||e!==null&&e.dependencies!==null&&cr(e.dependencies)?(typeof g=="function"&&(Xc(t,n,g,l),H=t.memoizedState),(q=Mn||Wd(t,n,q,l,U,H,w)||e!==null&&e.dependencies!==null&&cr(e.dependencies))?(z||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,H,w),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,H,w)),typeof r.componentDidUpdate=="function"&&(t.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof r.componentDidUpdate!="function"||d===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=H),r.props=l,r.state=H,r.context=w,l=q):(typeof r.componentDidUpdate!="function"||d===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),l=!1)}return r=l,Cr(e,t),l=(t.flags&128)!==0,r||l?(r=t.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:r.render(),t.flags|=1,e!==null&&l?(t.child=na(t,e.child,null,i),t.child=na(t,null,n,i)):rt(e,t,n,i),t.memoizedState=r.state,e=t.child):e=gn(e,t,i),e}function dm(e,t,n,l){return Ka(),t.flags|=256,rt(e,t,n,l),t.child}var Jc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function $c(e){return{baseLanes:e,cachePool:If()}}function Wc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Vt),e}function mm(e,t,n){var l=t.pendingProps,i=!1,r=(t.flags&128)!==0,d;if((d=r)||(d=e!==null&&e.memoizedState===null?!1:(Ie.current&2)!==0),d&&(i=!0,t.flags&=-129),d=(t.flags&32)!==0,t.flags&=-33,e===null){if(_e){if(i?Un(t):Hn(),_e){var g=Ge,w;if(w=g){e:{for(w=g,g=Pt;w.nodeType!==8;){if(!g){g=null;break e}if(w=Qt(w.nextSibling),w===null){g=null;break e}}g=w}g!==null?(t.memoizedState={dehydrated:g,treeContext:ml!==null?{id:un,overflow:fn}:null,retryLane:536870912,hydrationErrors:null},w=Tt(18,null,null,0),w.stateNode=g,w.return=t,t.child=w,vt=t,Ge=null,w=!0):w=!1}w||gl(t)}if(g=t.memoizedState,g!==null&&(g=g.dehydrated,g!==null))return Ds(g)?t.lanes=32:t.lanes=536870912,null;vn(t)}return g=l.children,l=l.fallback,i?(Hn(),i=t.mode,g=jr({mode:"hidden",children:g},i),l=dl(l,i,n,null),g.return=t,l.return=t,g.sibling=l,t.child=g,i=t.child,i.memoizedState=$c(n),i.childLanes=Wc(e,d,n),t.memoizedState=Jc,l):(Un(t),Pc(t,g))}if(w=e.memoizedState,w!==null&&(g=w.dehydrated,g!==null)){if(r)t.flags&256?(Un(t),t.flags&=-257,t=Fc(e,t,n)):t.memoizedState!==null?(Hn(),t.child=e.child,t.flags|=128,t=null):(Hn(),i=l.fallback,g=t.mode,l=jr({mode:"visible",children:l.children},g),i=dl(i,g,n,null),i.flags|=2,l.return=t,i.return=t,l.sibling=i,t.child=l,na(t,e.child,null,n),l=t.child,l.memoizedState=$c(n),l.childLanes=Wc(e,d,n),t.memoizedState=Jc,t=i);else if(Un(t),Ds(g)){if(d=g.nextSibling&&g.nextSibling.dataset,d)var z=d.dgst;d=z,l=Error(c(419)),l.stack="",l.digest=d,Ja({value:l,source:null,stack:null}),t=Fc(e,t,n)}else if(nt||$a(e,t,n,!1),d=(n&e.childLanes)!==0,nt||d){if(d=Be,d!==null&&(l=n&-n,l=(l&42)!==0?1:zo(l),l=(l&(d.suspendedLanes|n))!==0?0:l,l!==0&&l!==w.retryLane))throw w.retryLane=l,Ql(e,l),Mt(d,e,l),am;g.data==="$?"||gs(),t=Fc(e,t,n)}else g.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=w.treeContext,Ge=Qt(g.nextSibling),vt=t,_e=!0,vl=null,Pt=!1,e!==null&&(Bt[kt++]=un,Bt[kt++]=fn,Bt[kt++]=ml,un=e.id,fn=e.overflow,ml=t),t=Pc(t,l.children),t.flags|=4096);return t}return i?(Hn(),i=l.fallback,g=t.mode,w=e.child,z=w.sibling,l=sn(w,{mode:"hidden",children:l.children}),l.subtreeFlags=w.subtreeFlags&65011712,z!==null?i=sn(z,i):(i=dl(i,g,n,null),i.flags|=2),i.return=t,l.return=t,l.sibling=i,t.child=l,l=i,i=t.child,g=e.child.memoizedState,g===null?g=$c(n):(w=g.cachePool,w!==null?(z=Fe._currentValue,w=w.parent!==z?{parent:z,pool:z}:w):w=If(),g={baseLanes:g.baseLanes|n,cachePool:w}),i.memoizedState=g,i.childLanes=Wc(e,d,n),t.memoizedState=Jc,l):(Un(t),n=e.child,e=n.sibling,n=sn(n,{mode:"visible",children:l.children}),n.return=t,n.sibling=null,e!==null&&(d=t.deletions,d===null?(t.deletions=[e],t.flags|=16):d.push(e)),t.child=n,t.memoizedState=null,n)}function Pc(e,t){return t=jr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function jr(e,t){return e=Tt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Fc(e,t,n){return na(t,e.child,null,n),e=Pc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function hm(e,t,n){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),gc(e.return,t,n)}function Ic(e,t,n,l,i){var r=e.memoizedState;r===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(r.isBackwards=t,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=n,r.tailMode=i)}function vm(e,t,n){var l=t.pendingProps,i=l.revealOrder,r=l.tail;if(rt(e,t,l.children,n),l=Ie.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&hm(e,n,t);else if(e.tag===19)hm(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(W(Ie,l),i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Nr(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ic(t,!1,i,n,r);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Nr(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ic(t,!0,n,null,r);break;case"together":Ic(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function gn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Vn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if($a(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,n=sn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=sn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function es(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&cr(e)))}function X0(e,t,n){switch(t.tag){case 3:fe(t,t.stateNode.containerInfo),On(t,Fe,e.memoizedState.cache),Ka();break;case 27:case 5:Te(t);break;case 4:fe(t,t.stateNode.containerInfo);break;case 10:On(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Un(t),t.flags|=128,null):(n&t.child.childLanes)!==0?mm(e,t,n):(Un(t),e=gn(e,t,n),e!==null?e.sibling:null);Un(t);break;case 19:var i=(e.flags&128)!==0;if(l=(n&t.childLanes)!==0,l||($a(e,t,n,!1),l=(n&t.childLanes)!==0),i){if(l)return vm(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),W(Ie,Ie.current),l)break;return null;case 22:case 23:return t.lanes=0,cm(e,t,n);case 24:On(t,Fe,e.memoizedState.cache)}return gn(e,t,n)}function gm(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)nt=!0;else{if(!es(e,n)&&(t.flags&128)===0)return nt=!1,X0(e,t,n);nt=(e.flags&131072)!==0}else nt=!1,_e&&(t.flags&1048576)!==0&&Zf(t,or,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,i=l._init;if(l=i(l._payload),t.type=l,typeof l=="function")sc(l)?(e=Sl(l,e),t.tag=1,t=fm(null,t,l,e,n)):(t.tag=0,t=Kc(null,t,l,e,n));else{if(l!=null){if(i=l.$$typeof,i===K){t.tag=11,t=im(null,t,l,e,n);break e}else if(i===P){t.tag=14,t=rm(null,t,l,e,n);break e}}throw t=pe(l)||l,Error(c(306,t,""))}}return t;case 0:return Kc(e,t,t.type,t.pendingProps,n);case 1:return l=t.type,i=Sl(l,t.pendingProps),fm(e,t,l,i,n);case 3:e:{if(fe(t,t.stateNode.containerInfo),e===null)throw Error(c(387));l=t.pendingProps;var r=t.memoizedState;i=r.element,Ec(e,t),ni(t,l,null,n);var d=t.memoizedState;if(l=d.cache,On(t,Fe,l),l!==r.cache&&pc(t,[Fe],n,!0),ti(),l=d.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:d.cache},t.updateQueue.baseState=r,t.memoizedState=r,t.flags&256){t=dm(e,t,l,n);break e}else if(l!==i){i=Ht(Error(c(424)),t),Ja(i),t=dm(e,t,l,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ge=Qt(e.firstChild),vt=t,_e=!0,vl=null,Pt=!0,n=Jd(t,null,l,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Ka(),l===i){t=gn(e,t,n);break e}rt(e,t,l,n)}t=t.child}return t;case 26:return Cr(e,t),e===null?(n=bh(t.type,null,t.pendingProps,null))?t.memoizedState=n:_e||(n=t.type,e=t.pendingProps,l=Yr(ce.current).createElement(n),l[ft]=t,l[pt]=e,ct(l,n,e),tt(l),t.stateNode=l):t.memoizedState=bh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Te(t),e===null&&_e&&(l=t.stateNode=ph(t.type,t.pendingProps,ce.current),vt=t,Pt=!0,i=Ge,Qn(t.type)?(zs=i,Ge=Qt(l.firstChild)):Ge=i),rt(e,t,t.pendingProps.children,n),Cr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&_e&&((i=l=Ge)&&(l=yy(l,t.type,t.pendingProps,Pt),l!==null?(t.stateNode=l,vt=t,Ge=Qt(l.firstChild),Pt=!1,i=!0):i=!1),i||gl(t)),Te(t),i=t.type,r=t.pendingProps,d=e!==null?e.memoizedProps:null,l=r.children,Os(i,r)?l=null:d!==null&&Os(i,d)&&(t.flags|=32),t.memoizedState!==null&&(i=_c(e,t,H0,null,null,n),Ni._currentValue=i),Cr(e,t),rt(e,t,l,n),t.child;case 6:return e===null&&_e&&((e=n=Ge)&&(n=xy(n,t.pendingProps,Pt),n!==null?(t.stateNode=n,vt=t,Ge=null,e=!0):e=!1),e||gl(t)),null;case 13:return mm(e,t,n);case 4:return fe(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=na(t,null,l,n):rt(e,t,l,n),t.child;case 11:return im(e,t,t.type,t.pendingProps,n);case 7:return rt(e,t,t.pendingProps,n),t.child;case 8:return rt(e,t,t.pendingProps.children,n),t.child;case 12:return rt(e,t,t.pendingProps.children,n),t.child;case 10:return l=t.pendingProps,On(t,t.type,l.value),rt(e,t,l.children,n),t.child;case 9:return i=t.type._context,l=t.pendingProps.children,yl(t),i=dt(i),l=l(i),t.flags|=1,rt(e,t,l,n),t.child;case 14:return rm(e,t,t.type,t.pendingProps,n);case 15:return om(e,t,t.type,t.pendingProps,n);case 19:return vm(e,t,n);case 31:return l=t.pendingProps,n=t.mode,l={mode:l.mode,children:l.children},e===null?(n=jr(l,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=sn(e.child,l),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return cm(e,t,n);case 24:return yl(t),l=dt(Fe),e===null?(i=bc(),i===null&&(i=Be,r=yc(),i.pooledCache=r,r.refCount++,r!==null&&(i.pooledCacheLanes|=n),i=r),t.memoizedState={parent:l,cache:i},wc(t),On(t,Fe,i)):((e.lanes&n)!==0&&(Ec(e,t),ni(t,null,null,n),ti()),i=e.memoizedState,r=t.memoizedState,i.parent!==l?(i={parent:l,cache:l},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),On(t,Fe,l)):(l=r.cache,On(t,Fe,l),l!==i.cache&&pc(t,[Fe],n,!0))),rt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function pn(e){e.flags|=4}function pm(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Ah(t)){if(t=qt.current,t!==null&&((Ce&4194048)===Ce?Ft!==null:(Ce&62914560)!==Ce&&(Ce&536870912)===0||t!==Ft))throw Ia=Sc,ed;e.flags|=8192}}function _r(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Ju():536870912,e.lanes|=t,ra|=t)}function si(e,t){if(!_e)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Ye(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,l=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=l,e.childLanes=n,t}function Q0(e,t,n){var l=t.pendingProps;switch(mc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ye(t),null;case 1:return Ye(t),null;case 3:return n=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),mn(Fe),Oe(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Za(t)?pn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,$f())),Ye(t),null;case 26:return n=t.memoizedState,e===null?(pn(t),n!==null?(Ye(t),pm(t,n)):(Ye(t),t.flags&=-16777217)):n?n!==e.memoizedState?(pn(t),Ye(t),pm(t,n)):(Ye(t),t.flags&=-16777217):(e.memoizedProps!==l&&pn(t),Ye(t),t.flags&=-16777217),null;case 27:we(t),n=ce.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&pn(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Ye(t),null}e=te.current,Za(t)?Kf(t):(e=ph(i,l,n),t.stateNode=e,pn(t))}return Ye(t),null;case 5:if(we(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&pn(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Ye(t),null}if(e=te.current,Za(t))Kf(t);else{switch(i=Yr(ce.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}e[ft]=t,e[pt]=l;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(ct(e,n,l),n){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&pn(t)}}return Ye(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&pn(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(c(166));if(e=ce.current,Za(t)){if(e=t.stateNode,n=t.memoizedProps,l=null,i=vt,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}e[ft]=t,e=!!(e.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||uh(e.nodeValue,n)),e||gl(t)}else e=Yr(e).createTextNode(l),e[ft]=t,t.stateNode=e}return Ye(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=Za(t),l!==null&&l.dehydrated!==null){if(e===null){if(!i)throw Error(c(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(c(317));i[ft]=t}else Ka(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ye(t),i=!1}else i=$f(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(vn(t),t):(vn(t),null)}if(vn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=l!==null,e=e!==null&&e.memoizedState!==null,n){l=t.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==i&&(l.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),_r(t,t.updateQueue),Ye(t),null;case 4:return Oe(),e===null&&As(t.stateNode.containerInfo),Ye(t),null;case 10:return mn(t.type),Ye(t),null;case 19:if(F(Ie),i=t.memoizedState,i===null)return Ye(t),null;if(l=(t.flags&128)!==0,r=i.rendering,r===null)if(l)si(i,!1);else{if(Xe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(r=Nr(e),r!==null){for(t.flags|=128,si(i,!1),e=r.updateQueue,t.updateQueue=e,_r(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Qf(n,e),n=n.sibling;return W(Ie,Ie.current&1|2),t.child}e=e.sibling}i.tail!==null&&ut()>Rr&&(t.flags|=128,l=!0,si(i,!1),t.lanes=4194304)}else{if(!l)if(e=Nr(r),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,_r(t,e),si(i,!0),i.tail===null&&i.tailMode==="hidden"&&!r.alternate&&!_e)return Ye(t),null}else 2*ut()-i.renderingStartTime>Rr&&n!==536870912&&(t.flags|=128,l=!0,si(i,!1),t.lanes=4194304);i.isBackwards?(r.sibling=t.child,t.child=r):(e=i.last,e!==null?e.sibling=r:t.child=r,i.last=r)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ut(),t.sibling=null,e=Ie.current,W(Ie,l?e&1|2:e&1),t):(Ye(t),null);case 22:case 23:return vn(t),Cc(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(n&536870912)!==0&&(t.flags&128)===0&&(Ye(t),t.subtreeFlags&6&&(t.flags|=8192)):Ye(t),n=t.updateQueue,n!==null&&_r(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==n&&(t.flags|=2048),e!==null&&F(xl),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),mn(Fe),Ye(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function Z0(e,t){switch(mc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return mn(Fe),Oe(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return we(t),null;case 13:if(vn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));Ka()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return F(Ie),null;case 4:return Oe(),null;case 10:return mn(t.type),null;case 22:case 23:return vn(t),Cc(),e!==null&&F(xl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return mn(Fe),null;case 25:return null;default:return null}}function ym(e,t){switch(mc(t),t.tag){case 3:mn(Fe),Oe();break;case 26:case 27:case 5:we(t);break;case 4:Oe();break;case 13:vn(t);break;case 19:F(Ie);break;case 10:mn(t.type);break;case 22:case 23:vn(t),Cc(),e!==null&&F(xl);break;case 24:mn(Fe)}}function ui(e,t){try{var n=t.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&e)===e){l=void 0;var r=n.create,d=n.inst;l=r(),d.destroy=l}n=n.next}while(n!==i)}}catch(g){He(t,t.return,g)}}function Ln(e,t,n){try{var l=t.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var r=i.next;l=r;do{if((l.tag&e)===e){var d=l.inst,g=d.destroy;if(g!==void 0){d.destroy=void 0,i=t;var w=n,z=g;try{z()}catch(q){He(i,w,q)}}}l=l.next}while(l!==r)}}catch(q){He(t,t.return,q)}}function xm(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{rd(t,n)}catch(l){He(e,e.return,l)}}}function bm(e,t,n){n.props=Sl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(l){He(e,t,l)}}function fi(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof n=="function"?e.refCleanup=n(l):n.current=l}}catch(i){He(e,t,i)}}function It(e,t){var n=e.ref,l=e.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){He(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){He(e,t,i)}else n.current=null}function Sm(e){var t=e.type,n=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break e;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){He(e,e.return,i)}}function ts(e,t,n){try{var l=e.stateNode;my(l,e.type,n,t),l[pt]=t}catch(i){He(e,e.return,i)}}function wm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Qn(e.type)||e.tag===4}function ns(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||wm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Qn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ls(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Vr));else if(l!==4&&(l===27&&Qn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(ls(e,t,n),e=e.sibling;e!==null;)ls(e,t,n),e=e.sibling}function Or(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(l!==4&&(l===27&&Qn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Or(e,t,n),e=e.sibling;e!==null;)Or(e,t,n),e=e.sibling}function Em(e){var t=e.stateNode,n=e.memoizedProps;try{for(var l=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);ct(t,l,n),t[ft]=e,t[pt]=n}catch(r){He(e,e.return,r)}}var yn=!1,Ke=!1,as=!1,Nm=typeof WeakSet=="function"?WeakSet:Set,lt=null;function K0(e,t){if(e=e.containerInfo,js=Jr,e=Uf(e),nc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break e}var d=0,g=-1,w=-1,z=0,q=0,Q=e,U=null;t:for(;;){for(var H;Q!==n||i!==0&&Q.nodeType!==3||(g=d+i),Q!==r||l!==0&&Q.nodeType!==3||(w=d+l),Q.nodeType===3&&(d+=Q.nodeValue.length),(H=Q.firstChild)!==null;)U=Q,Q=H;for(;;){if(Q===e)break t;if(U===n&&++z===i&&(g=d),U===r&&++q===l&&(w=d),(H=Q.nextSibling)!==null)break;Q=U,U=Q.parentNode}Q=H}n=g===-1||w===-1?null:{start:g,end:w}}else n=null}n=n||{start:0,end:0}}else n=null;for(_s={focusedElem:e,selectionRange:n},Jr=!1,lt=t;lt!==null;)if(t=lt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,lt=e;else for(;lt!==null;){switch(t=lt,r=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&r!==null){e=void 0,n=t,i=r.memoizedProps,r=r.memoizedState,l=n.stateNode;try{var he=Sl(n.type,i,n.elementType===n.type);e=l.getSnapshotBeforeUpdate(he,r),l.__reactInternalSnapshotBeforeUpdate=e}catch(de){He(n,n.return,de)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Rs(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Rs(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,lt=e;break}lt=t.return}}function Am(e,t,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:Bn(e,n),l&4&&ui(5,n);break;case 1:if(Bn(e,n),l&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(d){He(n,n.return,d)}else{var i=Sl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(d){He(n,n.return,d)}}l&64&&xm(n),l&512&&fi(n,n.return);break;case 3:if(Bn(e,n),l&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{rd(e,t)}catch(d){He(n,n.return,d)}}break;case 27:t===null&&l&4&&Em(n);case 26:case 5:Bn(e,n),t===null&&l&4&&Sm(n),l&512&&fi(n,n.return);break;case 12:Bn(e,n);break;case 13:Bn(e,n),l&4&&jm(e,n),l&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=ny.bind(null,n),by(e,n))));break;case 22:if(l=n.memoizedState!==null||yn,!l){t=t!==null&&t.memoizedState!==null||Ke,i=yn;var r=Ke;yn=l,(Ke=t)&&!r?kn(e,n,(n.subtreeFlags&8772)!==0):Bn(e,n),yn=i,Ke=r}break;case 30:break;default:Bn(e,n)}}function Tm(e){var t=e.alternate;t!==null&&(e.alternate=null,Tm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Lo(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ve=null,bt=!1;function xn(e,t,n){for(n=n.child;n!==null;)Cm(e,t,n),n=n.sibling}function Cm(e,t,n){if(Et&&typeof Et.onCommitFiberUnmount=="function")try{Et.onCommitFiberUnmount(Ra,n)}catch{}switch(n.tag){case 26:Ke||It(n,t),xn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Ke||It(n,t);var l=Ve,i=bt;Qn(n.type)&&(Ve=n.stateNode,bt=!1),xn(e,t,n),bi(n.stateNode),Ve=l,bt=i;break;case 5:Ke||It(n,t);case 6:if(l=Ve,i=bt,Ve=null,xn(e,t,n),Ve=l,bt=i,Ve!==null)if(bt)try{(Ve.nodeType===9?Ve.body:Ve.nodeName==="HTML"?Ve.ownerDocument.body:Ve).removeChild(n.stateNode)}catch(r){He(n,t,r)}else try{Ve.removeChild(n.stateNode)}catch(r){He(n,t,r)}break;case 18:Ve!==null&&(bt?(e=Ve,vh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),ji(e)):vh(Ve,n.stateNode));break;case 4:l=Ve,i=bt,Ve=n.stateNode.containerInfo,bt=!0,xn(e,t,n),Ve=l,bt=i;break;case 0:case 11:case 14:case 15:Ke||Ln(2,n,t),Ke||Ln(4,n,t),xn(e,t,n);break;case 1:Ke||(It(n,t),l=n.stateNode,typeof l.componentWillUnmount=="function"&&bm(n,t,l)),xn(e,t,n);break;case 21:xn(e,t,n);break;case 22:Ke=(l=Ke)||n.memoizedState!==null,xn(e,t,n),Ke=l;break;default:xn(e,t,n)}}function jm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{ji(e)}catch(n){He(t,t.return,n)}}function J0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Nm),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Nm),t;default:throw Error(c(435,e.tag))}}function is(e,t){var n=J0(e);t.forEach(function(l){var i=ly.bind(null,e,l);n.has(l)||(n.add(l),l.then(i,i))})}function Ct(e,t){var n=t.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],r=e,d=t,g=d;e:for(;g!==null;){switch(g.tag){case 27:if(Qn(g.type)){Ve=g.stateNode,bt=!1;break e}break;case 5:Ve=g.stateNode,bt=!1;break e;case 3:case 4:Ve=g.stateNode.containerInfo,bt=!0;break e}g=g.return}if(Ve===null)throw Error(c(160));Cm(r,d,i),Ve=null,bt=!1,r=i.alternate,r!==null&&(r.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)_m(t,e),t=t.sibling}var Xt=null;function _m(e,t){var n=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ct(t,e),jt(e),l&4&&(Ln(3,e,e.return),ui(3,e),Ln(5,e,e.return));break;case 1:Ct(t,e),jt(e),l&512&&(Ke||n===null||It(n,n.return)),l&64&&yn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=Xt;if(Ct(t,e),jt(e),l&512&&(Ke||n===null||It(n,n.return)),l&4){var r=n!==null?n.memoizedState:null;if(l=e.memoizedState,n===null)if(l===null)if(e.stateNode===null){e:{l=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(l){case"title":r=i.getElementsByTagName("title")[0],(!r||r[Ua]||r[ft]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=i.createElement(l),i.head.insertBefore(r,i.querySelector("head > title"))),ct(r,l,n),r[ft]=e,tt(r),l=r;break e;case"link":var d=Eh("link","href",i).get(l+(n.href||""));if(d){for(var g=0;g<d.length;g++)if(r=d[g],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){d.splice(g,1);break t}}r=i.createElement(l),ct(r,l,n),i.head.appendChild(r);break;case"meta":if(d=Eh("meta","content",i).get(l+(n.content||""))){for(g=0;g<d.length;g++)if(r=d[g],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){d.splice(g,1);break t}}r=i.createElement(l),ct(r,l,n),i.head.appendChild(r);break;default:throw Error(c(468,l))}r[ft]=e,tt(r),l=r}e.stateNode=l}else Nh(i,e.type,e.stateNode);else e.stateNode=wh(i,l,e.memoizedProps);else r!==l?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,l===null?Nh(i,e.type,e.stateNode):wh(i,l,e.memoizedProps)):l===null&&e.stateNode!==null&&ts(e,e.memoizedProps,n.memoizedProps)}break;case 27:Ct(t,e),jt(e),l&512&&(Ke||n===null||It(n,n.return)),n!==null&&l&4&&ts(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Ct(t,e),jt(e),l&512&&(Ke||n===null||It(n,n.return)),e.flags&32){i=e.stateNode;try{Bl(i,"")}catch(H){He(e,e.return,H)}}l&4&&e.stateNode!=null&&(i=e.memoizedProps,ts(e,i,n!==null?n.memoizedProps:i)),l&1024&&(as=!0);break;case 6:if(Ct(t,e),jt(e),l&4){if(e.stateNode===null)throw Error(c(162));l=e.memoizedProps,n=e.stateNode;try{n.nodeValue=l}catch(H){He(e,e.return,H)}}break;case 3:if(Qr=null,i=Xt,Xt=Gr(t.containerInfo),Ct(t,e),Xt=i,jt(e),l&4&&n!==null&&n.memoizedState.isDehydrated)try{ji(t.containerInfo)}catch(H){He(e,e.return,H)}as&&(as=!1,Om(e));break;case 4:l=Xt,Xt=Gr(e.stateNode.containerInfo),Ct(t,e),jt(e),Xt=l;break;case 12:Ct(t,e),jt(e);break;case 13:Ct(t,e),jt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(fs=ut()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,is(e,l)));break;case 22:i=e.memoizedState!==null;var w=n!==null&&n.memoizedState!==null,z=yn,q=Ke;if(yn=z||i,Ke=q||w,Ct(t,e),Ke=q,yn=z,jt(e),l&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(n===null||w||yn||Ke||wl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){w=n=t;try{if(r=w.stateNode,i)d=r.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{g=w.stateNode;var Q=w.memoizedProps.style,U=Q!=null&&Q.hasOwnProperty("display")?Q.display:null;g.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(H){He(w,w.return,H)}}}else if(t.tag===6){if(n===null){w=t;try{w.stateNode.nodeValue=i?"":w.memoizedProps}catch(H){He(w,w.return,H)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,is(e,n))));break;case 19:Ct(t,e),jt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,is(e,l)));break;case 30:break;case 21:break;default:Ct(t,e),jt(e)}}function jt(e){var t=e.flags;if(t&2){try{for(var n,l=e.return;l!==null;){if(wm(l)){n=l;break}l=l.return}if(n==null)throw Error(c(160));switch(n.tag){case 27:var i=n.stateNode,r=ns(e);Or(e,r,i);break;case 5:var d=n.stateNode;n.flags&32&&(Bl(d,""),n.flags&=-33);var g=ns(e);Or(e,g,d);break;case 3:case 4:var w=n.stateNode.containerInfo,z=ns(e);ls(e,z,w);break;default:throw Error(c(161))}}catch(q){He(e,e.return,q)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Om(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Om(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Bn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Am(e,t.alternate,t),t=t.sibling}function wl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ln(4,t,t.return),wl(t);break;case 1:It(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&bm(t,t.return,n),wl(t);break;case 27:bi(t.stateNode);case 26:case 5:It(t,t.return),wl(t);break;case 22:t.memoizedState===null&&wl(t);break;case 30:wl(t);break;default:wl(t)}e=e.sibling}}function kn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,i=e,r=t,d=r.flags;switch(r.tag){case 0:case 11:case 15:kn(i,r,n),ui(4,r);break;case 1:if(kn(i,r,n),l=r,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(z){He(l,l.return,z)}if(l=r,i=l.updateQueue,i!==null){var g=l.stateNode;try{var w=i.shared.hiddenCallbacks;if(w!==null)for(i.shared.hiddenCallbacks=null,i=0;i<w.length;i++)id(w[i],g)}catch(z){He(l,l.return,z)}}n&&d&64&&xm(r),fi(r,r.return);break;case 27:Em(r);case 26:case 5:kn(i,r,n),n&&l===null&&d&4&&Sm(r),fi(r,r.return);break;case 12:kn(i,r,n);break;case 13:kn(i,r,n),n&&d&4&&jm(i,r);break;case 22:r.memoizedState===null&&kn(i,r,n),fi(r,r.return);break;case 30:break;default:kn(i,r,n)}t=t.sibling}}function rs(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Wa(n))}function os(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Wa(e))}function en(e,t,n,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Mm(e,t,n,l),t=t.sibling}function Mm(e,t,n,l){var i=t.flags;switch(t.tag){case 0:case 11:case 15:en(e,t,n,l),i&2048&&ui(9,t);break;case 1:en(e,t,n,l);break;case 3:en(e,t,n,l),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Wa(e)));break;case 12:if(i&2048){en(e,t,n,l),e=t.stateNode;try{var r=t.memoizedProps,d=r.id,g=r.onPostCommit;typeof g=="function"&&g(d,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(w){He(t,t.return,w)}}else en(e,t,n,l);break;case 13:en(e,t,n,l);break;case 23:break;case 22:r=t.stateNode,d=t.alternate,t.memoizedState!==null?r._visibility&2?en(e,t,n,l):di(e,t):r._visibility&2?en(e,t,n,l):(r._visibility|=2,la(e,t,n,l,(t.subtreeFlags&10256)!==0)),i&2048&&rs(d,t);break;case 24:en(e,t,n,l),i&2048&&os(t.alternate,t);break;default:en(e,t,n,l)}}function la(e,t,n,l,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var r=e,d=t,g=n,w=l,z=d.flags;switch(d.tag){case 0:case 11:case 15:la(r,d,g,w,i),ui(8,d);break;case 23:break;case 22:var q=d.stateNode;d.memoizedState!==null?q._visibility&2?la(r,d,g,w,i):di(r,d):(q._visibility|=2,la(r,d,g,w,i)),i&&z&2048&&rs(d.alternate,d);break;case 24:la(r,d,g,w,i),i&&z&2048&&os(d.alternate,d);break;default:la(r,d,g,w,i)}t=t.sibling}}function di(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,l=t,i=l.flags;switch(l.tag){case 22:di(n,l),i&2048&&rs(l.alternate,l);break;case 24:di(n,l),i&2048&&os(l.alternate,l);break;default:di(n,l)}t=t.sibling}}var mi=8192;function aa(e){if(e.subtreeFlags&mi)for(e=e.child;e!==null;)Rm(e),e=e.sibling}function Rm(e){switch(e.tag){case 26:aa(e),e.flags&mi&&e.memoizedState!==null&&Dy(Xt,e.memoizedState,e.memoizedProps);break;case 5:aa(e);break;case 3:case 4:var t=Xt;Xt=Gr(e.stateNode.containerInfo),aa(e),Xt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=mi,mi=16777216,aa(e),mi=t):aa(e));break;default:aa(e)}}function Dm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function hi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];lt=l,Um(l,e)}Dm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)zm(e),e=e.sibling}function zm(e){switch(e.tag){case 0:case 11:case 15:hi(e),e.flags&2048&&Ln(9,e,e.return);break;case 3:hi(e);break;case 12:hi(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Mr(e)):hi(e);break;default:hi(e)}}function Mr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];lt=l,Um(l,e)}Dm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Ln(8,t,t.return),Mr(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Mr(t));break;default:Mr(t)}e=e.sibling}}function Um(e,t){for(;lt!==null;){var n=lt;switch(n.tag){case 0:case 11:case 15:Ln(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Wa(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,lt=l;else e:for(n=e;lt!==null;){l=lt;var i=l.sibling,r=l.return;if(Tm(l),l===n){lt=null;break e}if(i!==null){i.return=r,lt=i;break e}lt=r}}}var $0={getCacheForType:function(e){var t=dt(Fe),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},W0=typeof WeakMap=="function"?WeakMap:Map,Me=0,Be=null,Ne=null,Ce=0,Re=0,_t=null,qn=!1,ia=!1,cs=!1,bn=0,Xe=0,Vn=0,El=0,ss=0,Vt=0,ra=0,vi=null,St=null,us=!1,fs=0,Rr=1/0,Dr=null,Yn=null,ot=0,Gn=null,oa=null,ca=0,ds=0,ms=null,Hm=null,gi=0,hs=null;function Ot(){if((Me&2)!==0&&Ce!==0)return Ce&-Ce;if(_.T!==null){var e=$l;return e!==0?e:Ss()}return Pu()}function Lm(){Vt===0&&(Vt=(Ce&536870912)===0||_e?Ku():536870912);var e=qt.current;return e!==null&&(e.flags|=32),Vt}function Mt(e,t,n){(e===Be&&(Re===2||Re===9)||e.cancelPendingCommit!==null)&&(sa(e,0),Xn(e,Ce,Vt,!1)),za(e,n),((Me&2)===0||e!==Be)&&(e===Be&&((Me&2)===0&&(El|=n),Xe===4&&Xn(e,Ce,Vt,!1)),tn(e))}function Bm(e,t,n){if((Me&6)!==0)throw Error(c(327));var l=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Da(e,t),i=l?I0(e,t):ps(e,t,!0),r=l;do{if(i===0){ia&&!l&&Xn(e,t,0,!1);break}else{if(n=e.current.alternate,r&&!P0(n)){i=ps(e,t,!1),r=!1;continue}if(i===2){if(r=t,e.errorRecoveryDisabledLanes&r)var d=0;else d=e.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){t=d;e:{var g=e;i=vi;var w=g.current.memoizedState.isDehydrated;if(w&&(sa(g,d).flags|=256),d=ps(g,d,!1),d!==2){if(cs&&!w){g.errorRecoveryDisabledLanes|=r,El|=r,i=4;break e}r=St,St=i,r!==null&&(St===null?St=r:St.push.apply(St,r))}i=d}if(r=!1,i!==2)continue}}if(i===1){sa(e,0),Xn(e,t,0,!0);break}e:{switch(l=e,r=i,r){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Xn(l,t,Vt,!qn);break e;case 2:St=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(i=fs+300-ut(),10<i)){if(Xn(l,t,Vt,!qn),Xi(l,0,!0)!==0)break e;l.timeoutHandle=mh(km.bind(null,l,n,St,Dr,us,t,Vt,El,ra,qn,r,2,-0,0),i);break e}km(l,n,St,Dr,us,t,Vt,El,ra,qn,r,0,-0,0)}}break}while(!0);tn(e)}function km(e,t,n,l,i,r,d,g,w,z,q,Q,U,H){if(e.timeoutHandle=-1,Q=t.subtreeFlags,(Q&8192||(Q&16785408)===16785408)&&(Ei={stylesheets:null,count:0,unsuspend:Ry},Rm(t),Q=zy(),Q!==null)){e.cancelPendingCommit=Q(Zm.bind(null,e,t,r,n,l,i,d,g,w,q,1,U,H)),Xn(e,r,d,!z);return}Zm(e,t,r,n,l,i,d,g,w)}function P0(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],r=i.getSnapshot;i=i.value;try{if(!At(r(),i))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Xn(e,t,n,l){t&=~ss,t&=~El,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var i=t;0<i;){var r=31-Nt(i),d=1<<r;l[r]=-1,i&=~d}n!==0&&$u(e,n,t)}function zr(){return(Me&6)===0?(pi(0),!1):!0}function vs(){if(Ne!==null){if(Re===0)var e=Ne.return;else e=Ne,dn=pl=null,Rc(e),ta=null,oi=0,e=Ne;for(;e!==null;)ym(e.alternate,e),e=e.return;Ne=null}}function sa(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,vy(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),vs(),Be=e,Ne=n=sn(e.current,null),Ce=t,Re=0,_t=null,qn=!1,ia=Da(e,t),cs=!1,ra=Vt=ss=El=Vn=Xe=0,St=vi=null,us=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var i=31-Nt(l),r=1<<i;t|=e[i],l&=~r}return bn=t,nr(),n}function qm(e,t){xe=null,_.H=Sr,t===Fa||t===fr?(t=ld(),Re=3):t===ed?(t=ld(),Re=4):Re=t===am?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,_t=t,Ne===null&&(Xe=1,Tr(e,Ht(t,e.current)))}function Vm(){var e=_.H;return _.H=Sr,e===null?Sr:e}function Ym(){var e=_.A;return _.A=$0,e}function gs(){Xe=4,qn||(Ce&4194048)!==Ce&&qt.current!==null||(ia=!0),(Vn&134217727)===0&&(El&134217727)===0||Be===null||Xn(Be,Ce,Vt,!1)}function ps(e,t,n){var l=Me;Me|=2;var i=Vm(),r=Ym();(Be!==e||Ce!==t)&&(Dr=null,sa(e,t)),t=!1;var d=Xe;e:do try{if(Re!==0&&Ne!==null){var g=Ne,w=_t;switch(Re){case 8:vs(),d=6;break e;case 3:case 2:case 9:case 6:qt.current===null&&(t=!0);var z=Re;if(Re=0,_t=null,ua(e,g,w,z),n&&ia){d=0;break e}break;default:z=Re,Re=0,_t=null,ua(e,g,w,z)}}F0(),d=Xe;break}catch(q){qm(e,q)}while(!0);return t&&e.shellSuspendCounter++,dn=pl=null,Me=l,_.H=i,_.A=r,Ne===null&&(Be=null,Ce=0,nr()),d}function F0(){for(;Ne!==null;)Gm(Ne)}function I0(e,t){var n=Me;Me|=2;var l=Vm(),i=Ym();Be!==e||Ce!==t?(Dr=null,Rr=ut()+500,sa(e,t)):ia=Da(e,t);e:do try{if(Re!==0&&Ne!==null){t=Ne;var r=_t;t:switch(Re){case 1:Re=0,_t=null,ua(e,t,r,1);break;case 2:case 9:if(td(r)){Re=0,_t=null,Xm(t);break}t=function(){Re!==2&&Re!==9||Be!==e||(Re=7),tn(e)},r.then(t,t);break e;case 3:Re=7;break e;case 4:Re=5;break e;case 7:td(r)?(Re=0,_t=null,Xm(t)):(Re=0,_t=null,ua(e,t,r,7));break;case 5:var d=null;switch(Ne.tag){case 26:d=Ne.memoizedState;case 5:case 27:var g=Ne;if(!d||Ah(d)){Re=0,_t=null;var w=g.sibling;if(w!==null)Ne=w;else{var z=g.return;z!==null?(Ne=z,Ur(z)):Ne=null}break t}}Re=0,_t=null,ua(e,t,r,5);break;case 6:Re=0,_t=null,ua(e,t,r,6);break;case 8:vs(),Xe=6;break e;default:throw Error(c(462))}}ey();break}catch(q){qm(e,q)}while(!0);return dn=pl=null,_.H=l,_.A=i,Me=n,Ne!==null?0:(Be=null,Ce=0,nr(),Xe)}function ey(){for(;Ne!==null&&!al();)Gm(Ne)}function Gm(e){var t=gm(e.alternate,e,bn);e.memoizedProps=e.pendingProps,t===null?Ur(e):Ne=t}function Xm(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=um(n,t,t.pendingProps,t.type,void 0,Ce);break;case 11:t=um(n,t,t.pendingProps,t.type.render,t.ref,Ce);break;case 5:Rc(t);default:ym(n,t),t=Ne=Qf(t,bn),t=gm(n,t,bn)}e.memoizedProps=e.pendingProps,t===null?Ur(e):Ne=t}function ua(e,t,n,l){dn=pl=null,Rc(t),ta=null,oi=0;var i=t.return;try{if(G0(e,i,t,n,Ce)){Xe=1,Tr(e,Ht(n,e.current)),Ne=null;return}}catch(r){if(i!==null)throw Ne=i,r;Xe=1,Tr(e,Ht(n,e.current)),Ne=null;return}t.flags&32768?(_e||l===1?e=!0:ia||(Ce&536870912)!==0?e=!1:(qn=e=!0,(l===2||l===9||l===3||l===6)&&(l=qt.current,l!==null&&l.tag===13&&(l.flags|=16384))),Qm(t,e)):Ur(t)}function Ur(e){var t=e;do{if((t.flags&32768)!==0){Qm(t,qn);return}e=t.return;var n=Q0(t.alternate,t,bn);if(n!==null){Ne=n;return}if(t=t.sibling,t!==null){Ne=t;return}Ne=t=e}while(t!==null);Xe===0&&(Xe=5)}function Qm(e,t){do{var n=Z0(e.alternate,e);if(n!==null){n.flags&=32767,Ne=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){Ne=e;return}Ne=e=n}while(e!==null);Xe=6,Ne=null}function Zm(e,t,n,l,i,r,d,g,w){e.cancelPendingCommit=null;do Hr();while(ot!==0);if((Me&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(r=t.lanes|t.childLanes,r|=oc,Rp(e,n,r,d,g,w),e===Be&&(Ne=Be=null,Ce=0),oa=t,Gn=e,ca=n,ds=r,ms=i,Hm=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,ay(Vi,function(){return Pm(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=_.T,_.T=null,i=Z.p,Z.p=2,d=Me,Me|=4;try{K0(e,t,n)}finally{Me=d,Z.p=i,_.T=l}}ot=1,Km(),Jm(),$m()}}function Km(){if(ot===1){ot=0;var e=Gn,t=oa,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=_.T,_.T=null;var l=Z.p;Z.p=2;var i=Me;Me|=4;try{_m(t,e);var r=_s,d=Uf(e.containerInfo),g=r.focusedElem,w=r.selectionRange;if(d!==g&&g&&g.ownerDocument&&zf(g.ownerDocument.documentElement,g)){if(w!==null&&nc(g)){var z=w.start,q=w.end;if(q===void 0&&(q=z),"selectionStart"in g)g.selectionStart=z,g.selectionEnd=Math.min(q,g.value.length);else{var Q=g.ownerDocument||document,U=Q&&Q.defaultView||window;if(U.getSelection){var H=U.getSelection(),he=g.textContent.length,de=Math.min(w.start,he),Ue=w.end===void 0?de:Math.min(w.end,he);!H.extend&&de>Ue&&(d=Ue,Ue=de,de=d);var j=Df(g,de),A=Df(g,Ue);if(j&&A&&(H.rangeCount!==1||H.anchorNode!==j.node||H.anchorOffset!==j.offset||H.focusNode!==A.node||H.focusOffset!==A.offset)){var M=Q.createRange();M.setStart(j.node,j.offset),H.removeAllRanges(),de>Ue?(H.addRange(M),H.extend(A.node,A.offset)):(M.setEnd(A.node,A.offset),H.addRange(M))}}}}for(Q=[],H=g;H=H.parentNode;)H.nodeType===1&&Q.push({element:H,left:H.scrollLeft,top:H.scrollTop});for(typeof g.focus=="function"&&g.focus(),g=0;g<Q.length;g++){var X=Q[g];X.element.scrollLeft=X.left,X.element.scrollTop=X.top}}Jr=!!js,_s=js=null}finally{Me=i,Z.p=l,_.T=n}}e.current=t,ot=2}}function Jm(){if(ot===2){ot=0;var e=Gn,t=oa,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=_.T,_.T=null;var l=Z.p;Z.p=2;var i=Me;Me|=4;try{Am(e,t.alternate,t)}finally{Me=i,Z.p=l,_.T=n}}ot=3}}function $m(){if(ot===4||ot===3){ot=0,il();var e=Gn,t=oa,n=ca,l=Hm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ot=5:(ot=0,oa=Gn=null,Wm(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(Yn=null),Uo(n),t=t.stateNode,Et&&typeof Et.onCommitFiberRoot=="function")try{Et.onCommitFiberRoot(Ra,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=_.T,i=Z.p,Z.p=2,_.T=null;try{for(var r=e.onRecoverableError,d=0;d<l.length;d++){var g=l[d];r(g.value,{componentStack:g.stack})}}finally{_.T=t,Z.p=i}}(ca&3)!==0&&Hr(),tn(e),i=e.pendingLanes,(n&4194090)!==0&&(i&42)!==0?e===hs?gi++:(gi=0,hs=e):gi=0,pi(0)}}function Wm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Wa(t)))}function Hr(e){return Km(),Jm(),$m(),Pm()}function Pm(){if(ot!==5)return!1;var e=Gn,t=ds;ds=0;var n=Uo(ca),l=_.T,i=Z.p;try{Z.p=32>n?32:n,_.T=null,n=ms,ms=null;var r=Gn,d=ca;if(ot=0,oa=Gn=null,ca=0,(Me&6)!==0)throw Error(c(331));var g=Me;if(Me|=4,zm(r.current),Mm(r,r.current,d,n),Me=g,pi(0,!1),Et&&typeof Et.onPostCommitFiberRoot=="function")try{Et.onPostCommitFiberRoot(Ra,r)}catch{}return!0}finally{Z.p=i,_.T=l,Wm(e,t)}}function Fm(e,t,n){t=Ht(n,t),t=Zc(e.stateNode,t,2),e=Dn(e,t,2),e!==null&&(za(e,2),tn(e))}function He(e,t,n){if(e.tag===3)Fm(e,e,n);else for(;t!==null;){if(t.tag===3){Fm(t,e,n);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Yn===null||!Yn.has(l))){e=Ht(n,e),n=nm(2),l=Dn(t,n,2),l!==null&&(lm(n,l,t,e),za(l,2),tn(l));break}}t=t.return}}function ys(e,t,n){var l=e.pingCache;if(l===null){l=e.pingCache=new W0;var i=new Set;l.set(t,i)}else i=l.get(t),i===void 0&&(i=new Set,l.set(t,i));i.has(n)||(cs=!0,i.add(n),e=ty.bind(null,e,t,n),t.then(e,e))}function ty(e,t,n){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Be===e&&(Ce&n)===n&&(Xe===4||Xe===3&&(Ce&62914560)===Ce&&300>ut()-fs?(Me&2)===0&&sa(e,0):ss|=n,ra===Ce&&(ra=0)),tn(e)}function Im(e,t){t===0&&(t=Ju()),e=Ql(e,t),e!==null&&(za(e,t),tn(e))}function ny(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Im(e,n)}function ly(e,t){var n=0;switch(e.tag){case 13:var l=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(c(314))}l!==null&&l.delete(t),Im(e,n)}function ay(e,t){return it(e,t)}var Lr=null,fa=null,xs=!1,Br=!1,bs=!1,Nl=0;function tn(e){e!==fa&&e.next===null&&(fa===null?Lr=fa=e:fa=fa.next=e),Br=!0,xs||(xs=!0,ry())}function pi(e,t){if(!bs&&Br){bs=!0;do for(var n=!1,l=Lr;l!==null;){if(e!==0){var i=l.pendingLanes;if(i===0)var r=0;else{var d=l.suspendedLanes,g=l.pingedLanes;r=(1<<31-Nt(42|e)+1)-1,r&=i&~(d&~g),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,lh(l,r))}else r=Ce,r=Xi(l,l===Be?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(r&3)===0||Da(l,r)||(n=!0,lh(l,r));l=l.next}while(n);bs=!1}}function iy(){eh()}function eh(){Br=xs=!1;var e=0;Nl!==0&&(hy()&&(e=Nl),Nl=0);for(var t=ut(),n=null,l=Lr;l!==null;){var i=l.next,r=th(l,t);r===0?(l.next=null,n===null?Lr=i:n.next=i,i===null&&(fa=n)):(n=l,(e!==0||(r&3)!==0)&&(Br=!0)),l=i}pi(e)}function th(e,t){for(var n=e.suspendedLanes,l=e.pingedLanes,i=e.expirationTimes,r=e.pendingLanes&-62914561;0<r;){var d=31-Nt(r),g=1<<d,w=i[d];w===-1?((g&n)===0||(g&l)!==0)&&(i[d]=Mp(g,t)):w<=t&&(e.expiredLanes|=g),r&=~g}if(t=Be,n=Ce,n=Xi(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,n===0||e===t&&(Re===2||Re===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&gt(l),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Da(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(l!==null&&gt(l),Uo(n)){case 2:case 8:n=Qu;break;case 32:n=Vi;break;case 268435456:n=Zu;break;default:n=Vi}return l=nh.bind(null,e),n=it(n,l),e.callbackPriority=t,e.callbackNode=n,t}return l!==null&&l!==null&&gt(l),e.callbackPriority=2,e.callbackNode=null,2}function nh(e,t){if(ot!==0&&ot!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Hr()&&e.callbackNode!==n)return null;var l=Ce;return l=Xi(e,e===Be?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Bm(e,l,t),th(e,ut()),e.callbackNode!=null&&e.callbackNode===n?nh.bind(null,e):null)}function lh(e,t){if(Hr())return null;Bm(e,t,!0)}function ry(){gy(function(){(Me&6)!==0?it(rl,iy):eh()})}function Ss(){return Nl===0&&(Nl=Ku()),Nl}function ah(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:$i(""+e)}function ih(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function oy(e,t,n,l,i){if(t==="submit"&&n&&n.stateNode===i){var r=ah((i[pt]||null).action),d=l.submitter;d&&(t=(t=d[pt]||null)?ah(t.formAction):d.getAttribute("formAction"),t!==null&&(r=t,d=null));var g=new Ii("action","action",null,l,i);e.push({event:g,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Nl!==0){var w=d?ih(i,d):new FormData(i);Vc(n,{pending:!0,data:w,method:i.method,action:r},null,w)}}else typeof r=="function"&&(g.preventDefault(),w=d?ih(i,d):new FormData(i),Vc(n,{pending:!0,data:w,method:i.method,action:r},r,w))},currentTarget:i}]})}}for(var ws=0;ws<rc.length;ws++){var Es=rc[ws],cy=Es.toLowerCase(),sy=Es[0].toUpperCase()+Es.slice(1);Gt(cy,"on"+sy)}Gt(Bf,"onAnimationEnd"),Gt(kf,"onAnimationIteration"),Gt(qf,"onAnimationStart"),Gt("dblclick","onDoubleClick"),Gt("focusin","onFocus"),Gt("focusout","onBlur"),Gt(T0,"onTransitionRun"),Gt(C0,"onTransitionStart"),Gt(j0,"onTransitionCancel"),Gt(Vf,"onTransitionEnd"),Ul("onMouseEnter",["mouseout","mouseover"]),Ul("onMouseLeave",["mouseout","mouseover"]),Ul("onPointerEnter",["pointerout","pointerover"]),Ul("onPointerLeave",["pointerout","pointerover"]),cl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),cl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),cl("onBeforeInput",["compositionend","keypress","textInput","paste"]),cl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),cl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),cl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var yi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),uy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(yi));function rh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var l=e[n],i=l.event;l=l.listeners;e:{var r=void 0;if(t)for(var d=l.length-1;0<=d;d--){var g=l[d],w=g.instance,z=g.currentTarget;if(g=g.listener,w!==r&&i.isPropagationStopped())break e;r=g,i.currentTarget=z;try{r(i)}catch(q){Ar(q)}i.currentTarget=null,r=w}else for(d=0;d<l.length;d++){if(g=l[d],w=g.instance,z=g.currentTarget,g=g.listener,w!==r&&i.isPropagationStopped())break e;r=g,i.currentTarget=z;try{r(i)}catch(q){Ar(q)}i.currentTarget=null,r=w}}}}function Ae(e,t){var n=t[Ho];n===void 0&&(n=t[Ho]=new Set);var l=e+"__bubble";n.has(l)||(oh(t,e,2,!1),n.add(l))}function Ns(e,t,n){var l=0;t&&(l|=4),oh(n,e,l,t)}var kr="_reactListening"+Math.random().toString(36).slice(2);function As(e){if(!e[kr]){e[kr]=!0,Iu.forEach(function(n){n!=="selectionchange"&&(uy.has(n)||Ns(n,!1,e),Ns(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[kr]||(t[kr]=!0,Ns("selectionchange",!1,t))}}function oh(e,t,n,l){switch(Mh(t)){case 2:var i=Ly;break;case 8:i=By;break;default:i=ks}n=i.bind(null,t,n,e),i=void 0,!Ko||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),l?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Ts(e,t,n,l,i){var r=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var d=l.tag;if(d===3||d===4){var g=l.stateNode.containerInfo;if(g===i)break;if(d===4)for(d=l.return;d!==null;){var w=d.tag;if((w===3||w===4)&&d.stateNode.containerInfo===i)return;d=d.return}for(;g!==null;){if(d=Rl(g),d===null)return;if(w=d.tag,w===5||w===6||w===26||w===27){l=r=d;continue e}g=g.parentNode}}l=l.return}hf(function(){var z=r,q=Qo(n),Q=[];e:{var U=Yf.get(e);if(U!==void 0){var H=Ii,he=e;switch(e){case"keypress":if(Pi(n)===0)break e;case"keydown":case"keyup":H=a0;break;case"focusin":he="focus",H=Po;break;case"focusout":he="blur",H=Po;break;case"beforeblur":case"afterblur":H=Po;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":H=pf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":H=Zp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":H=o0;break;case Bf:case kf:case qf:H=$p;break;case Vf:H=s0;break;case"scroll":case"scrollend":H=Xp;break;case"wheel":H=f0;break;case"copy":case"cut":case"paste":H=Pp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":H=xf;break;case"toggle":case"beforetoggle":H=m0}var de=(t&4)!==0,Ue=!de&&(e==="scroll"||e==="scrollend"),j=de?U!==null?U+"Capture":null:U;de=[];for(var A=z,M;A!==null;){var X=A;if(M=X.stateNode,X=X.tag,X!==5&&X!==26&&X!==27||M===null||j===null||(X=La(A,j),X!=null&&de.push(xi(A,X,M))),Ue)break;A=A.return}0<de.length&&(U=new H(U,he,null,n,q),Q.push({event:U,listeners:de}))}}if((t&7)===0){e:{if(U=e==="mouseover"||e==="pointerover",H=e==="mouseout"||e==="pointerout",U&&n!==Xo&&(he=n.relatedTarget||n.fromElement)&&(Rl(he)||he[Ml]))break e;if((H||U)&&(U=q.window===q?q:(U=q.ownerDocument)?U.defaultView||U.parentWindow:window,H?(he=n.relatedTarget||n.toElement,H=z,he=he?Rl(he):null,he!==null&&(Ue=m(he),de=he.tag,he!==Ue||de!==5&&de!==27&&de!==6)&&(he=null)):(H=null,he=z),H!==he)){if(de=pf,X="onMouseLeave",j="onMouseEnter",A="mouse",(e==="pointerout"||e==="pointerover")&&(de=xf,X="onPointerLeave",j="onPointerEnter",A="pointer"),Ue=H==null?U:Ha(H),M=he==null?U:Ha(he),U=new de(X,A+"leave",H,n,q),U.target=Ue,U.relatedTarget=M,X=null,Rl(q)===z&&(de=new de(j,A+"enter",he,n,q),de.target=M,de.relatedTarget=Ue,X=de),Ue=X,H&&he)t:{for(de=H,j=he,A=0,M=de;M;M=da(M))A++;for(M=0,X=j;X;X=da(X))M++;for(;0<A-M;)de=da(de),A--;for(;0<M-A;)j=da(j),M--;for(;A--;){if(de===j||j!==null&&de===j.alternate)break t;de=da(de),j=da(j)}de=null}else de=null;H!==null&&ch(Q,U,H,de,!1),he!==null&&Ue!==null&&ch(Q,Ue,he,de,!0)}}e:{if(U=z?Ha(z):window,H=U.nodeName&&U.nodeName.toLowerCase(),H==="select"||H==="input"&&U.type==="file")var ne=Cf;else if(Af(U))if(jf)ne=E0;else{ne=S0;var Se=b0}else H=U.nodeName,!H||H.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?z&&Go(z.elementType)&&(ne=Cf):ne=w0;if(ne&&(ne=ne(e,z))){Tf(Q,ne,n,q);break e}Se&&Se(e,U,z),e==="focusout"&&z&&U.type==="number"&&z.memoizedProps.value!=null&&Yo(U,"number",U.value)}switch(Se=z?Ha(z):window,e){case"focusin":(Af(Se)||Se.contentEditable==="true")&&(Yl=Se,lc=z,Qa=null);break;case"focusout":Qa=lc=Yl=null;break;case"mousedown":ac=!0;break;case"contextmenu":case"mouseup":case"dragend":ac=!1,Hf(Q,n,q);break;case"selectionchange":if(A0)break;case"keydown":case"keyup":Hf(Q,n,q)}var re;if(Io)e:{switch(e){case"compositionstart":var me="onCompositionStart";break e;case"compositionend":me="onCompositionEnd";break e;case"compositionupdate":me="onCompositionUpdate";break e}me=void 0}else Vl?Ef(e,n)&&(me="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(me="onCompositionStart");me&&(bf&&n.locale!=="ko"&&(Vl||me!=="onCompositionStart"?me==="onCompositionEnd"&&Vl&&(re=vf()):(_n=q,Jo="value"in _n?_n.value:_n.textContent,Vl=!0)),Se=qr(z,me),0<Se.length&&(me=new yf(me,e,null,n,q),Q.push({event:me,listeners:Se}),re?me.data=re:(re=Nf(n),re!==null&&(me.data=re)))),(re=v0?g0(e,n):p0(e,n))&&(me=qr(z,"onBeforeInput"),0<me.length&&(Se=new yf("onBeforeInput","beforeinput",null,n,q),Q.push({event:Se,listeners:me}),Se.data=re)),oy(Q,e,z,n,q)}rh(Q,t)})}function xi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",l=[];e!==null;){var i=e,r=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||r===null||(i=La(e,n),i!=null&&l.unshift(xi(e,i,r)),i=La(e,t),i!=null&&l.push(xi(e,i,r))),e.tag===3)return l;e=e.return}return[]}function da(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function ch(e,t,n,l,i){for(var r=t._reactName,d=[];n!==null&&n!==l;){var g=n,w=g.alternate,z=g.stateNode;if(g=g.tag,w!==null&&w===l)break;g!==5&&g!==26&&g!==27||z===null||(w=z,i?(z=La(n,r),z!=null&&d.unshift(xi(n,z,w))):i||(z=La(n,r),z!=null&&d.push(xi(n,z,w)))),n=n.return}d.length!==0&&e.push({event:t,listeners:d})}var fy=/\r\n?/g,dy=/\u0000|\uFFFD/g;function sh(e){return(typeof e=="string"?e:""+e).replace(fy,`
`).replace(dy,"")}function uh(e,t){return t=sh(t),sh(e)===t}function Vr(){}function ze(e,t,n,l,i,r){switch(n){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||Bl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&Bl(e,""+l);break;case"className":Zi(e,"class",l);break;case"tabIndex":Zi(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Zi(e,n,l);break;case"style":df(e,l,r);break;case"data":if(t!=="object"){Zi(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=$i(""+l),e.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(t!=="input"&&ze(e,t,"name",i.name,i,null),ze(e,t,"formEncType",i.formEncType,i,null),ze(e,t,"formMethod",i.formMethod,i,null),ze(e,t,"formTarget",i.formTarget,i,null)):(ze(e,t,"encType",i.encType,i,null),ze(e,t,"method",i.method,i,null),ze(e,t,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=$i(""+l),e.setAttribute(n,l);break;case"onClick":l!=null&&(e.onclick=Vr);break;case"onScroll":l!=null&&Ae("scroll",e);break;case"onScrollEnd":l!=null&&Ae("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=n}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}n=$i(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""+l):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":l===!0?e.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,l):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(n,l):e.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(n):e.setAttribute(n,l);break;case"popover":Ae("beforetoggle",e),Ae("toggle",e),Qi(e,"popover",l);break;case"xlinkActuate":on(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":on(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":on(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":on(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":on(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":on(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":on(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":on(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":on(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Qi(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Yp.get(n)||n,Qi(e,n,l))}}function Cs(e,t,n,l,i,r){switch(n){case"style":df(e,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=n}}break;case"children":typeof l=="string"?Bl(e,l):(typeof l=="number"||typeof l=="bigint")&&Bl(e,""+l);break;case"onScroll":l!=null&&Ae("scroll",e);break;case"onScrollEnd":l!=null&&Ae("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Vr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ef.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),r=e[pt]||null,r=r!=null?r[n]:null,typeof r=="function"&&e.removeEventListener(t,r,i),typeof l=="function")){typeof r!="function"&&r!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,l,i);break e}n in e?e[n]=l:l===!0?e.setAttribute(n,""):Qi(e,n,l)}}}function ct(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ae("error",e),Ae("load",e);var l=!1,i=!1,r;for(r in n)if(n.hasOwnProperty(r)){var d=n[r];if(d!=null)switch(r){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:ze(e,t,r,d,n,null)}}i&&ze(e,t,"srcSet",n.srcSet,n,null),l&&ze(e,t,"src",n.src,n,null);return;case"input":Ae("invalid",e);var g=r=d=i=null,w=null,z=null;for(l in n)if(n.hasOwnProperty(l)){var q=n[l];if(q!=null)switch(l){case"name":i=q;break;case"type":d=q;break;case"checked":w=q;break;case"defaultChecked":z=q;break;case"value":r=q;break;case"defaultValue":g=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(c(137,t));break;default:ze(e,t,l,q,n,null)}}cf(e,r,g,w,z,d,i,!1),Ki(e);return;case"select":Ae("invalid",e),l=d=r=null;for(i in n)if(n.hasOwnProperty(i)&&(g=n[i],g!=null))switch(i){case"value":r=g;break;case"defaultValue":d=g;break;case"multiple":l=g;default:ze(e,t,i,g,n,null)}t=r,n=d,e.multiple=!!l,t!=null?Ll(e,!!l,t,!1):n!=null&&Ll(e,!!l,n,!0);return;case"textarea":Ae("invalid",e),r=i=l=null;for(d in n)if(n.hasOwnProperty(d)&&(g=n[d],g!=null))switch(d){case"value":l=g;break;case"defaultValue":i=g;break;case"children":r=g;break;case"dangerouslySetInnerHTML":if(g!=null)throw Error(c(91));break;default:ze(e,t,d,g,n,null)}uf(e,l,i,r),Ki(e);return;case"option":for(w in n)if(n.hasOwnProperty(w)&&(l=n[w],l!=null))switch(w){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:ze(e,t,w,l,n,null)}return;case"dialog":Ae("beforetoggle",e),Ae("toggle",e),Ae("cancel",e),Ae("close",e);break;case"iframe":case"object":Ae("load",e);break;case"video":case"audio":for(l=0;l<yi.length;l++)Ae(yi[l],e);break;case"image":Ae("error",e),Ae("load",e);break;case"details":Ae("toggle",e);break;case"embed":case"source":case"link":Ae("error",e),Ae("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(z in n)if(n.hasOwnProperty(z)&&(l=n[z],l!=null))switch(z){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:ze(e,t,z,l,n,null)}return;default:if(Go(t)){for(q in n)n.hasOwnProperty(q)&&(l=n[q],l!==void 0&&Cs(e,t,q,l,n,void 0));return}}for(g in n)n.hasOwnProperty(g)&&(l=n[g],l!=null&&ze(e,t,g,l,n,null))}function my(e,t,n,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,r=null,d=null,g=null,w=null,z=null,q=null;for(H in n){var Q=n[H];if(n.hasOwnProperty(H)&&Q!=null)switch(H){case"checked":break;case"value":break;case"defaultValue":w=Q;default:l.hasOwnProperty(H)||ze(e,t,H,null,l,Q)}}for(var U in l){var H=l[U];if(Q=n[U],l.hasOwnProperty(U)&&(H!=null||Q!=null))switch(U){case"type":r=H;break;case"name":i=H;break;case"checked":z=H;break;case"defaultChecked":q=H;break;case"value":d=H;break;case"defaultValue":g=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(c(137,t));break;default:H!==Q&&ze(e,t,U,H,l,Q)}}Vo(e,d,g,w,z,q,r,i);return;case"select":H=d=g=U=null;for(r in n)if(w=n[r],n.hasOwnProperty(r)&&w!=null)switch(r){case"value":break;case"multiple":H=w;default:l.hasOwnProperty(r)||ze(e,t,r,null,l,w)}for(i in l)if(r=l[i],w=n[i],l.hasOwnProperty(i)&&(r!=null||w!=null))switch(i){case"value":U=r;break;case"defaultValue":g=r;break;case"multiple":d=r;default:r!==w&&ze(e,t,i,r,l,w)}t=g,n=d,l=H,U!=null?Ll(e,!!n,U,!1):!!l!=!!n&&(t!=null?Ll(e,!!n,t,!0):Ll(e,!!n,n?[]:"",!1));return;case"textarea":H=U=null;for(g in n)if(i=n[g],n.hasOwnProperty(g)&&i!=null&&!l.hasOwnProperty(g))switch(g){case"value":break;case"children":break;default:ze(e,t,g,null,l,i)}for(d in l)if(i=l[d],r=n[d],l.hasOwnProperty(d)&&(i!=null||r!=null))switch(d){case"value":U=i;break;case"defaultValue":H=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(c(91));break;default:i!==r&&ze(e,t,d,i,l,r)}sf(e,U,H);return;case"option":for(var he in n)if(U=n[he],n.hasOwnProperty(he)&&U!=null&&!l.hasOwnProperty(he))switch(he){case"selected":e.selected=!1;break;default:ze(e,t,he,null,l,U)}for(w in l)if(U=l[w],H=n[w],l.hasOwnProperty(w)&&U!==H&&(U!=null||H!=null))switch(w){case"selected":e.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:ze(e,t,w,U,l,H)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var de in n)U=n[de],n.hasOwnProperty(de)&&U!=null&&!l.hasOwnProperty(de)&&ze(e,t,de,null,l,U);for(z in l)if(U=l[z],H=n[z],l.hasOwnProperty(z)&&U!==H&&(U!=null||H!=null))switch(z){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(c(137,t));break;default:ze(e,t,z,U,l,H)}return;default:if(Go(t)){for(var Ue in n)U=n[Ue],n.hasOwnProperty(Ue)&&U!==void 0&&!l.hasOwnProperty(Ue)&&Cs(e,t,Ue,void 0,l,U);for(q in l)U=l[q],H=n[q],!l.hasOwnProperty(q)||U===H||U===void 0&&H===void 0||Cs(e,t,q,U,l,H);return}}for(var j in n)U=n[j],n.hasOwnProperty(j)&&U!=null&&!l.hasOwnProperty(j)&&ze(e,t,j,null,l,U);for(Q in l)U=l[Q],H=n[Q],!l.hasOwnProperty(Q)||U===H||U==null&&H==null||ze(e,t,Q,U,l,H)}var js=null,_s=null;function Yr(e){return e.nodeType===9?e:e.ownerDocument}function fh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function dh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Os(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ms=null;function hy(){var e=window.event;return e&&e.type==="popstate"?e===Ms?!1:(Ms=e,!0):(Ms=null,!1)}var mh=typeof setTimeout=="function"?setTimeout:void 0,vy=typeof clearTimeout=="function"?clearTimeout:void 0,hh=typeof Promise=="function"?Promise:void 0,gy=typeof queueMicrotask=="function"?queueMicrotask:typeof hh<"u"?function(e){return hh.resolve(null).then(e).catch(py)}:mh;function py(e){setTimeout(function(){throw e})}function Qn(e){return e==="head"}function vh(e,t){var n=t,l=0,i=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<l&&8>l){n=l;var d=e.ownerDocument;if(n&1&&bi(d.documentElement),n&2&&bi(d.body),n&4)for(n=d.head,bi(n),d=n.firstChild;d;){var g=d.nextSibling,w=d.nodeName;d[Ua]||w==="SCRIPT"||w==="STYLE"||w==="LINK"&&d.rel.toLowerCase()==="stylesheet"||n.removeChild(d),d=g}}if(i===0){e.removeChild(r),ji(t);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=r}while(n);ji(t)}function Rs(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Rs(n),Lo(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function yy(e,t,n,l){for(;e.nodeType===1;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Ua])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(r=e.getAttribute("rel"),r==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(r!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(r=e.getAttribute("src"),(r!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&r&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var r=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===r)return e}else return e;if(e=Qt(e.nextSibling),e===null)break}return null}function xy(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Qt(e.nextSibling),e===null))return null;return e}function Ds(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function by(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var l=function(){t(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var zs=null;function gh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function ph(e,t,n){switch(t=Yr(n),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function bi(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Lo(e)}var Yt=new Map,yh=new Set;function Gr(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Sn=Z.d;Z.d={f:Sy,r:wy,D:Ey,C:Ny,L:Ay,m:Ty,X:jy,S:Cy,M:_y};function Sy(){var e=Sn.f(),t=zr();return e||t}function wy(e){var t=Dl(e);t!==null&&t.tag===5&&t.type==="form"?Bd(t):Sn.r(e)}var ma=typeof document>"u"?null:document;function xh(e,t,n){var l=ma;if(l&&typeof t=="string"&&t){var i=Ut(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),yh.has(i)||(yh.add(i),e={rel:e,crossOrigin:n,href:t},l.querySelector(i)===null&&(t=l.createElement("link"),ct(t,"link",e),tt(t),l.head.appendChild(t)))}}function Ey(e){Sn.D(e),xh("dns-prefetch",e,null)}function Ny(e,t){Sn.C(e,t),xh("preconnect",e,t)}function Ay(e,t,n){Sn.L(e,t,n);var l=ma;if(l&&e&&t){var i='link[rel="preload"][as="'+Ut(t)+'"]';t==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+Ut(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+Ut(n.imageSizes)+'"]')):i+='[href="'+Ut(e)+'"]';var r=i;switch(t){case"style":r=ha(e);break;case"script":r=va(e)}Yt.has(r)||(e=S({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Yt.set(r,e),l.querySelector(i)!==null||t==="style"&&l.querySelector(Si(r))||t==="script"&&l.querySelector(wi(r))||(t=l.createElement("link"),ct(t,"link",e),tt(t),l.head.appendChild(t)))}}function Ty(e,t){Sn.m(e,t);var n=ma;if(n&&e){var l=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Ut(l)+'"][href="'+Ut(e)+'"]',r=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=va(e)}if(!Yt.has(r)&&(e=S({rel:"modulepreload",href:e},t),Yt.set(r,e),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(wi(r)))return}l=n.createElement("link"),ct(l,"link",e),tt(l),n.head.appendChild(l)}}}function Cy(e,t,n){Sn.S(e,t,n);var l=ma;if(l&&e){var i=zl(l).hoistableStyles,r=ha(e);t=t||"default";var d=i.get(r);if(!d){var g={loading:0,preload:null};if(d=l.querySelector(Si(r)))g.loading=5;else{e=S({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Yt.get(r))&&Us(e,n);var w=d=l.createElement("link");tt(w),ct(w,"link",e),w._p=new Promise(function(z,q){w.onload=z,w.onerror=q}),w.addEventListener("load",function(){g.loading|=1}),w.addEventListener("error",function(){g.loading|=2}),g.loading|=4,Xr(d,t,l)}d={type:"stylesheet",instance:d,count:1,state:g},i.set(r,d)}}}function jy(e,t){Sn.X(e,t);var n=ma;if(n&&e){var l=zl(n).hoistableScripts,i=va(e),r=l.get(i);r||(r=n.querySelector(wi(i)),r||(e=S({src:e,async:!0},t),(t=Yt.get(i))&&Hs(e,t),r=n.createElement("script"),tt(r),ct(r,"link",e),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function _y(e,t){Sn.M(e,t);var n=ma;if(n&&e){var l=zl(n).hoistableScripts,i=va(e),r=l.get(i);r||(r=n.querySelector(wi(i)),r||(e=S({src:e,async:!0,type:"module"},t),(t=Yt.get(i))&&Hs(e,t),r=n.createElement("script"),tt(r),ct(r,"link",e),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function bh(e,t,n,l){var i=(i=ce.current)?Gr(i):null;if(!i)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=ha(n.href),n=zl(i).hoistableStyles,l=n.get(t),l||(l={type:"style",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=ha(n.href);var r=zl(i).hoistableStyles,d=r.get(e);if(d||(i=i.ownerDocument||i,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(e,d),(r=i.querySelector(Si(e)))&&!r._p&&(d.instance=r,d.state.loading=5),Yt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Yt.set(e,n),r||Oy(i,e,n,d.state))),t&&l===null)throw Error(c(528,""));return d}if(t&&l!==null)throw Error(c(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=va(n),n=zl(i).hoistableScripts,l=n.get(t),l||(l={type:"script",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function ha(e){return'href="'+Ut(e)+'"'}function Si(e){return'link[rel="stylesheet"]['+e+"]"}function Sh(e){return S({},e,{"data-precedence":e.precedence,precedence:null})}function Oy(e,t,n,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),ct(t,"link",n),tt(t),e.head.appendChild(t))}function va(e){return'[src="'+Ut(e)+'"]'}function wi(e){return"script[async]"+e}function wh(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Ut(n.href)+'"]');if(l)return t.instance=l,tt(l),l;var i=S({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),tt(l),ct(l,"style",i),Xr(l,n.precedence,e),t.instance=l;case"stylesheet":i=ha(n.href);var r=e.querySelector(Si(i));if(r)return t.state.loading|=4,t.instance=r,tt(r),r;l=Sh(n),(i=Yt.get(i))&&Us(l,i),r=(e.ownerDocument||e).createElement("link"),tt(r);var d=r;return d._p=new Promise(function(g,w){d.onload=g,d.onerror=w}),ct(r,"link",l),t.state.loading|=4,Xr(r,n.precedence,e),t.instance=r;case"script":return r=va(n.src),(i=e.querySelector(wi(r)))?(t.instance=i,tt(i),i):(l=n,(i=Yt.get(r))&&(l=S({},n),Hs(l,i)),e=e.ownerDocument||e,i=e.createElement("script"),tt(i),ct(i,"link",l),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,Xr(l,n.precedence,e));return t.instance}function Xr(e,t,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,r=i,d=0;d<l.length;d++){var g=l[d];if(g.dataset.precedence===t)r=g;else if(r!==i)break}r?r.parentNode.insertBefore(e,r.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Us(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Hs(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Qr=null;function Eh(e,t,n){if(Qr===null){var l=new Map,i=Qr=new Map;i.set(n,l)}else i=Qr,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(e))return l;for(l.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var r=n[i];if(!(r[Ua]||r[ft]||e==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var d=r.getAttribute(t)||"";d=e+d;var g=l.get(d);g?g.push(r):l.set(d,[r])}}return l}function Nh(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function My(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Ah(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Ei=null;function Ry(){}function Dy(e,t,n){if(Ei===null)throw Error(c(475));var l=Ei;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=ha(n.href),r=e.querySelector(Si(i));if(r){e=r._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Zr.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=r,tt(r);return}r=e.ownerDocument||e,n=Sh(n),(i=Yt.get(i))&&Us(n,i),r=r.createElement("link"),tt(r);var d=r;d._p=new Promise(function(g,w){d.onload=g,d.onerror=w}),ct(r,"link",n),t.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Zr.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function zy(){if(Ei===null)throw Error(c(475));var e=Ei;return e.stylesheets&&e.count===0&&Ls(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Ls(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Zr(){if(this.count--,this.count===0){if(this.stylesheets)Ls(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Kr=null;function Ls(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Kr=new Map,t.forEach(Uy,e),Kr=null,Zr.call(e))}function Uy(e,t){if(!(t.state.loading&4)){var n=Kr.get(e);if(n)var l=n.get(null);else{n=new Map,Kr.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<i.length;r++){var d=i[r];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(n.set(d.dataset.precedence,d),l=d)}l&&n.set(null,l)}i=t.instance,d=i.getAttribute("data-precedence"),r=n.get(d)||l,r===l&&n.set(null,i),n.set(d,i),this.count++,l=Zr.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),r?r.parentNode.insertBefore(i,r.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Ni={$$typeof:B,Provider:null,Consumer:null,_currentValue:k,_currentValue2:k,_threadCount:0};function Hy(e,t,n,l,i,r,d,g){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Do(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Do(0),this.hiddenUpdates=Do(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=r,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=g,this.incompleteTransitions=new Map}function Th(e,t,n,l,i,r,d,g,w,z,q,Q){return e=new Hy(e,t,n,d,g,w,z,Q),t=1,r===!0&&(t|=24),r=Tt(3,null,null,t),e.current=r,r.stateNode=e,t=yc(),t.refCount++,e.pooledCache=t,t.refCount++,r.memoizedState={element:l,isDehydrated:n,cache:t},wc(r),e}function Ch(e){return e?(e=Zl,e):Zl}function jh(e,t,n,l,i,r){i=Ch(i),l.context===null?l.context=i:l.pendingContext=i,l=Rn(t),l.payload={element:n},r=r===void 0?null:r,r!==null&&(l.callback=r),n=Dn(e,l,t),n!==null&&(Mt(n,e,t),ei(n,e,t))}function _h(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Bs(e,t){_h(e,t),(e=e.alternate)&&_h(e,t)}function Oh(e){if(e.tag===13){var t=Ql(e,67108864);t!==null&&Mt(t,e,67108864),Bs(e,67108864)}}var Jr=!0;function Ly(e,t,n,l){var i=_.T;_.T=null;var r=Z.p;try{Z.p=2,ks(e,t,n,l)}finally{Z.p=r,_.T=i}}function By(e,t,n,l){var i=_.T;_.T=null;var r=Z.p;try{Z.p=8,ks(e,t,n,l)}finally{Z.p=r,_.T=i}}function ks(e,t,n,l){if(Jr){var i=qs(l);if(i===null)Ts(e,t,l,$r,n),Rh(e,l);else if(qy(i,e,t,n,l))l.stopPropagation();else if(Rh(e,l),t&4&&-1<ky.indexOf(e)){for(;i!==null;){var r=Dl(i);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var d=ol(r.pendingLanes);if(d!==0){var g=r;for(g.pendingLanes|=2,g.entangledLanes|=2;d;){var w=1<<31-Nt(d);g.entanglements[1]|=w,d&=~w}tn(r),(Me&6)===0&&(Rr=ut()+500,pi(0))}}break;case 13:g=Ql(r,2),g!==null&&Mt(g,r,2),zr(),Bs(r,2)}if(r=qs(l),r===null&&Ts(e,t,l,$r,n),r===i)break;i=r}i!==null&&l.stopPropagation()}else Ts(e,t,l,null,n)}}function qs(e){return e=Qo(e),Vs(e)}var $r=null;function Vs(e){if($r=null,e=Rl(e),e!==null){var t=m(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=h(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return $r=e,null}function Mh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ro()){case rl:return 2;case Qu:return 8;case Vi:case Ap:return 32;case Zu:return 268435456;default:return 32}default:return 32}}var Ys=!1,Zn=null,Kn=null,Jn=null,Ai=new Map,Ti=new Map,$n=[],ky="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Rh(e,t){switch(e){case"focusin":case"focusout":Zn=null;break;case"dragenter":case"dragleave":Kn=null;break;case"mouseover":case"mouseout":Jn=null;break;case"pointerover":case"pointerout":Ai.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ti.delete(t.pointerId)}}function Ci(e,t,n,l,i,r){return e===null||e.nativeEvent!==r?(e={blockedOn:t,domEventName:n,eventSystemFlags:l,nativeEvent:r,targetContainers:[i]},t!==null&&(t=Dl(t),t!==null&&Oh(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function qy(e,t,n,l,i){switch(t){case"focusin":return Zn=Ci(Zn,e,t,n,l,i),!0;case"dragenter":return Kn=Ci(Kn,e,t,n,l,i),!0;case"mouseover":return Jn=Ci(Jn,e,t,n,l,i),!0;case"pointerover":var r=i.pointerId;return Ai.set(r,Ci(Ai.get(r)||null,e,t,n,l,i)),!0;case"gotpointercapture":return r=i.pointerId,Ti.set(r,Ci(Ti.get(r)||null,e,t,n,l,i)),!0}return!1}function Dh(e){var t=Rl(e.target);if(t!==null){var n=m(t);if(n!==null){if(t=n.tag,t===13){if(t=h(n),t!==null){e.blockedOn=t,Dp(e.priority,function(){if(n.tag===13){var l=Ot();l=zo(l);var i=Ql(n,l);i!==null&&Mt(i,n,l),Bs(n,l)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Wr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=qs(e.nativeEvent);if(n===null){n=e.nativeEvent;var l=new n.constructor(n.type,n);Xo=l,n.target.dispatchEvent(l),Xo=null}else return t=Dl(n),t!==null&&Oh(t),e.blockedOn=n,!1;t.shift()}return!0}function zh(e,t,n){Wr(e)&&n.delete(t)}function Vy(){Ys=!1,Zn!==null&&Wr(Zn)&&(Zn=null),Kn!==null&&Wr(Kn)&&(Kn=null),Jn!==null&&Wr(Jn)&&(Jn=null),Ai.forEach(zh),Ti.forEach(zh)}function Pr(e,t){e.blockedOn===t&&(e.blockedOn=null,Ys||(Ys=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Vy)))}var Fr=null;function Uh(e){Fr!==e&&(Fr=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Fr===e&&(Fr=null);for(var t=0;t<e.length;t+=3){var n=e[t],l=e[t+1],i=e[t+2];if(typeof l!="function"){if(Vs(l||n)===null)continue;break}var r=Dl(n);r!==null&&(e.splice(t,3),t-=3,Vc(r,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function ji(e){function t(w){return Pr(w,e)}Zn!==null&&Pr(Zn,e),Kn!==null&&Pr(Kn,e),Jn!==null&&Pr(Jn,e),Ai.forEach(t),Ti.forEach(t);for(var n=0;n<$n.length;n++){var l=$n[n];l.blockedOn===e&&(l.blockedOn=null)}for(;0<$n.length&&(n=$n[0],n.blockedOn===null);)Dh(n),n.blockedOn===null&&$n.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],r=n[l+1],d=i[pt]||null;if(typeof r=="function")d||Uh(n);else if(d){var g=null;if(r&&r.hasAttribute("formAction")){if(i=r,d=r[pt]||null)g=d.formAction;else if(Vs(i)!==null)continue}else g=d.action;typeof g=="function"?n[l+1]=g:(n.splice(l,3),l-=3),Uh(n)}}}function Gs(e){this._internalRoot=e}Ir.prototype.render=Gs.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var n=t.current,l=Ot();jh(n,l,e,t,null,null)},Ir.prototype.unmount=Gs.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;jh(e.current,2,null,e,null,null),zr(),t[Ml]=null}};function Ir(e){this._internalRoot=e}Ir.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pu();e={blockedOn:null,target:e,priority:t};for(var n=0;n<$n.length&&t!==0&&t<$n[n].priority;n++);$n.splice(n,0,e),n===0&&Dh(e)}};var Hh=o.version;if(Hh!=="19.1.0")throw Error(c(527,Hh,"19.1.0"));Z.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=y(t),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var Yy={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:_,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var eo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!eo.isDisabled&&eo.supportsFiber)try{Ra=eo.inject(Yy),Et=eo}catch{}}return Oi.createRoot=function(e,t){if(!f(e))throw Error(c(299));var n=!1,l="",i=Fd,r=Id,d=em,g=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(r=t.onCaughtError),t.onRecoverableError!==void 0&&(d=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(g=t.unstable_transitionCallbacks)),t=Th(e,1,!1,null,null,n,l,i,r,d,g,null),e[Ml]=t.current,As(e),new Gs(t)},Oi.hydrateRoot=function(e,t,n){if(!f(e))throw Error(c(299));var l=!1,i="",r=Fd,d=Id,g=em,w=null,z=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(d=n.onCaughtError),n.onRecoverableError!==void 0&&(g=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(w=n.unstable_transitionCallbacks),n.formState!==void 0&&(z=n.formState)),t=Th(e,1,!0,t,n??null,l,i,r,d,g,w,z),t.context=Ch(null),n=t.current,l=Ot(),l=zo(l),i=Rn(l),i.callback=null,Dn(n,i,l),n=l,t.current.lanes=n,za(t,n),tn(t),e[Ml]=t.current,As(e),new Ir(t)},Oi.version="19.1.0",Oi}var Zh;function Fy(){if(Zh)return Zs.exports;Zh=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(o){console.error(o)}}return a(),Zs.exports=Py(),Zs.exports}var Iy=Fy();/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ex=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),tx=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(o,s,c)=>c?c.toUpperCase():s.toLowerCase()),Kh=a=>{const o=tx(a);return o.charAt(0).toUpperCase()+o.slice(1)},Cv=(...a)=>a.filter((o,s,c)=>!!o&&o.trim()!==""&&c.indexOf(o)===s).join(" ").trim(),nx=a=>{for(const o in a)if(o.startsWith("aria-")||o==="role"||o==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var lx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ax=x.forwardRef(({color:a="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:c,className:f="",children:m,iconNode:h,...v},y)=>x.createElement("svg",{ref:y,...lx,width:o,height:o,stroke:a,strokeWidth:c?Number(s)*24/Number(o):s,className:Cv("lucide",f),...!m&&!nx(v)&&{"aria-hidden":"true"},...v},[...h.map(([p,S])=>x.createElement(p,S)),...Array.isArray(m)?m:[m]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qe=(a,o)=>{const s=x.forwardRef(({className:c,...f},m)=>x.createElement(ax,{ref:m,iconNode:o,className:Cv(`lucide-${ex(Kh(a))}`,`lucide-${a}`,c),...f}));return s.displayName=Kh(a),s};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ix=[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]],rx=qe("bot",ix);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ox=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],cx=qe("chart-column",ox);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sx=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],ux=qe("check",sx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fx=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],jv=qe("chevron-down",fx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dx=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],mx=qe("chevron-up",dx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hx=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],vx=qe("copy",hx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gx=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],px=qe("dollar-sign",gx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yx=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]],xx=qe("download",yx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bx=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],so=qe("file-text",bx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sx=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],wx=qe("image",Sx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ex=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Nx=qe("mail",Ex);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ax=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Tx=qe("menu",Ax);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cx=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],Jh=qe("message-square",Cx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jx=[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],_x=qe("mic",jx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ox=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],_v=qe("phone",Ox);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mx=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],vo=qe("plus",Mx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rx=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],ou=qe("search",Rx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dx=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],zx=qe("settings",Dx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ux=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],Ov=qe("square-pen",Ux);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hx=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],Lx=qe("tag",Hx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bx=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Mv=qe("trash-2",Bx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kx=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],uo=qe("users",kx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qx=[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]],Vx=qe("video",qx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yx=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Rv=qe("x",Yx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gx=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],Xx=qe("zap",Gx);function $h(a,o){if(typeof a=="function")return a(o);a!=null&&(a.current=o)}function Qx(...a){return o=>{let s=!1;const c=a.map(f=>{const m=$h(f,o);return!s&&typeof m=="function"&&(s=!0),m});if(s)return()=>{for(let f=0;f<c.length;f++){const m=c[f];typeof m=="function"?m():$h(a[f],null)}}}}function Qe(...a){return x.useCallback(Qx(...a),a)}function Aa(a){const o=Zx(a),s=x.forwardRef((c,f)=>{const{children:m,...h}=c,v=x.Children.toArray(m),y=v.find(Jx);if(y){const p=y.props.children,S=v.map(N=>N===y?x.Children.count(p)>1?x.Children.only(null):x.isValidElement(p)?p.props.children:null:N);return u.jsx(o,{...h,ref:f,children:x.isValidElement(p)?x.cloneElement(p,void 0,S):null})}return u.jsx(o,{...h,ref:f,children:m})});return s.displayName=`${a}.Slot`,s}var Dv=Aa("Slot");function Zx(a){const o=x.forwardRef((s,c)=>{const{children:f,...m}=s,h=x.isValidElement(f)?Wx(f):void 0,v=Qe(h,c);if(x.isValidElement(f)){const y=$x(m,f.props);return f.type!==x.Fragment&&(y.ref=v),x.cloneElement(f,y)}return x.Children.count(f)>1?x.Children.only(null):null});return o.displayName=`${a}.SlotClone`,o}var Kx=Symbol("radix.slottable");function Jx(a){return x.isValidElement(a)&&typeof a.type=="function"&&"__radixId"in a.type&&a.type.__radixId===Kx}function $x(a,o){const s={...o};for(const c in o){const f=a[c],m=o[c];/^on[A-Z]/.test(c)?f&&m?s[c]=(...v)=>{const y=m(...v);return f(...v),y}:f&&(s[c]=f):c==="style"?s[c]={...f,...m}:c==="className"&&(s[c]=[f,m].filter(Boolean).join(" "))}return{...a,...s}}function Wx(a){var c,f;let o=(c=Object.getOwnPropertyDescriptor(a.props,"ref"))==null?void 0:c.get,s=o&&"isReactWarning"in o&&o.isReactWarning;return s?a.ref:(o=(f=Object.getOwnPropertyDescriptor(a,"ref"))==null?void 0:f.get,s=o&&"isReactWarning"in o&&o.isReactWarning,s?a.props.ref:a.props.ref||a.ref)}function zv(a){var o,s,c="";if(typeof a=="string"||typeof a=="number")c+=a;else if(typeof a=="object")if(Array.isArray(a)){var f=a.length;for(o=0;o<f;o++)a[o]&&(s=zv(a[o]))&&(c&&(c+=" "),c+=s)}else for(s in a)a[s]&&(c&&(c+=" "),c+=s);return c}function Uv(){for(var a,o,s=0,c="",f=arguments.length;s<f;s++)(a=arguments[s])&&(o=zv(a))&&(c&&(c+=" "),c+=o);return c}const Wh=a=>typeof a=="boolean"?`${a}`:a===0?"0":a,Ph=Uv,Hv=(a,o)=>s=>{var c;if((o==null?void 0:o.variants)==null)return Ph(a,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:f,defaultVariants:m}=o,h=Object.keys(f).map(p=>{const S=s==null?void 0:s[p],N=m==null?void 0:m[p];if(S===null)return null;const C=Wh(S)||Wh(N);return f[p][C]}),v=s&&Object.entries(s).reduce((p,S)=>{let[N,C]=S;return C===void 0||(p[N]=C),p},{}),y=o==null||(c=o.compoundVariants)===null||c===void 0?void 0:c.reduce((p,S)=>{let{class:N,className:C,...O}=S;return Object.entries(O).every(R=>{let[b,T]=R;return Array.isArray(T)?T.includes({...m,...v}[b]):{...m,...v}[b]===T})?[...p,N,C]:p},[]);return Ph(a,h,y,s==null?void 0:s.class,s==null?void 0:s.className)},Cu="-",Px=a=>{const o=Ix(a),{conflictingClassGroups:s,conflictingClassGroupModifiers:c}=a;return{getClassGroupId:h=>{const v=h.split(Cu);return v[0]===""&&v.length!==1&&v.shift(),Lv(v,o)||Fx(h)},getConflictingClassGroupIds:(h,v)=>{const y=s[h]||[];return v&&c[h]?[...y,...c[h]]:y}}},Lv=(a,o)=>{var h;if(a.length===0)return o.classGroupId;const s=a[0],c=o.nextPart.get(s),f=c?Lv(a.slice(1),c):void 0;if(f)return f;if(o.validators.length===0)return;const m=a.join(Cu);return(h=o.validators.find(({validator:v})=>v(m)))==null?void 0:h.classGroupId},Fh=/^\[(.+)\]$/,Fx=a=>{if(Fh.test(a)){const o=Fh.exec(a)[1],s=o==null?void 0:o.substring(0,o.indexOf(":"));if(s)return"arbitrary.."+s}},Ix=a=>{const{theme:o,classGroups:s}=a,c={nextPart:new Map,validators:[]};for(const f in s)cu(s[f],c,f,o);return c},cu=(a,o,s,c)=>{a.forEach(f=>{if(typeof f=="string"){const m=f===""?o:Ih(o,f);m.classGroupId=s;return}if(typeof f=="function"){if(eb(f)){cu(f(c),o,s,c);return}o.validators.push({validator:f,classGroupId:s});return}Object.entries(f).forEach(([m,h])=>{cu(h,Ih(o,m),s,c)})})},Ih=(a,o)=>{let s=a;return o.split(Cu).forEach(c=>{s.nextPart.has(c)||s.nextPart.set(c,{nextPart:new Map,validators:[]}),s=s.nextPart.get(c)}),s},eb=a=>a.isThemeGetter,tb=a=>{if(a<1)return{get:()=>{},set:()=>{}};let o=0,s=new Map,c=new Map;const f=(m,h)=>{s.set(m,h),o++,o>a&&(o=0,c=s,s=new Map)};return{get(m){let h=s.get(m);if(h!==void 0)return h;if((h=c.get(m))!==void 0)return f(m,h),h},set(m,h){s.has(m)?s.set(m,h):f(m,h)}}},su="!",uu=":",nb=uu.length,lb=a=>{const{prefix:o,experimentalParseClassName:s}=a;let c=f=>{const m=[];let h=0,v=0,y=0,p;for(let R=0;R<f.length;R++){let b=f[R];if(h===0&&v===0){if(b===uu){m.push(f.slice(y,R)),y=R+nb;continue}if(b==="/"){p=R;continue}}b==="["?h++:b==="]"?h--:b==="("?v++:b===")"&&v--}const S=m.length===0?f:f.substring(y),N=ab(S),C=N!==S,O=p&&p>y?p-y:void 0;return{modifiers:m,hasImportantModifier:C,baseClassName:N,maybePostfixModifierPosition:O}};if(o){const f=o+uu,m=c;c=h=>h.startsWith(f)?m(h.substring(f.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:h,maybePostfixModifierPosition:void 0}}if(s){const f=c;c=m=>s({className:m,parseClassName:f})}return c},ab=a=>a.endsWith(su)?a.substring(0,a.length-1):a.startsWith(su)?a.substring(1):a,ib=a=>{const o=Object.fromEntries(a.orderSensitiveModifiers.map(c=>[c,!0]));return c=>{if(c.length<=1)return c;const f=[];let m=[];return c.forEach(h=>{h[0]==="["||o[h]?(f.push(...m.sort(),h),m=[]):m.push(h)}),f.push(...m.sort()),f}},rb=a=>({cache:tb(a.cacheSize),parseClassName:lb(a),sortModifiers:ib(a),...Px(a)}),ob=/\s+/,cb=(a,o)=>{const{parseClassName:s,getClassGroupId:c,getConflictingClassGroupIds:f,sortModifiers:m}=o,h=[],v=a.trim().split(ob);let y="";for(let p=v.length-1;p>=0;p-=1){const S=v[p],{isExternal:N,modifiers:C,hasImportantModifier:O,baseClassName:R,maybePostfixModifierPosition:b}=s(S);if(N){y=S+(y.length>0?" "+y:y);continue}let T=!!b,L=c(T?R.substring(0,b):R);if(!L){if(!T){y=S+(y.length>0?" "+y:y);continue}if(L=c(R),!L){y=S+(y.length>0?" "+y:y);continue}T=!1}const D=m(C).join(":"),B=O?D+su:D,K=B+L;if(h.includes(K))continue;h.push(K);const V=f(L,T);for(let ee=0;ee<V.length;++ee){const P=V[ee];h.push(B+P)}y=S+(y.length>0?" "+y:y)}return y};function sb(){let a=0,o,s,c="";for(;a<arguments.length;)(o=arguments[a++])&&(s=Bv(o))&&(c&&(c+=" "),c+=s);return c}const Bv=a=>{if(typeof a=="string")return a;let o,s="";for(let c=0;c<a.length;c++)a[c]&&(o=Bv(a[c]))&&(s&&(s+=" "),s+=o);return s};function ub(a,...o){let s,c,f,m=h;function h(y){const p=o.reduce((S,N)=>N(S),a());return s=rb(p),c=s.cache.get,f=s.cache.set,m=v,v(y)}function v(y){const p=c(y);if(p)return p;const S=cb(y,s);return f(y,S),S}return function(){return m(sb.apply(null,arguments))}}const et=a=>{const o=s=>s[a]||[];return o.isThemeGetter=!0,o},kv=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,qv=/^\((?:(\w[\w-]*):)?(.+)\)$/i,fb=/^\d+\/\d+$/,db=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,mb=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,hb=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,vb=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,gb=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ga=a=>fb.test(a),be=a=>!!a&&!Number.isNaN(Number(a)),Pn=a=>!!a&&Number.isInteger(Number(a)),Ws=a=>a.endsWith("%")&&be(a.slice(0,-1)),wn=a=>db.test(a),pb=()=>!0,yb=a=>mb.test(a)&&!hb.test(a),Vv=()=>!1,xb=a=>vb.test(a),bb=a=>gb.test(a),Sb=a=>!le(a)&&!ae(a),wb=a=>Ca(a,Xv,Vv),le=a=>kv.test(a),Al=a=>Ca(a,Qv,yb),Ps=a=>Ca(a,Cb,be),ev=a=>Ca(a,Yv,Vv),Eb=a=>Ca(a,Gv,bb),to=a=>Ca(a,Zv,xb),ae=a=>qv.test(a),Mi=a=>ja(a,Qv),Nb=a=>ja(a,jb),tv=a=>ja(a,Yv),Ab=a=>ja(a,Xv),Tb=a=>ja(a,Gv),no=a=>ja(a,Zv,!0),Ca=(a,o,s)=>{const c=kv.exec(a);return c?c[1]?o(c[1]):s(c[2]):!1},ja=(a,o,s=!1)=>{const c=qv.exec(a);return c?c[1]?o(c[1]):s:!1},Yv=a=>a==="position"||a==="percentage",Gv=a=>a==="image"||a==="url",Xv=a=>a==="length"||a==="size"||a==="bg-size",Qv=a=>a==="length",Cb=a=>a==="number",jb=a=>a==="family-name",Zv=a=>a==="shadow",_b=()=>{const a=et("color"),o=et("font"),s=et("text"),c=et("font-weight"),f=et("tracking"),m=et("leading"),h=et("breakpoint"),v=et("container"),y=et("spacing"),p=et("radius"),S=et("shadow"),N=et("inset-shadow"),C=et("text-shadow"),O=et("drop-shadow"),R=et("blur"),b=et("perspective"),T=et("aspect"),L=et("ease"),D=et("animate"),B=()=>["auto","avoid","all","avoid-page","page","left","right","column"],K=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],V=()=>[...K(),ae,le],ee=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],$=()=>[ae,le,y],se=()=>[ga,"full","auto",...$()],J=()=>[Pn,"none","subgrid",ae,le],ue=()=>["auto",{span:["full",Pn,ae,le]},Pn,ae,le],oe=()=>[Pn,"auto",ae,le],ve=()=>["auto","min","max","fr",ae,le],pe=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Y=()=>["start","end","center","stretch","center-safe","end-safe"],_=()=>["auto",...$()],Z=()=>[ga,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...$()],k=()=>[a,ae,le],ie=()=>[...K(),tv,ev,{position:[ae,le]}],E=()=>["no-repeat",{repeat:["","x","y","space","round"]}],G=()=>["auto","cover","contain",Ab,wb,{size:[ae,le]}],F=()=>[Ws,Mi,Al],W=()=>["","none","full",p,ae,le],te=()=>["",be,Mi,Al],ge=()=>["solid","dashed","dotted","double"],ce=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],I=()=>[be,Ws,tv,ev],fe=()=>["","none",R,ae,le],Oe=()=>["none",be,ae,le],Te=()=>["none",be,ae,le],we=()=>[be,ae,le],Ee=()=>[ga,"full",...$()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[wn],breakpoint:[wn],color:[pb],container:[wn],"drop-shadow":[wn],ease:["in","out","in-out"],font:[Sb],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[wn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[wn],shadow:[wn],spacing:["px",be],text:[wn],"text-shadow":[wn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",ga,le,ae,T]}],container:["container"],columns:[{columns:[be,le,ae,v]}],"break-after":[{"break-after":B()}],"break-before":[{"break-before":B()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:V()}],overflow:[{overflow:ee()}],"overflow-x":[{"overflow-x":ee()}],"overflow-y":[{"overflow-y":ee()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:se()}],"inset-x":[{"inset-x":se()}],"inset-y":[{"inset-y":se()}],start:[{start:se()}],end:[{end:se()}],top:[{top:se()}],right:[{right:se()}],bottom:[{bottom:se()}],left:[{left:se()}],visibility:["visible","invisible","collapse"],z:[{z:[Pn,"auto",ae,le]}],basis:[{basis:[ga,"full","auto",v,...$()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[be,ga,"auto","initial","none",le]}],grow:[{grow:["",be,ae,le]}],shrink:[{shrink:["",be,ae,le]}],order:[{order:[Pn,"first","last","none",ae,le]}],"grid-cols":[{"grid-cols":J()}],"col-start-end":[{col:ue()}],"col-start":[{"col-start":oe()}],"col-end":[{"col-end":oe()}],"grid-rows":[{"grid-rows":J()}],"row-start-end":[{row:ue()}],"row-start":[{"row-start":oe()}],"row-end":[{"row-end":oe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ve()}],"auto-rows":[{"auto-rows":ve()}],gap:[{gap:$()}],"gap-x":[{"gap-x":$()}],"gap-y":[{"gap-y":$()}],"justify-content":[{justify:[...pe(),"normal"]}],"justify-items":[{"justify-items":[...Y(),"normal"]}],"justify-self":[{"justify-self":["auto",...Y()]}],"align-content":[{content:["normal",...pe()]}],"align-items":[{items:[...Y(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Y(),{baseline:["","last"]}]}],"place-content":[{"place-content":pe()}],"place-items":[{"place-items":[...Y(),"baseline"]}],"place-self":[{"place-self":["auto",...Y()]}],p:[{p:$()}],px:[{px:$()}],py:[{py:$()}],ps:[{ps:$()}],pe:[{pe:$()}],pt:[{pt:$()}],pr:[{pr:$()}],pb:[{pb:$()}],pl:[{pl:$()}],m:[{m:_()}],mx:[{mx:_()}],my:[{my:_()}],ms:[{ms:_()}],me:[{me:_()}],mt:[{mt:_()}],mr:[{mr:_()}],mb:[{mb:_()}],ml:[{ml:_()}],"space-x":[{"space-x":$()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":$()}],"space-y-reverse":["space-y-reverse"],size:[{size:Z()}],w:[{w:[v,"screen",...Z()]}],"min-w":[{"min-w":[v,"screen","none",...Z()]}],"max-w":[{"max-w":[v,"screen","none","prose",{screen:[h]},...Z()]}],h:[{h:["screen","lh",...Z()]}],"min-h":[{"min-h":["screen","lh","none",...Z()]}],"max-h":[{"max-h":["screen","lh",...Z()]}],"font-size":[{text:["base",s,Mi,Al]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[c,ae,Ps]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Ws,le]}],"font-family":[{font:[Nb,le,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[f,ae,le]}],"line-clamp":[{"line-clamp":[be,"none",ae,Ps]}],leading:[{leading:[m,...$()]}],"list-image":[{"list-image":["none",ae,le]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ae,le]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:k()}],"text-color":[{text:k()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ge(),"wavy"]}],"text-decoration-thickness":[{decoration:[be,"from-font","auto",ae,Al]}],"text-decoration-color":[{decoration:k()}],"underline-offset":[{"underline-offset":[be,"auto",ae,le]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:$()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ae,le]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ae,le]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ie()}],"bg-repeat":[{bg:E()}],"bg-size":[{bg:G()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Pn,ae,le],radial:["",ae,le],conic:[Pn,ae,le]},Tb,Eb]}],"bg-color":[{bg:k()}],"gradient-from-pos":[{from:F()}],"gradient-via-pos":[{via:F()}],"gradient-to-pos":[{to:F()}],"gradient-from":[{from:k()}],"gradient-via":[{via:k()}],"gradient-to":[{to:k()}],rounded:[{rounded:W()}],"rounded-s":[{"rounded-s":W()}],"rounded-e":[{"rounded-e":W()}],"rounded-t":[{"rounded-t":W()}],"rounded-r":[{"rounded-r":W()}],"rounded-b":[{"rounded-b":W()}],"rounded-l":[{"rounded-l":W()}],"rounded-ss":[{"rounded-ss":W()}],"rounded-se":[{"rounded-se":W()}],"rounded-ee":[{"rounded-ee":W()}],"rounded-es":[{"rounded-es":W()}],"rounded-tl":[{"rounded-tl":W()}],"rounded-tr":[{"rounded-tr":W()}],"rounded-br":[{"rounded-br":W()}],"rounded-bl":[{"rounded-bl":W()}],"border-w":[{border:te()}],"border-w-x":[{"border-x":te()}],"border-w-y":[{"border-y":te()}],"border-w-s":[{"border-s":te()}],"border-w-e":[{"border-e":te()}],"border-w-t":[{"border-t":te()}],"border-w-r":[{"border-r":te()}],"border-w-b":[{"border-b":te()}],"border-w-l":[{"border-l":te()}],"divide-x":[{"divide-x":te()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":te()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ge(),"hidden","none"]}],"divide-style":[{divide:[...ge(),"hidden","none"]}],"border-color":[{border:k()}],"border-color-x":[{"border-x":k()}],"border-color-y":[{"border-y":k()}],"border-color-s":[{"border-s":k()}],"border-color-e":[{"border-e":k()}],"border-color-t":[{"border-t":k()}],"border-color-r":[{"border-r":k()}],"border-color-b":[{"border-b":k()}],"border-color-l":[{"border-l":k()}],"divide-color":[{divide:k()}],"outline-style":[{outline:[...ge(),"none","hidden"]}],"outline-offset":[{"outline-offset":[be,ae,le]}],"outline-w":[{outline:["",be,Mi,Al]}],"outline-color":[{outline:k()}],shadow:[{shadow:["","none",S,no,to]}],"shadow-color":[{shadow:k()}],"inset-shadow":[{"inset-shadow":["none",N,no,to]}],"inset-shadow-color":[{"inset-shadow":k()}],"ring-w":[{ring:te()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:k()}],"ring-offset-w":[{"ring-offset":[be,Al]}],"ring-offset-color":[{"ring-offset":k()}],"inset-ring-w":[{"inset-ring":te()}],"inset-ring-color":[{"inset-ring":k()}],"text-shadow":[{"text-shadow":["none",C,no,to]}],"text-shadow-color":[{"text-shadow":k()}],opacity:[{opacity:[be,ae,le]}],"mix-blend":[{"mix-blend":[...ce(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ce()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[be]}],"mask-image-linear-from-pos":[{"mask-linear-from":I()}],"mask-image-linear-to-pos":[{"mask-linear-to":I()}],"mask-image-linear-from-color":[{"mask-linear-from":k()}],"mask-image-linear-to-color":[{"mask-linear-to":k()}],"mask-image-t-from-pos":[{"mask-t-from":I()}],"mask-image-t-to-pos":[{"mask-t-to":I()}],"mask-image-t-from-color":[{"mask-t-from":k()}],"mask-image-t-to-color":[{"mask-t-to":k()}],"mask-image-r-from-pos":[{"mask-r-from":I()}],"mask-image-r-to-pos":[{"mask-r-to":I()}],"mask-image-r-from-color":[{"mask-r-from":k()}],"mask-image-r-to-color":[{"mask-r-to":k()}],"mask-image-b-from-pos":[{"mask-b-from":I()}],"mask-image-b-to-pos":[{"mask-b-to":I()}],"mask-image-b-from-color":[{"mask-b-from":k()}],"mask-image-b-to-color":[{"mask-b-to":k()}],"mask-image-l-from-pos":[{"mask-l-from":I()}],"mask-image-l-to-pos":[{"mask-l-to":I()}],"mask-image-l-from-color":[{"mask-l-from":k()}],"mask-image-l-to-color":[{"mask-l-to":k()}],"mask-image-x-from-pos":[{"mask-x-from":I()}],"mask-image-x-to-pos":[{"mask-x-to":I()}],"mask-image-x-from-color":[{"mask-x-from":k()}],"mask-image-x-to-color":[{"mask-x-to":k()}],"mask-image-y-from-pos":[{"mask-y-from":I()}],"mask-image-y-to-pos":[{"mask-y-to":I()}],"mask-image-y-from-color":[{"mask-y-from":k()}],"mask-image-y-to-color":[{"mask-y-to":k()}],"mask-image-radial":[{"mask-radial":[ae,le]}],"mask-image-radial-from-pos":[{"mask-radial-from":I()}],"mask-image-radial-to-pos":[{"mask-radial-to":I()}],"mask-image-radial-from-color":[{"mask-radial-from":k()}],"mask-image-radial-to-color":[{"mask-radial-to":k()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":K()}],"mask-image-conic-pos":[{"mask-conic":[be]}],"mask-image-conic-from-pos":[{"mask-conic-from":I()}],"mask-image-conic-to-pos":[{"mask-conic-to":I()}],"mask-image-conic-from-color":[{"mask-conic-from":k()}],"mask-image-conic-to-color":[{"mask-conic-to":k()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ie()}],"mask-repeat":[{mask:E()}],"mask-size":[{mask:G()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ae,le]}],filter:[{filter:["","none",ae,le]}],blur:[{blur:fe()}],brightness:[{brightness:[be,ae,le]}],contrast:[{contrast:[be,ae,le]}],"drop-shadow":[{"drop-shadow":["","none",O,no,to]}],"drop-shadow-color":[{"drop-shadow":k()}],grayscale:[{grayscale:["",be,ae,le]}],"hue-rotate":[{"hue-rotate":[be,ae,le]}],invert:[{invert:["",be,ae,le]}],saturate:[{saturate:[be,ae,le]}],sepia:[{sepia:["",be,ae,le]}],"backdrop-filter":[{"backdrop-filter":["","none",ae,le]}],"backdrop-blur":[{"backdrop-blur":fe()}],"backdrop-brightness":[{"backdrop-brightness":[be,ae,le]}],"backdrop-contrast":[{"backdrop-contrast":[be,ae,le]}],"backdrop-grayscale":[{"backdrop-grayscale":["",be,ae,le]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[be,ae,le]}],"backdrop-invert":[{"backdrop-invert":["",be,ae,le]}],"backdrop-opacity":[{"backdrop-opacity":[be,ae,le]}],"backdrop-saturate":[{"backdrop-saturate":[be,ae,le]}],"backdrop-sepia":[{"backdrop-sepia":["",be,ae,le]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":$()}],"border-spacing-x":[{"border-spacing-x":$()}],"border-spacing-y":[{"border-spacing-y":$()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ae,le]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[be,"initial",ae,le]}],ease:[{ease:["linear","initial",L,ae,le]}],delay:[{delay:[be,ae,le]}],animate:[{animate:["none",D,ae,le]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,ae,le]}],"perspective-origin":[{"perspective-origin":V()}],rotate:[{rotate:Oe()}],"rotate-x":[{"rotate-x":Oe()}],"rotate-y":[{"rotate-y":Oe()}],"rotate-z":[{"rotate-z":Oe()}],scale:[{scale:Te()}],"scale-x":[{"scale-x":Te()}],"scale-y":[{"scale-y":Te()}],"scale-z":[{"scale-z":Te()}],"scale-3d":["scale-3d"],skew:[{skew:we()}],"skew-x":[{"skew-x":we()}],"skew-y":[{"skew-y":we()}],transform:[{transform:[ae,le,"","none","gpu","cpu"]}],"transform-origin":[{origin:V()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ee()}],"translate-x":[{"translate-x":Ee()}],"translate-y":[{"translate-y":Ee()}],"translate-z":[{"translate-z":Ee()}],"translate-none":["translate-none"],accent:[{accent:k()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:k()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ae,le]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":$()}],"scroll-mx":[{"scroll-mx":$()}],"scroll-my":[{"scroll-my":$()}],"scroll-ms":[{"scroll-ms":$()}],"scroll-me":[{"scroll-me":$()}],"scroll-mt":[{"scroll-mt":$()}],"scroll-mr":[{"scroll-mr":$()}],"scroll-mb":[{"scroll-mb":$()}],"scroll-ml":[{"scroll-ml":$()}],"scroll-p":[{"scroll-p":$()}],"scroll-px":[{"scroll-px":$()}],"scroll-py":[{"scroll-py":$()}],"scroll-ps":[{"scroll-ps":$()}],"scroll-pe":[{"scroll-pe":$()}],"scroll-pt":[{"scroll-pt":$()}],"scroll-pr":[{"scroll-pr":$()}],"scroll-pb":[{"scroll-pb":$()}],"scroll-pl":[{"scroll-pl":$()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ae,le]}],fill:[{fill:["none",...k()]}],"stroke-w":[{stroke:[be,Mi,Al,Ps]}],stroke:[{stroke:["none",...k()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Ob=ub(_b);function Je(...a){return Ob(Uv(a))}const Mb=Hv("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function st({className:a,variant:o,size:s,asChild:c=!1,...f}){const m=c?Dv:"button";return u.jsx(m,{"data-slot":"button",className:Je(Mb({variant:o,size:s,className:a})),...f})}const Rb=({children:a,currentPage:o,onPageChange:s})=>{const[c,f]=x.useState(!1),m=[{id:"dashboard",label:"Dashboard",icon:cx},{id:"conversations",label:"Conversas",icon:Jh},{id:"contacts",label:"Contatos",icon:uo},{id:"templates",label:"Templates",icon:so},{id:"automation",label:"Automação",icon:rx},{id:"whatsapp",label:"WhatsApp",icon:_v},{id:"ai-analysis",label:"Análise IA",icon:Xx},{id:"settings",label:"Configurações",icon:zx}];return u.jsxs("div",{className:"min-h-screen bg-gray-50",children:[c&&u.jsx("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>f(!1)}),u.jsxs("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${c?"translate-x-0":"-translate-x-full"}`,children:[u.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b",children:[u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx("div",{className:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center",children:u.jsx(Jh,{className:"w-5 h-5 text-white"})}),u.jsx("span",{className:"text-xl font-bold text-gray-900",children:"WhatsApp AI"})]}),u.jsx(st,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>f(!1),children:u.jsx(Rv,{className:"w-5 h-5"})})]}),u.jsx("nav",{className:"mt-6",children:m.map(h=>{const v=h.icon;return u.jsxs("button",{onClick:()=>{s(h.id),f(!1)},className:`w-full flex items-center px-6 py-3 text-left transition-colors duration-200 ${o===h.id?"bg-green-50 text-green-700 border-r-2 border-green-500":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[u.jsx(v,{className:"w-5 h-5 mr-3"}),h.label]},h.id)})}),u.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t",children:u.jsxs("div",{className:"flex items-center space-x-3",children:[u.jsx("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:u.jsx("span",{className:"text-sm font-medium text-gray-600",children:"U"})}),u.jsxs("div",{children:[u.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Usuário"}),u.jsx("p",{className:"text-xs text-gray-500",children:"Administrador"})]})]})})]}),u.jsxs("div",{className:"lg:pl-64",children:[u.jsxs("div",{className:"sticky top-0 z-30 flex items-center justify-between h-16 px-6 bg-white border-b",children:[u.jsx(st,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>f(!0),children:u.jsx(Tx,{className:"w-5 h-5"})}),u.jsx("div",{className:"flex items-center space-x-4",children:u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),u.jsx("span",{className:"text-sm text-gray-600",children:"Sistema Online"})]})})]}),u.jsx("main",{className:"p-6",children:a})]})]})},wt="http://localhost:5000",fu={CONTACTS:`${wt}/api/contacts`,CONTACTS_STATS:`${wt}/api/contacts/stats`,TEMPLATES:`${wt}/api/templates`,TEMPLATES_CATEGORIES:`${wt}/api/templates/categories`,AUTOMATION_RULES:`${wt}/api/automation/rules`,AUTOMATION_STATS:`${wt}/api/automation/stats`,WHATSAPP_SEND:`${wt}/api/whatsapp/send`,WHATSAPP_WEBHOOK:`${wt}/api/whatsapp/webhook`,WHATSAPP_ACCOUNTS:`${wt}/api/whatsapp/accounts`,AI_ANALYZE:`${wt}/api/ai/analyze`,AI_SUGGESTIONS:`${wt}/api/ai/suggestions`,AUTH_LOGIN:`${wt}/api/auth/login`,AUTH_VERIFY:`${wt}/api/auth/verify-token`,AUTH_REFRESH:`${wt}/api/auth/refresh`},du=async(a,o={})=>{const s={headers:{"Content-Type":"application/json",...o.headers},...o};try{const c=await fetch(a,s);if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);return await c.json()}catch(c){throw console.error("API request failed:",c),c}},nv=()=>{const[a,o]=x.useState({totalContacts:0,newContacts:0,qualifiedLeads:0,totalValue:0}),[s,c]=x.useState(!0);x.useEffect(()=>{f()},[]);const f=async()=>{try{c(!0);const h=await du(fu.CONTACTS_STATS);o(h)}catch(h){console.error("Erro ao buscar estatísticas:",h),o({totalContacts:1247,newContacts:89,qualifiedLeads:156,totalValue:45890})}finally{c(!1)}},m=()=>{f()};return s?u.jsx("div",{className:"p-6",children:u.jsxs("div",{className:"animate-pulse",children:[u.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-4"}),u.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[1,2,3,4].map(h=>u.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[u.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),u.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/2 mb-2"}),u.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]},h))})]})}):u.jsxs("div",{className:"p-6",children:[u.jsxs("div",{className:"flex justify-between items-center mb-6",children:[u.jsxs("div",{children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),u.jsx("p",{className:"text-gray-600",children:"Visão geral do seu WhatsApp AI Agent"})]}),u.jsx("button",{onClick:m,className:"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors",children:"Atualizar Dados"})]}),u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[u.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total de Contatos"}),u.jsx("p",{className:"text-2xl font-bold text-gray-900",children:a.totalContacts}),u.jsx("p",{className:"text-xs text-green-600",children:u.jsx("span",{className:"inline-flex items-center",children:"↗ +12% contatos cadastrados"})})]}),u.jsx("div",{className:"p-3 bg-blue-50 rounded-full",children:u.jsx("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM9 9a2 2 0 11-4 0 2 2 0 014 0z"})})})]})}),u.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Novos Contatos"}),u.jsx("p",{className:"text-2xl font-bold text-gray-900",children:a.newContacts}),u.jsx("p",{className:"text-xs text-green-600",children:u.jsx("span",{className:"inline-flex items-center",children:"↗ +8% últimos 30 dias"})})]}),u.jsx("div",{className:"p-3 bg-green-50 rounded-full",children:u.jsx("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})})})]})}),u.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Leads Qualificados"}),u.jsx("p",{className:"text-2xl font-bold text-gray-900",children:a.qualifiedLeads}),u.jsx("p",{className:"text-xs text-green-600",children:u.jsx("span",{className:"inline-flex items-center",children:"↗ +15% prontos para conversão"})})]}),u.jsx("div",{className:"p-3 bg-yellow-50 rounded-full",children:u.jsx("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})})]})}),u.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Valor Total"}),u.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:["R$ ",a.totalValue]}),u.jsx("p",{className:"text-xs text-green-600",children:u.jsx("span",{className:"inline-flex items-center",children:"↗ +23% em negociações"})})]}),u.jsx("div",{className:"p-3 bg-purple-50 rounded-full",children:u.jsx("svg",{className:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})})]})})]}),u.jsxs("div",{className:"bg-white rounded-lg shadow-sm border",children:[u.jsxs("div",{className:"p-6 border-b border-gray-200",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx("svg",{className:"w-5 h-5 text-gray-400 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),u.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Mensagens Recentes"})]}),u.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Últimas mensagens recebidas"})]}),u.jsx("div",{className:"p-6",children:u.jsxs("div",{className:"flex flex-col items-center justify-center py-12",children:[u.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:u.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),u.jsx("p",{className:"text-gray-500 text-center",children:"Nenhuma mensagem recente"}),u.jsx("p",{className:"text-gray-400 text-sm text-center mt-1",children:"As mensagens aparecerão aqui quando você começar a receber conversas"})]})})]})]})};function ba({className:a,...o}){return u.jsx("div",{"data-slot":"card",className:Je("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...o})}function mu({className:a,...o}){return u.jsx("div",{"data-slot":"card-header",className:Je("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...o})}function Kv({className:a,...o}){return u.jsx("div",{"data-slot":"card-title",className:Je("leading-none font-semibold",a),...o})}function Db({className:a,...o}){return u.jsx("div",{"data-slot":"card-description",className:Je("text-muted-foreground text-sm",a),...o})}function Sa({className:a,...o}){return u.jsx("div",{"data-slot":"card-content",className:Je("px-6",a),...o})}function En({className:a,type:o,...s}){return u.jsx("input",{type:o,"data-slot":"input",className:Je("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...s})}const zb=Hv("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function hu({className:a,variant:o,asChild:s=!1,...c}){const f=s?Dv:"span";return u.jsx(f,{"data-slot":"badge",className:Je(zb({variant:o}),a),...c})}function ke(a,o,{checkForDefaultPrevented:s=!0}={}){return function(f){if(a==null||a(f),s===!1||!f.defaultPrevented)return o==null?void 0:o(f)}}function Ub(a,o){const s=x.createContext(o),c=m=>{const{children:h,...v}=m,y=x.useMemo(()=>v,Object.values(v));return u.jsx(s.Provider,{value:y,children:h})};c.displayName=a+"Provider";function f(m){const h=x.useContext(s);if(h)return h;if(o!==void 0)return o;throw new Error(`\`${m}\` must be used within \`${a}\``)}return[c,f]}function wo(a,o=[]){let s=[];function c(m,h){const v=x.createContext(h),y=s.length;s=[...s,h];const p=N=>{var L;const{scope:C,children:O,...R}=N,b=((L=C==null?void 0:C[a])==null?void 0:L[y])||v,T=x.useMemo(()=>R,Object.values(R));return u.jsx(b.Provider,{value:T,children:O})};p.displayName=m+"Provider";function S(N,C){var b;const O=((b=C==null?void 0:C[a])==null?void 0:b[y])||v,R=x.useContext(O);if(R)return R;if(h!==void 0)return h;throw new Error(`\`${N}\` must be used within \`${m}\``)}return[p,S]}const f=()=>{const m=s.map(h=>x.createContext(h));return function(v){const y=(v==null?void 0:v[a])||m;return x.useMemo(()=>({[`__scope${a}`]:{...v,[a]:y}}),[v,y])}};return f.scopeName=a,[c,Hb(f,...o)]}function Hb(...a){const o=a[0];if(a.length===1)return o;const s=()=>{const c=a.map(f=>({useScope:f(),scopeName:f.scopeName}));return function(m){const h=c.reduce((v,{useScope:y,scopeName:p})=>{const N=y(m)[`__scope${p}`];return{...v,...N}},{});return x.useMemo(()=>({[`__scope${o.scopeName}`]:h}),[h])}};return s.scopeName=o.scopeName,s}var ht=globalThis!=null&&globalThis.document?x.useLayoutEffect:()=>{},Lb=Av[" useId ".trim().toString()]||(()=>{}),Bb=0;function wa(a){const[o,s]=x.useState(Lb());return ht(()=>{s(c=>c??String(Bb++))},[a]),a||(o?`radix-${o}`:"")}var kb=Av[" useInsertionEffect ".trim().toString()]||ht;function vu({prop:a,defaultProp:o,onChange:s=()=>{},caller:c}){const[f,m,h]=qb({defaultProp:o,onChange:s}),v=a!==void 0,y=v?a:f;{const S=x.useRef(a!==void 0);x.useEffect(()=>{const N=S.current;N!==v&&console.warn(`${c} is changing from ${N?"controlled":"uncontrolled"} to ${v?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),S.current=v},[v,c])}const p=x.useCallback(S=>{var N;if(v){const C=Vb(S)?S(a):S;C!==a&&((N=h.current)==null||N.call(h,C))}else m(S)},[v,a,m,h]);return[y,p]}function qb({defaultProp:a,onChange:o}){const[s,c]=x.useState(a),f=x.useRef(s),m=x.useRef(o);return kb(()=>{m.current=o},[o]),x.useEffect(()=>{var h;f.current!==s&&((h=m.current)==null||h.call(m,s),f.current=s)},[s,f]),[s,c,m]}function Vb(a){return typeof a=="function"}var ki=Tv();const Yb=Nv(ki);var Gb=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Le=Gb.reduce((a,o)=>{const s=Aa(`Primitive.${o}`),c=x.forwardRef((f,m)=>{const{asChild:h,...v}=f,y=h?s:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(y,{...v,ref:m})});return c.displayName=`Primitive.${o}`,{...a,[o]:c}},{});function Xb(a,o){a&&ki.flushSync(()=>a.dispatchEvent(o))}function Tl(a){const o=x.useRef(a);return x.useEffect(()=>{o.current=a}),x.useMemo(()=>(...s)=>{var c;return(c=o.current)==null?void 0:c.call(o,...s)},[])}function Qb(a,o=globalThis==null?void 0:globalThis.document){const s=Tl(a);x.useEffect(()=>{const c=f=>{f.key==="Escape"&&s(f)};return o.addEventListener("keydown",c,{capture:!0}),()=>o.removeEventListener("keydown",c,{capture:!0})},[s,o])}var Zb="DismissableLayer",gu="dismissableLayer.update",Kb="dismissableLayer.pointerDownOutside",Jb="dismissableLayer.focusOutside",lv,Jv=x.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ju=x.forwardRef((a,o)=>{const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:h,onDismiss:v,...y}=a,p=x.useContext(Jv),[S,N]=x.useState(null),C=(S==null?void 0:S.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,O]=x.useState({}),R=Qe(o,P=>N(P)),b=Array.from(p.layers),[T]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),L=b.indexOf(T),D=S?b.indexOf(S):-1,B=p.layersWithOutsidePointerEventsDisabled.size>0,K=D>=L,V=Pb(P=>{const $=P.target,se=[...p.branches].some(J=>J.contains($));!K||se||(f==null||f(P),h==null||h(P),P.defaultPrevented||v==null||v())},C),ee=Fb(P=>{const $=P.target;[...p.branches].some(J=>J.contains($))||(m==null||m(P),h==null||h(P),P.defaultPrevented||v==null||v())},C);return Qb(P=>{D===p.layers.size-1&&(c==null||c(P),!P.defaultPrevented&&v&&(P.preventDefault(),v()))},C),x.useEffect(()=>{if(S)return s&&(p.layersWithOutsidePointerEventsDisabled.size===0&&(lv=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(S)),p.layers.add(S),av(),()=>{s&&p.layersWithOutsidePointerEventsDisabled.size===1&&(C.body.style.pointerEvents=lv)}},[S,C,s,p]),x.useEffect(()=>()=>{S&&(p.layers.delete(S),p.layersWithOutsidePointerEventsDisabled.delete(S),av())},[S,p]),x.useEffect(()=>{const P=()=>O({});return document.addEventListener(gu,P),()=>document.removeEventListener(gu,P)},[]),u.jsx(Le.div,{...y,ref:R,style:{pointerEvents:B?K?"auto":"none":void 0,...a.style},onFocusCapture:ke(a.onFocusCapture,ee.onFocusCapture),onBlurCapture:ke(a.onBlurCapture,ee.onBlurCapture),onPointerDownCapture:ke(a.onPointerDownCapture,V.onPointerDownCapture)})});ju.displayName=Zb;var $b="DismissableLayerBranch",Wb=x.forwardRef((a,o)=>{const s=x.useContext(Jv),c=x.useRef(null),f=Qe(o,c);return x.useEffect(()=>{const m=c.current;if(m)return s.branches.add(m),()=>{s.branches.delete(m)}},[s.branches]),u.jsx(Le.div,{...a,ref:f})});Wb.displayName=$b;function Pb(a,o=globalThis==null?void 0:globalThis.document){const s=Tl(a),c=x.useRef(!1),f=x.useRef(()=>{});return x.useEffect(()=>{const m=v=>{if(v.target&&!c.current){let y=function(){$v(Kb,s,p,{discrete:!0})};const p={originalEvent:v};v.pointerType==="touch"?(o.removeEventListener("click",f.current),f.current=y,o.addEventListener("click",f.current,{once:!0})):y()}else o.removeEventListener("click",f.current);c.current=!1},h=window.setTimeout(()=>{o.addEventListener("pointerdown",m)},0);return()=>{window.clearTimeout(h),o.removeEventListener("pointerdown",m),o.removeEventListener("click",f.current)}},[o,s]),{onPointerDownCapture:()=>c.current=!0}}function Fb(a,o=globalThis==null?void 0:globalThis.document){const s=Tl(a),c=x.useRef(!1);return x.useEffect(()=>{const f=m=>{m.target&&!c.current&&$v(Jb,s,{originalEvent:m},{discrete:!1})};return o.addEventListener("focusin",f),()=>o.removeEventListener("focusin",f)},[o,s]),{onFocusCapture:()=>c.current=!0,onBlurCapture:()=>c.current=!1}}function av(){const a=new CustomEvent(gu);document.dispatchEvent(a)}function $v(a,o,s,{discrete:c}){const f=s.originalEvent.target,m=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:s});o&&f.addEventListener(a,o,{once:!0}),c?Xb(f,m):f.dispatchEvent(m)}var Fs="focusScope.autoFocusOnMount",Is="focusScope.autoFocusOnUnmount",iv={bubbles:!1,cancelable:!0},Ib="FocusScope",_u=x.forwardRef((a,o)=>{const{loop:s=!1,trapped:c=!1,onMountAutoFocus:f,onUnmountAutoFocus:m,...h}=a,[v,y]=x.useState(null),p=Tl(f),S=Tl(m),N=x.useRef(null),C=Qe(o,b=>y(b)),O=x.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;x.useEffect(()=>{if(c){let b=function(B){if(O.paused||!v)return;const K=B.target;v.contains(K)?N.current=K:In(N.current,{select:!0})},T=function(B){if(O.paused||!v)return;const K=B.relatedTarget;K!==null&&(v.contains(K)||In(N.current,{select:!0}))},L=function(B){if(document.activeElement===document.body)for(const V of B)V.removedNodes.length>0&&In(v)};document.addEventListener("focusin",b),document.addEventListener("focusout",T);const D=new MutationObserver(L);return v&&D.observe(v,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",b),document.removeEventListener("focusout",T),D.disconnect()}}},[c,v,O.paused]),x.useEffect(()=>{if(v){ov.add(O);const b=document.activeElement;if(!v.contains(b)){const L=new CustomEvent(Fs,iv);v.addEventListener(Fs,p),v.dispatchEvent(L),L.defaultPrevented||(e1(i1(Wv(v)),{select:!0}),document.activeElement===b&&In(v))}return()=>{v.removeEventListener(Fs,p),setTimeout(()=>{const L=new CustomEvent(Is,iv);v.addEventListener(Is,S),v.dispatchEvent(L),L.defaultPrevented||In(b??document.body,{select:!0}),v.removeEventListener(Is,S),ov.remove(O)},0)}}},[v,p,S,O]);const R=x.useCallback(b=>{if(!s&&!c||O.paused)return;const T=b.key==="Tab"&&!b.altKey&&!b.ctrlKey&&!b.metaKey,L=document.activeElement;if(T&&L){const D=b.currentTarget,[B,K]=t1(D);B&&K?!b.shiftKey&&L===K?(b.preventDefault(),s&&In(B,{select:!0})):b.shiftKey&&L===B&&(b.preventDefault(),s&&In(K,{select:!0})):L===D&&b.preventDefault()}},[s,c,O.paused]);return u.jsx(Le.div,{tabIndex:-1,...h,ref:C,onKeyDown:R})});_u.displayName=Ib;function e1(a,{select:o=!1}={}){const s=document.activeElement;for(const c of a)if(In(c,{select:o}),document.activeElement!==s)return}function t1(a){const o=Wv(a),s=rv(o,a),c=rv(o.reverse(),a);return[s,c]}function Wv(a){const o=[],s=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:c=>{const f=c.tagName==="INPUT"&&c.type==="hidden";return c.disabled||c.hidden||f?NodeFilter.FILTER_SKIP:c.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)o.push(s.currentNode);return o}function rv(a,o){for(const s of a)if(!n1(s,{upTo:o}))return s}function n1(a,{upTo:o}){if(getComputedStyle(a).visibility==="hidden")return!0;for(;a;){if(o!==void 0&&a===o)return!1;if(getComputedStyle(a).display==="none")return!0;a=a.parentElement}return!1}function l1(a){return a instanceof HTMLInputElement&&"select"in a}function In(a,{select:o=!1}={}){if(a&&a.focus){const s=document.activeElement;a.focus({preventScroll:!0}),a!==s&&l1(a)&&o&&a.select()}}var ov=a1();function a1(){let a=[];return{add(o){const s=a[0];o!==s&&(s==null||s.pause()),a=cv(a,o),a.unshift(o)},remove(o){var s;a=cv(a,o),(s=a[0])==null||s.resume()}}}function cv(a,o){const s=[...a],c=s.indexOf(o);return c!==-1&&s.splice(c,1),s}function i1(a){return a.filter(o=>o.tagName!=="A")}var r1="Portal",Ou=x.forwardRef((a,o)=>{var v;const{container:s,...c}=a,[f,m]=x.useState(!1);ht(()=>m(!0),[]);const h=s||f&&((v=globalThis==null?void 0:globalThis.document)==null?void 0:v.body);return h?Yb.createPortal(u.jsx(Le.div,{...c,ref:o}),h):null});Ou.displayName=r1;function o1(a,o){return x.useReducer((s,c)=>o[s][c]??s,a)}var Eo=a=>{const{present:o,children:s}=a,c=c1(o),f=typeof s=="function"?s({present:c.isPresent}):x.Children.only(s),m=Qe(c.ref,s1(f));return typeof s=="function"||c.isPresent?x.cloneElement(f,{ref:m}):null};Eo.displayName="Presence";function c1(a){const[o,s]=x.useState(),c=x.useRef(null),f=x.useRef(a),m=x.useRef("none"),h=a?"mounted":"unmounted",[v,y]=o1(h,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return x.useEffect(()=>{const p=lo(c.current);m.current=v==="mounted"?p:"none"},[v]),ht(()=>{const p=c.current,S=f.current;if(S!==a){const C=m.current,O=lo(p);a?y("MOUNT"):O==="none"||(p==null?void 0:p.display)==="none"?y("UNMOUNT"):y(S&&C!==O?"ANIMATION_OUT":"UNMOUNT"),f.current=a}},[a,y]),ht(()=>{if(o){let p;const S=o.ownerDocument.defaultView??window,N=O=>{const b=lo(c.current).includes(O.animationName);if(O.target===o&&b&&(y("ANIMATION_END"),!f.current)){const T=o.style.animationFillMode;o.style.animationFillMode="forwards",p=S.setTimeout(()=>{o.style.animationFillMode==="forwards"&&(o.style.animationFillMode=T)})}},C=O=>{O.target===o&&(m.current=lo(c.current))};return o.addEventListener("animationstart",C),o.addEventListener("animationcancel",N),o.addEventListener("animationend",N),()=>{S.clearTimeout(p),o.removeEventListener("animationstart",C),o.removeEventListener("animationcancel",N),o.removeEventListener("animationend",N)}}else y("ANIMATION_END")},[o,y]),{isPresent:["mounted","unmountSuspended"].includes(v),ref:x.useCallback(p=>{c.current=p?getComputedStyle(p):null,s(p)},[])}}function lo(a){return(a==null?void 0:a.animationName)||"none"}function s1(a){var c,f;let o=(c=Object.getOwnPropertyDescriptor(a.props,"ref"))==null?void 0:c.get,s=o&&"isReactWarning"in o&&o.isReactWarning;return s?a.ref:(o=(f=Object.getOwnPropertyDescriptor(a,"ref"))==null?void 0:f.get,s=o&&"isReactWarning"in o&&o.isReactWarning,s?a.props.ref:a.props.ref||a.ref)}var eu=0;function Pv(){x.useEffect(()=>{const a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??sv()),document.body.insertAdjacentElement("beforeend",a[1]??sv()),eu++,()=>{eu===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(o=>o.remove()),eu--}},[])}function sv(){const a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var nn=function(){return nn=Object.assign||function(o){for(var s,c=1,f=arguments.length;c<f;c++){s=arguments[c];for(var m in s)Object.prototype.hasOwnProperty.call(s,m)&&(o[m]=s[m])}return o},nn.apply(this,arguments)};function Fv(a,o){var s={};for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&o.indexOf(c)<0&&(s[c]=a[c]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var f=0,c=Object.getOwnPropertySymbols(a);f<c.length;f++)o.indexOf(c[f])<0&&Object.prototype.propertyIsEnumerable.call(a,c[f])&&(s[c[f]]=a[c[f]]);return s}function u1(a,o,s){if(s||arguments.length===2)for(var c=0,f=o.length,m;c<f;c++)(m||!(c in o))&&(m||(m=Array.prototype.slice.call(o,0,c)),m[c]=o[c]);return a.concat(m||Array.prototype.slice.call(o))}var fo="right-scroll-bar-position",mo="width-before-scroll-bar",f1="with-scroll-bars-hidden",d1="--removed-body-scroll-bar-size";function tu(a,o){return typeof a=="function"?a(o):a&&(a.current=o),a}function m1(a,o){var s=x.useState(function(){return{value:a,callback:o,facade:{get current(){return s.value},set current(c){var f=s.value;f!==c&&(s.value=c,s.callback(c,f))}}}})[0];return s.callback=o,s.facade}var h1=typeof window<"u"?x.useLayoutEffect:x.useEffect,uv=new WeakMap;function v1(a,o){var s=m1(null,function(c){return a.forEach(function(f){return tu(f,c)})});return h1(function(){var c=uv.get(s);if(c){var f=new Set(c),m=new Set(a),h=s.current;f.forEach(function(v){m.has(v)||tu(v,null)}),m.forEach(function(v){f.has(v)||tu(v,h)})}uv.set(s,a)},[a]),s}function g1(a){return a}function p1(a,o){o===void 0&&(o=g1);var s=[],c=!1,f={read:function(){if(c)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:a},useMedium:function(m){var h=o(m,c);return s.push(h),function(){s=s.filter(function(v){return v!==h})}},assignSyncMedium:function(m){for(c=!0;s.length;){var h=s;s=[],h.forEach(m)}s={push:function(v){return m(v)},filter:function(){return s}}},assignMedium:function(m){c=!0;var h=[];if(s.length){var v=s;s=[],v.forEach(m),h=s}var y=function(){var S=h;h=[],S.forEach(m)},p=function(){return Promise.resolve().then(y)};p(),s={push:function(S){h.push(S),p()},filter:function(S){return h=h.filter(S),s}}}};return f}function y1(a){a===void 0&&(a={});var o=p1(null);return o.options=nn({async:!0,ssr:!1},a),o}var Iv=function(a){var o=a.sideCar,s=Fv(a,["sideCar"]);if(!o)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var c=o.read();if(!c)throw new Error("Sidecar medium not found");return x.createElement(c,nn({},s))};Iv.isSideCarExport=!0;function x1(a,o){return a.useMedium(o),Iv}var eg=y1(),nu=function(){},No=x.forwardRef(function(a,o){var s=x.useRef(null),c=x.useState({onScrollCapture:nu,onWheelCapture:nu,onTouchMoveCapture:nu}),f=c[0],m=c[1],h=a.forwardProps,v=a.children,y=a.className,p=a.removeScrollBar,S=a.enabled,N=a.shards,C=a.sideCar,O=a.noIsolation,R=a.inert,b=a.allowPinchZoom,T=a.as,L=T===void 0?"div":T,D=a.gapMode,B=Fv(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),K=C,V=v1([s,o]),ee=nn(nn({},B),f);return x.createElement(x.Fragment,null,S&&x.createElement(K,{sideCar:eg,removeScrollBar:p,shards:N,noIsolation:O,inert:R,setCallbacks:m,allowPinchZoom:!!b,lockRef:s,gapMode:D}),h?x.cloneElement(x.Children.only(v),nn(nn({},ee),{ref:V})):x.createElement(L,nn({},ee,{className:y,ref:V}),v))});No.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};No.classNames={fullWidth:mo,zeroRight:fo};var b1=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function S1(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var o=b1();return o&&a.setAttribute("nonce",o),a}function w1(a,o){a.styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o))}function E1(a){var o=document.head||document.getElementsByTagName("head")[0];o.appendChild(a)}var N1=function(){var a=0,o=null;return{add:function(s){a==0&&(o=S1())&&(w1(o,s),E1(o)),a++},remove:function(){a--,!a&&o&&(o.parentNode&&o.parentNode.removeChild(o),o=null)}}},A1=function(){var a=N1();return function(o,s){x.useEffect(function(){return a.add(o),function(){a.remove()}},[o&&s])}},tg=function(){var a=A1(),o=function(s){var c=s.styles,f=s.dynamic;return a(c,f),null};return o},T1={left:0,top:0,right:0,gap:0},lu=function(a){return parseInt(a||"",10)||0},C1=function(a){var o=window.getComputedStyle(document.body),s=o[a==="padding"?"paddingLeft":"marginLeft"],c=o[a==="padding"?"paddingTop":"marginTop"],f=o[a==="padding"?"paddingRight":"marginRight"];return[lu(s),lu(c),lu(f)]},j1=function(a){if(a===void 0&&(a="margin"),typeof window>"u")return T1;var o=C1(a),s=document.documentElement.clientWidth,c=window.innerWidth;return{left:o[0],top:o[1],right:o[2],gap:Math.max(0,c-s+o[2]-o[0])}},_1=tg(),Ea="data-scroll-locked",O1=function(a,o,s,c){var f=a.left,m=a.top,h=a.right,v=a.gap;return s===void 0&&(s="margin"),`
  .`.concat(f1,` {
   overflow: hidden `).concat(c,`;
   padding-right: `).concat(v,"px ").concat(c,`;
  }
  body[`).concat(Ea,`] {
    overflow: hidden `).concat(c,`;
    overscroll-behavior: contain;
    `).concat([o&&"position: relative ".concat(c,";"),s==="margin"&&`
    padding-left: `.concat(f,`px;
    padding-top: `).concat(m,`px;
    padding-right: `).concat(h,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(v,"px ").concat(c,`;
    `),s==="padding"&&"padding-right: ".concat(v,"px ").concat(c,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(fo,` {
    right: `).concat(v,"px ").concat(c,`;
  }
  
  .`).concat(mo,` {
    margin-right: `).concat(v,"px ").concat(c,`;
  }
  
  .`).concat(fo," .").concat(fo,` {
    right: 0 `).concat(c,`;
  }
  
  .`).concat(mo," .").concat(mo,` {
    margin-right: 0 `).concat(c,`;
  }
  
  body[`).concat(Ea,`] {
    `).concat(d1,": ").concat(v,`px;
  }
`)},fv=function(){var a=parseInt(document.body.getAttribute(Ea)||"0",10);return isFinite(a)?a:0},M1=function(){x.useEffect(function(){return document.body.setAttribute(Ea,(fv()+1).toString()),function(){var a=fv()-1;a<=0?document.body.removeAttribute(Ea):document.body.setAttribute(Ea,a.toString())}},[])},R1=function(a){var o=a.noRelative,s=a.noImportant,c=a.gapMode,f=c===void 0?"margin":c;M1();var m=x.useMemo(function(){return j1(f)},[f]);return x.createElement(_1,{styles:O1(m,!o,f,s?"":"!important")})},pu=!1;if(typeof window<"u")try{var ao=Object.defineProperty({},"passive",{get:function(){return pu=!0,!0}});window.addEventListener("test",ao,ao),window.removeEventListener("test",ao,ao)}catch{pu=!1}var pa=pu?{passive:!1}:!1,D1=function(a){return a.tagName==="TEXTAREA"},ng=function(a,o){if(!(a instanceof Element))return!1;var s=window.getComputedStyle(a);return s[o]!=="hidden"&&!(s.overflowY===s.overflowX&&!D1(a)&&s[o]==="visible")},z1=function(a){return ng(a,"overflowY")},U1=function(a){return ng(a,"overflowX")},dv=function(a,o){var s=o.ownerDocument,c=o;do{typeof ShadowRoot<"u"&&c instanceof ShadowRoot&&(c=c.host);var f=lg(a,c);if(f){var m=ag(a,c),h=m[1],v=m[2];if(h>v)return!0}c=c.parentNode}while(c&&c!==s.body);return!1},H1=function(a){var o=a.scrollTop,s=a.scrollHeight,c=a.clientHeight;return[o,s,c]},L1=function(a){var o=a.scrollLeft,s=a.scrollWidth,c=a.clientWidth;return[o,s,c]},lg=function(a,o){return a==="v"?z1(o):U1(o)},ag=function(a,o){return a==="v"?H1(o):L1(o)},B1=function(a,o){return a==="h"&&o==="rtl"?-1:1},k1=function(a,o,s,c,f){var m=B1(a,window.getComputedStyle(o).direction),h=m*c,v=s.target,y=o.contains(v),p=!1,S=h>0,N=0,C=0;do{var O=ag(a,v),R=O[0],b=O[1],T=O[2],L=b-T-m*R;(R||L)&&lg(a,v)&&(N+=L,C+=R),v instanceof ShadowRoot?v=v.host:v=v.parentNode}while(!y&&v!==document.body||y&&(o.contains(v)||o===v));return(S&&Math.abs(N)<1||!S&&Math.abs(C)<1)&&(p=!0),p},io=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},mv=function(a){return[a.deltaX,a.deltaY]},hv=function(a){return a&&"current"in a?a.current:a},q1=function(a,o){return a[0]===o[0]&&a[1]===o[1]},V1=function(a){return`
  .block-interactivity-`.concat(a,` {pointer-events: none;}
  .allow-interactivity-`).concat(a,` {pointer-events: all;}
`)},Y1=0,ya=[];function G1(a){var o=x.useRef([]),s=x.useRef([0,0]),c=x.useRef(),f=x.useState(Y1++)[0],m=x.useState(tg)[0],h=x.useRef(a);x.useEffect(function(){h.current=a},[a]),x.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(f));var b=u1([a.lockRef.current],(a.shards||[]).map(hv),!0).filter(Boolean);return b.forEach(function(T){return T.classList.add("allow-interactivity-".concat(f))}),function(){document.body.classList.remove("block-interactivity-".concat(f)),b.forEach(function(T){return T.classList.remove("allow-interactivity-".concat(f))})}}},[a.inert,a.lockRef.current,a.shards]);var v=x.useCallback(function(b,T){if("touches"in b&&b.touches.length===2||b.type==="wheel"&&b.ctrlKey)return!h.current.allowPinchZoom;var L=io(b),D=s.current,B="deltaX"in b?b.deltaX:D[0]-L[0],K="deltaY"in b?b.deltaY:D[1]-L[1],V,ee=b.target,P=Math.abs(B)>Math.abs(K)?"h":"v";if("touches"in b&&P==="h"&&ee.type==="range")return!1;var $=dv(P,ee);if(!$)return!0;if($?V=P:(V=P==="v"?"h":"v",$=dv(P,ee)),!$)return!1;if(!c.current&&"changedTouches"in b&&(B||K)&&(c.current=V),!V)return!0;var se=c.current||V;return k1(se,T,b,se==="h"?B:K)},[]),y=x.useCallback(function(b){var T=b;if(!(!ya.length||ya[ya.length-1]!==m)){var L="deltaY"in T?mv(T):io(T),D=o.current.filter(function(V){return V.name===T.type&&(V.target===T.target||T.target===V.shadowParent)&&q1(V.delta,L)})[0];if(D&&D.should){T.cancelable&&T.preventDefault();return}if(!D){var B=(h.current.shards||[]).map(hv).filter(Boolean).filter(function(V){return V.contains(T.target)}),K=B.length>0?v(T,B[0]):!h.current.noIsolation;K&&T.cancelable&&T.preventDefault()}}},[]),p=x.useCallback(function(b,T,L,D){var B={name:b,delta:T,target:L,should:D,shadowParent:X1(L)};o.current.push(B),setTimeout(function(){o.current=o.current.filter(function(K){return K!==B})},1)},[]),S=x.useCallback(function(b){s.current=io(b),c.current=void 0},[]),N=x.useCallback(function(b){p(b.type,mv(b),b.target,v(b,a.lockRef.current))},[]),C=x.useCallback(function(b){p(b.type,io(b),b.target,v(b,a.lockRef.current))},[]);x.useEffect(function(){return ya.push(m),a.setCallbacks({onScrollCapture:N,onWheelCapture:N,onTouchMoveCapture:C}),document.addEventListener("wheel",y,pa),document.addEventListener("touchmove",y,pa),document.addEventListener("touchstart",S,pa),function(){ya=ya.filter(function(b){return b!==m}),document.removeEventListener("wheel",y,pa),document.removeEventListener("touchmove",y,pa),document.removeEventListener("touchstart",S,pa)}},[]);var O=a.removeScrollBar,R=a.inert;return x.createElement(x.Fragment,null,R?x.createElement(m,{styles:V1(f)}):null,O?x.createElement(R1,{gapMode:a.gapMode}):null)}function X1(a){for(var o=null;a!==null;)a instanceof ShadowRoot&&(o=a.host,a=a.host),a=a.parentNode;return o}const Q1=x1(eg,G1);var Mu=x.forwardRef(function(a,o){return x.createElement(No,nn({},a,{ref:o,sideCar:Q1}))});Mu.classNames=No.classNames;var Z1=function(a){if(typeof document>"u")return null;var o=Array.isArray(a)?a[0]:a;return o.ownerDocument.body},xa=new WeakMap,ro=new WeakMap,oo={},au=0,ig=function(a){return a&&(a.host||ig(a.parentNode))},K1=function(a,o){return o.map(function(s){if(a.contains(s))return s;var c=ig(s);return c&&a.contains(c)?c:(console.error("aria-hidden",s,"in not contained inside",a,". Doing nothing"),null)}).filter(function(s){return!!s})},J1=function(a,o,s,c){var f=K1(o,Array.isArray(a)?a:[a]);oo[s]||(oo[s]=new WeakMap);var m=oo[s],h=[],v=new Set,y=new Set(f),p=function(N){!N||v.has(N)||(v.add(N),p(N.parentNode))};f.forEach(p);var S=function(N){!N||y.has(N)||Array.prototype.forEach.call(N.children,function(C){if(v.has(C))S(C);else try{var O=C.getAttribute(c),R=O!==null&&O!=="false",b=(xa.get(C)||0)+1,T=(m.get(C)||0)+1;xa.set(C,b),m.set(C,T),h.push(C),b===1&&R&&ro.set(C,!0),T===1&&C.setAttribute(s,"true"),R||C.setAttribute(c,"true")}catch(L){console.error("aria-hidden: cannot operate on ",C,L)}})};return S(o),v.clear(),au++,function(){h.forEach(function(N){var C=xa.get(N)-1,O=m.get(N)-1;xa.set(N,C),m.set(N,O),C||(ro.has(N)||N.removeAttribute(c),ro.delete(N)),O||N.removeAttribute(s)}),au--,au||(xa=new WeakMap,xa=new WeakMap,ro=new WeakMap,oo={})}},rg=function(a,o,s){s===void 0&&(s="data-aria-hidden");var c=Array.from(Array.isArray(a)?a:[a]),f=Z1(a);return f?(c.push.apply(c,Array.from(f.querySelectorAll("[aria-live]"))),J1(c,f,s,"aria-hidden")):function(){return null}},Ao="Dialog",[og,xw]=wo(Ao),[$1,Wt]=og(Ao),cg=a=>{const{__scopeDialog:o,children:s,open:c,defaultOpen:f,onOpenChange:m,modal:h=!0}=a,v=x.useRef(null),y=x.useRef(null),[p,S]=vu({prop:c,defaultProp:f??!1,onChange:m,caller:Ao});return u.jsx($1,{scope:o,triggerRef:v,contentRef:y,contentId:wa(),titleId:wa(),descriptionId:wa(),open:p,onOpenChange:S,onOpenToggle:x.useCallback(()=>S(N=>!N),[S]),modal:h,children:s})};cg.displayName=Ao;var sg="DialogTrigger",ug=x.forwardRef((a,o)=>{const{__scopeDialog:s,...c}=a,f=Wt(sg,s),m=Qe(o,f.triggerRef);return u.jsx(Le.button,{type:"button","aria-haspopup":"dialog","aria-expanded":f.open,"aria-controls":f.contentId,"data-state":zu(f.open),...c,ref:m,onClick:ke(a.onClick,f.onOpenToggle)})});ug.displayName=sg;var Ru="DialogPortal",[W1,fg]=og(Ru,{forceMount:void 0}),dg=a=>{const{__scopeDialog:o,forceMount:s,children:c,container:f}=a,m=Wt(Ru,o);return u.jsx(W1,{scope:o,forceMount:s,children:x.Children.map(c,h=>u.jsx(Eo,{present:s||m.open,children:u.jsx(Ou,{asChild:!0,container:f,children:h})}))})};dg.displayName=Ru;var go="DialogOverlay",mg=x.forwardRef((a,o)=>{const s=fg(go,a.__scopeDialog),{forceMount:c=s.forceMount,...f}=a,m=Wt(go,a.__scopeDialog);return m.modal?u.jsx(Eo,{present:c||m.open,children:u.jsx(F1,{...f,ref:o})}):null});mg.displayName=go;var P1=Aa("DialogOverlay.RemoveScroll"),F1=x.forwardRef((a,o)=>{const{__scopeDialog:s,...c}=a,f=Wt(go,s);return u.jsx(Mu,{as:P1,allowPinchZoom:!0,shards:[f.contentRef],children:u.jsx(Le.div,{"data-state":zu(f.open),...c,ref:o,style:{pointerEvents:"auto",...c.style}})})}),Cl="DialogContent",hg=x.forwardRef((a,o)=>{const s=fg(Cl,a.__scopeDialog),{forceMount:c=s.forceMount,...f}=a,m=Wt(Cl,a.__scopeDialog);return u.jsx(Eo,{present:c||m.open,children:m.modal?u.jsx(I1,{...f,ref:o}):u.jsx(e2,{...f,ref:o})})});hg.displayName=Cl;var I1=x.forwardRef((a,o)=>{const s=Wt(Cl,a.__scopeDialog),c=x.useRef(null),f=Qe(o,s.contentRef,c);return x.useEffect(()=>{const m=c.current;if(m)return rg(m)},[]),u.jsx(vg,{...a,ref:f,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:ke(a.onCloseAutoFocus,m=>{var h;m.preventDefault(),(h=s.triggerRef.current)==null||h.focus()}),onPointerDownOutside:ke(a.onPointerDownOutside,m=>{const h=m.detail.originalEvent,v=h.button===0&&h.ctrlKey===!0;(h.button===2||v)&&m.preventDefault()}),onFocusOutside:ke(a.onFocusOutside,m=>m.preventDefault())})}),e2=x.forwardRef((a,o)=>{const s=Wt(Cl,a.__scopeDialog),c=x.useRef(!1),f=x.useRef(!1);return u.jsx(vg,{...a,ref:o,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:m=>{var h,v;(h=a.onCloseAutoFocus)==null||h.call(a,m),m.defaultPrevented||(c.current||(v=s.triggerRef.current)==null||v.focus(),m.preventDefault()),c.current=!1,f.current=!1},onInteractOutside:m=>{var y,p;(y=a.onInteractOutside)==null||y.call(a,m),m.defaultPrevented||(c.current=!0,m.detail.originalEvent.type==="pointerdown"&&(f.current=!0));const h=m.target;((p=s.triggerRef.current)==null?void 0:p.contains(h))&&m.preventDefault(),m.detail.originalEvent.type==="focusin"&&f.current&&m.preventDefault()}})}),vg=x.forwardRef((a,o)=>{const{__scopeDialog:s,trapFocus:c,onOpenAutoFocus:f,onCloseAutoFocus:m,...h}=a,v=Wt(Cl,s),y=x.useRef(null),p=Qe(o,y);return Pv(),u.jsxs(u.Fragment,{children:[u.jsx(_u,{asChild:!0,loop:!0,trapped:c,onMountAutoFocus:f,onUnmountAutoFocus:m,children:u.jsx(ju,{role:"dialog",id:v.contentId,"aria-describedby":v.descriptionId,"aria-labelledby":v.titleId,"data-state":zu(v.open),...h,ref:p,onDismiss:()=>v.onOpenChange(!1)})}),u.jsxs(u.Fragment,{children:[u.jsx(t2,{titleId:v.titleId}),u.jsx(l2,{contentRef:y,descriptionId:v.descriptionId})]})]})}),Du="DialogTitle",gg=x.forwardRef((a,o)=>{const{__scopeDialog:s,...c}=a,f=Wt(Du,s);return u.jsx(Le.h2,{id:f.titleId,...c,ref:o})});gg.displayName=Du;var pg="DialogDescription",yg=x.forwardRef((a,o)=>{const{__scopeDialog:s,...c}=a,f=Wt(pg,s);return u.jsx(Le.p,{id:f.descriptionId,...c,ref:o})});yg.displayName=pg;var xg="DialogClose",bg=x.forwardRef((a,o)=>{const{__scopeDialog:s,...c}=a,f=Wt(xg,s);return u.jsx(Le.button,{type:"button",...c,ref:o,onClick:ke(a.onClick,()=>f.onOpenChange(!1))})});bg.displayName=xg;function zu(a){return a?"open":"closed"}var Sg="DialogTitleWarning",[bw,wg]=Ub(Sg,{contentName:Cl,titleName:Du,docsSlug:"dialog"}),t2=({titleId:a})=>{const o=wg(Sg),s=`\`${o.contentName}\` requires a \`${o.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${o.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${o.docsSlug}`;return x.useEffect(()=>{a&&(document.getElementById(a)||console.error(s))},[s,a]),null},n2="DialogDescriptionWarning",l2=({contentRef:a,descriptionId:o})=>{const c=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${wg(n2).contentName}}.`;return x.useEffect(()=>{var m;const f=(m=a.current)==null?void 0:m.getAttribute("aria-describedby");o&&f&&(document.getElementById(o)||console.warn(c))},[c,a,o]),null},a2=cg,i2=ug,r2=dg,o2=mg,c2=hg,s2=gg,u2=yg,f2=bg;function Eg({...a}){return u.jsx(a2,{"data-slot":"dialog",...a})}function Ng({...a}){return u.jsx(i2,{"data-slot":"dialog-trigger",...a})}function d2({...a}){return u.jsx(r2,{"data-slot":"dialog-portal",...a})}function m2({className:a,...o}){return u.jsx(o2,{"data-slot":"dialog-overlay",className:Je("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...o})}function Ag({className:a,children:o,...s}){return u.jsxs(d2,{"data-slot":"dialog-portal",children:[u.jsx(m2,{}),u.jsxs(c2,{"data-slot":"dialog-content",className:Je("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...s,children:[o,u.jsxs(f2,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[u.jsx(Rv,{}),u.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Tg({className:a,...o}){return u.jsx("div",{"data-slot":"dialog-header",className:Je("flex flex-col gap-2 text-center sm:text-left",a),...o})}function Cg({className:a,...o}){return u.jsx(s2,{"data-slot":"dialog-title",className:Je("text-lg leading-none font-semibold",a),...o})}function jg({className:a,...o}){return u.jsx(u2,{"data-slot":"dialog-description",className:Je("text-muted-foreground text-sm",a),...o})}var h2="Label",_g=x.forwardRef((a,o)=>u.jsx(Le.label,{...a,ref:o,onMouseDown:s=>{var f;s.target.closest("button, input, select, textarea")||((f=a.onMouseDown)==null||f.call(a,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));_g.displayName=h2;var v2=_g;function Kt({className:a,...o}){return u.jsx(v2,{"data-slot":"label",className:Je("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...o})}function Og({className:a,...o}){return u.jsx("textarea",{"data-slot":"textarea",className:Je("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...o})}function vv(a,[o,s]){return Math.min(s,Math.max(o,a))}function g2(a){const o=a+"CollectionProvider",[s,c]=wo(o),[f,m]=s(o,{collectionRef:{current:null},itemMap:new Map}),h=b=>{const{scope:T,children:L}=b,D=Fn.useRef(null),B=Fn.useRef(new Map).current;return u.jsx(f,{scope:T,itemMap:B,collectionRef:D,children:L})};h.displayName=o;const v=a+"CollectionSlot",y=Aa(v),p=Fn.forwardRef((b,T)=>{const{scope:L,children:D}=b,B=m(v,L),K=Qe(T,B.collectionRef);return u.jsx(y,{ref:K,children:D})});p.displayName=v;const S=a+"CollectionItemSlot",N="data-radix-collection-item",C=Aa(S),O=Fn.forwardRef((b,T)=>{const{scope:L,children:D,...B}=b,K=Fn.useRef(null),V=Qe(T,K),ee=m(S,L);return Fn.useEffect(()=>(ee.itemMap.set(K,{ref:K,...B}),()=>void ee.itemMap.delete(K))),u.jsx(C,{[N]:"",ref:V,children:D})});O.displayName=S;function R(b){const T=m(a+"CollectionConsumer",b);return Fn.useCallback(()=>{const D=T.collectionRef.current;if(!D)return[];const B=Array.from(D.querySelectorAll(`[${N}]`));return Array.from(T.itemMap.values()).sort((ee,P)=>B.indexOf(ee.ref.current)-B.indexOf(P.ref.current))},[T.collectionRef,T.itemMap])}return[{Provider:h,Slot:p,ItemSlot:O},R,c]}var p2=x.createContext(void 0);function y2(a){const o=x.useContext(p2);return a||o||"ltr"}const x2=["top","right","bottom","left"],el=Math.min,Rt=Math.max,po=Math.round,co=Math.floor,ln=a=>({x:a,y:a}),b2={left:"right",right:"left",bottom:"top",top:"bottom"},S2={start:"end",end:"start"};function yu(a,o,s){return Rt(a,el(o,s))}function An(a,o){return typeof a=="function"?a(o):a}function Tn(a){return a.split("-")[0]}function _a(a){return a.split("-")[1]}function Uu(a){return a==="x"?"y":"x"}function Hu(a){return a==="y"?"height":"width"}function Nn(a){return["top","bottom"].includes(Tn(a))?"y":"x"}function Lu(a){return Uu(Nn(a))}function w2(a,o,s){s===void 0&&(s=!1);const c=_a(a),f=Lu(a),m=Hu(f);let h=f==="x"?c===(s?"end":"start")?"right":"left":c==="start"?"bottom":"top";return o.reference[m]>o.floating[m]&&(h=yo(h)),[h,yo(h)]}function E2(a){const o=yo(a);return[xu(a),o,xu(o)]}function xu(a){return a.replace(/start|end/g,o=>S2[o])}function N2(a,o,s){const c=["left","right"],f=["right","left"],m=["top","bottom"],h=["bottom","top"];switch(a){case"top":case"bottom":return s?o?f:c:o?c:f;case"left":case"right":return o?m:h;default:return[]}}function A2(a,o,s,c){const f=_a(a);let m=N2(Tn(a),s==="start",c);return f&&(m=m.map(h=>h+"-"+f),o&&(m=m.concat(m.map(xu)))),m}function yo(a){return a.replace(/left|right|bottom|top/g,o=>b2[o])}function T2(a){return{top:0,right:0,bottom:0,left:0,...a}}function Mg(a){return typeof a!="number"?T2(a):{top:a,right:a,bottom:a,left:a}}function xo(a){const{x:o,y:s,width:c,height:f}=a;return{width:c,height:f,top:s,left:o,right:o+c,bottom:s+f,x:o,y:s}}function gv(a,o,s){let{reference:c,floating:f}=a;const m=Nn(o),h=Lu(o),v=Hu(h),y=Tn(o),p=m==="y",S=c.x+c.width/2-f.width/2,N=c.y+c.height/2-f.height/2,C=c[v]/2-f[v]/2;let O;switch(y){case"top":O={x:S,y:c.y-f.height};break;case"bottom":O={x:S,y:c.y+c.height};break;case"right":O={x:c.x+c.width,y:N};break;case"left":O={x:c.x-f.width,y:N};break;default:O={x:c.x,y:c.y}}switch(_a(o)){case"start":O[h]-=C*(s&&p?-1:1);break;case"end":O[h]+=C*(s&&p?-1:1);break}return O}const C2=async(a,o,s)=>{const{placement:c="bottom",strategy:f="absolute",middleware:m=[],platform:h}=s,v=m.filter(Boolean),y=await(h.isRTL==null?void 0:h.isRTL(o));let p=await h.getElementRects({reference:a,floating:o,strategy:f}),{x:S,y:N}=gv(p,c,y),C=c,O={},R=0;for(let b=0;b<v.length;b++){const{name:T,fn:L}=v[b],{x:D,y:B,data:K,reset:V}=await L({x:S,y:N,initialPlacement:c,placement:C,strategy:f,middlewareData:O,rects:p,platform:h,elements:{reference:a,floating:o}});S=D??S,N=B??N,O={...O,[T]:{...O[T],...K}},V&&R<=50&&(R++,typeof V=="object"&&(V.placement&&(C=V.placement),V.rects&&(p=V.rects===!0?await h.getElementRects({reference:a,floating:o,strategy:f}):V.rects),{x:S,y:N}=gv(p,C,y)),b=-1)}return{x:S,y:N,placement:C,strategy:f,middlewareData:O}};async function Li(a,o){var s;o===void 0&&(o={});const{x:c,y:f,platform:m,rects:h,elements:v,strategy:y}=a,{boundary:p="clippingAncestors",rootBoundary:S="viewport",elementContext:N="floating",altBoundary:C=!1,padding:O=0}=An(o,a),R=Mg(O),T=v[C?N==="floating"?"reference":"floating":N],L=xo(await m.getClippingRect({element:(s=await(m.isElement==null?void 0:m.isElement(T)))==null||s?T:T.contextElement||await(m.getDocumentElement==null?void 0:m.getDocumentElement(v.floating)),boundary:p,rootBoundary:S,strategy:y})),D=N==="floating"?{x:c,y:f,width:h.floating.width,height:h.floating.height}:h.reference,B=await(m.getOffsetParent==null?void 0:m.getOffsetParent(v.floating)),K=await(m.isElement==null?void 0:m.isElement(B))?await(m.getScale==null?void 0:m.getScale(B))||{x:1,y:1}:{x:1,y:1},V=xo(m.convertOffsetParentRelativeRectToViewportRelativeRect?await m.convertOffsetParentRelativeRectToViewportRelativeRect({elements:v,rect:D,offsetParent:B,strategy:y}):D);return{top:(L.top-V.top+R.top)/K.y,bottom:(V.bottom-L.bottom+R.bottom)/K.y,left:(L.left-V.left+R.left)/K.x,right:(V.right-L.right+R.right)/K.x}}const j2=a=>({name:"arrow",options:a,async fn(o){const{x:s,y:c,placement:f,rects:m,platform:h,elements:v,middlewareData:y}=o,{element:p,padding:S=0}=An(a,o)||{};if(p==null)return{};const N=Mg(S),C={x:s,y:c},O=Lu(f),R=Hu(O),b=await h.getDimensions(p),T=O==="y",L=T?"top":"left",D=T?"bottom":"right",B=T?"clientHeight":"clientWidth",K=m.reference[R]+m.reference[O]-C[O]-m.floating[R],V=C[O]-m.reference[O],ee=await(h.getOffsetParent==null?void 0:h.getOffsetParent(p));let P=ee?ee[B]:0;(!P||!await(h.isElement==null?void 0:h.isElement(ee)))&&(P=v.floating[B]||m.floating[R]);const $=K/2-V/2,se=P/2-b[R]/2-1,J=el(N[L],se),ue=el(N[D],se),oe=J,ve=P-b[R]-ue,pe=P/2-b[R]/2+$,Y=yu(oe,pe,ve),_=!y.arrow&&_a(f)!=null&&pe!==Y&&m.reference[R]/2-(pe<oe?J:ue)-b[R]/2<0,Z=_?pe<oe?pe-oe:pe-ve:0;return{[O]:C[O]+Z,data:{[O]:Y,centerOffset:pe-Y-Z,..._&&{alignmentOffset:Z}},reset:_}}}),_2=function(a){return a===void 0&&(a={}),{name:"flip",options:a,async fn(o){var s,c;const{placement:f,middlewareData:m,rects:h,initialPlacement:v,platform:y,elements:p}=o,{mainAxis:S=!0,crossAxis:N=!0,fallbackPlacements:C,fallbackStrategy:O="bestFit",fallbackAxisSideDirection:R="none",flipAlignment:b=!0,...T}=An(a,o);if((s=m.arrow)!=null&&s.alignmentOffset)return{};const L=Tn(f),D=Nn(v),B=Tn(v)===v,K=await(y.isRTL==null?void 0:y.isRTL(p.floating)),V=C||(B||!b?[yo(v)]:E2(v)),ee=R!=="none";!C&&ee&&V.push(...A2(v,b,R,K));const P=[v,...V],$=await Li(o,T),se=[];let J=((c=m.flip)==null?void 0:c.overflows)||[];if(S&&se.push($[L]),N){const Y=w2(f,h,K);se.push($[Y[0]],$[Y[1]])}if(J=[...J,{placement:f,overflows:se}],!se.every(Y=>Y<=0)){var ue,oe;const Y=(((ue=m.flip)==null?void 0:ue.index)||0)+1,_=P[Y];if(_){var ve;const k=N==="alignment"?D!==Nn(_):!1,ie=((ve=J[0])==null?void 0:ve.overflows[0])>0;if(!k||ie)return{data:{index:Y,overflows:J},reset:{placement:_}}}let Z=(oe=J.filter(k=>k.overflows[0]<=0).sort((k,ie)=>k.overflows[1]-ie.overflows[1])[0])==null?void 0:oe.placement;if(!Z)switch(O){case"bestFit":{var pe;const k=(pe=J.filter(ie=>{if(ee){const E=Nn(ie.placement);return E===D||E==="y"}return!0}).map(ie=>[ie.placement,ie.overflows.filter(E=>E>0).reduce((E,G)=>E+G,0)]).sort((ie,E)=>ie[1]-E[1])[0])==null?void 0:pe[0];k&&(Z=k);break}case"initialPlacement":Z=v;break}if(f!==Z)return{reset:{placement:Z}}}return{}}}};function pv(a,o){return{top:a.top-o.height,right:a.right-o.width,bottom:a.bottom-o.height,left:a.left-o.width}}function yv(a){return x2.some(o=>a[o]>=0)}const O2=function(a){return a===void 0&&(a={}),{name:"hide",options:a,async fn(o){const{rects:s}=o,{strategy:c="referenceHidden",...f}=An(a,o);switch(c){case"referenceHidden":{const m=await Li(o,{...f,elementContext:"reference"}),h=pv(m,s.reference);return{data:{referenceHiddenOffsets:h,referenceHidden:yv(h)}}}case"escaped":{const m=await Li(o,{...f,altBoundary:!0}),h=pv(m,s.floating);return{data:{escapedOffsets:h,escaped:yv(h)}}}default:return{}}}}};async function M2(a,o){const{placement:s,platform:c,elements:f}=a,m=await(c.isRTL==null?void 0:c.isRTL(f.floating)),h=Tn(s),v=_a(s),y=Nn(s)==="y",p=["left","top"].includes(h)?-1:1,S=m&&y?-1:1,N=An(o,a);let{mainAxis:C,crossAxis:O,alignmentAxis:R}=typeof N=="number"?{mainAxis:N,crossAxis:0,alignmentAxis:null}:{mainAxis:N.mainAxis||0,crossAxis:N.crossAxis||0,alignmentAxis:N.alignmentAxis};return v&&typeof R=="number"&&(O=v==="end"?R*-1:R),y?{x:O*S,y:C*p}:{x:C*p,y:O*S}}const R2=function(a){return a===void 0&&(a=0),{name:"offset",options:a,async fn(o){var s,c;const{x:f,y:m,placement:h,middlewareData:v}=o,y=await M2(o,a);return h===((s=v.offset)==null?void 0:s.placement)&&(c=v.arrow)!=null&&c.alignmentOffset?{}:{x:f+y.x,y:m+y.y,data:{...y,placement:h}}}}},D2=function(a){return a===void 0&&(a={}),{name:"shift",options:a,async fn(o){const{x:s,y:c,placement:f}=o,{mainAxis:m=!0,crossAxis:h=!1,limiter:v={fn:T=>{let{x:L,y:D}=T;return{x:L,y:D}}},...y}=An(a,o),p={x:s,y:c},S=await Li(o,y),N=Nn(Tn(f)),C=Uu(N);let O=p[C],R=p[N];if(m){const T=C==="y"?"top":"left",L=C==="y"?"bottom":"right",D=O+S[T],B=O-S[L];O=yu(D,O,B)}if(h){const T=N==="y"?"top":"left",L=N==="y"?"bottom":"right",D=R+S[T],B=R-S[L];R=yu(D,R,B)}const b=v.fn({...o,[C]:O,[N]:R});return{...b,data:{x:b.x-s,y:b.y-c,enabled:{[C]:m,[N]:h}}}}}},z2=function(a){return a===void 0&&(a={}),{options:a,fn(o){const{x:s,y:c,placement:f,rects:m,middlewareData:h}=o,{offset:v=0,mainAxis:y=!0,crossAxis:p=!0}=An(a,o),S={x:s,y:c},N=Nn(f),C=Uu(N);let O=S[C],R=S[N];const b=An(v,o),T=typeof b=="number"?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(y){const B=C==="y"?"height":"width",K=m.reference[C]-m.floating[B]+T.mainAxis,V=m.reference[C]+m.reference[B]-T.mainAxis;O<K?O=K:O>V&&(O=V)}if(p){var L,D;const B=C==="y"?"width":"height",K=["top","left"].includes(Tn(f)),V=m.reference[N]-m.floating[B]+(K&&((L=h.offset)==null?void 0:L[N])||0)+(K?0:T.crossAxis),ee=m.reference[N]+m.reference[B]+(K?0:((D=h.offset)==null?void 0:D[N])||0)-(K?T.crossAxis:0);R<V?R=V:R>ee&&(R=ee)}return{[C]:O,[N]:R}}}},U2=function(a){return a===void 0&&(a={}),{name:"size",options:a,async fn(o){var s,c;const{placement:f,rects:m,platform:h,elements:v}=o,{apply:y=()=>{},...p}=An(a,o),S=await Li(o,p),N=Tn(f),C=_a(f),O=Nn(f)==="y",{width:R,height:b}=m.floating;let T,L;N==="top"||N==="bottom"?(T=N,L=C===(await(h.isRTL==null?void 0:h.isRTL(v.floating))?"start":"end")?"left":"right"):(L=N,T=C==="end"?"top":"bottom");const D=b-S.top-S.bottom,B=R-S.left-S.right,K=el(b-S[T],D),V=el(R-S[L],B),ee=!o.middlewareData.shift;let P=K,$=V;if((s=o.middlewareData.shift)!=null&&s.enabled.x&&($=B),(c=o.middlewareData.shift)!=null&&c.enabled.y&&(P=D),ee&&!C){const J=Rt(S.left,0),ue=Rt(S.right,0),oe=Rt(S.top,0),ve=Rt(S.bottom,0);O?$=R-2*(J!==0||ue!==0?J+ue:Rt(S.left,S.right)):P=b-2*(oe!==0||ve!==0?oe+ve:Rt(S.top,S.bottom))}await y({...o,availableWidth:$,availableHeight:P});const se=await h.getDimensions(v.floating);return R!==se.width||b!==se.height?{reset:{rects:!0}}:{}}}};function To(){return typeof window<"u"}function Oa(a){return Rg(a)?(a.nodeName||"").toLowerCase():"#document"}function Dt(a){var o;return(a==null||(o=a.ownerDocument)==null?void 0:o.defaultView)||window}function rn(a){var o;return(o=(Rg(a)?a.ownerDocument:a.document)||window.document)==null?void 0:o.documentElement}function Rg(a){return To()?a instanceof Node||a instanceof Dt(a).Node:!1}function Jt(a){return To()?a instanceof Element||a instanceof Dt(a).Element:!1}function an(a){return To()?a instanceof HTMLElement||a instanceof Dt(a).HTMLElement:!1}function xv(a){return!To()||typeof ShadowRoot>"u"?!1:a instanceof ShadowRoot||a instanceof Dt(a).ShadowRoot}function qi(a){const{overflow:o,overflowX:s,overflowY:c,display:f}=$t(a);return/auto|scroll|overlay|hidden|clip/.test(o+c+s)&&!["inline","contents"].includes(f)}function H2(a){return["table","td","th"].includes(Oa(a))}function Co(a){return[":popover-open",":modal"].some(o=>{try{return a.matches(o)}catch{return!1}})}function Bu(a){const o=ku(),s=Jt(a)?$t(a):a;return["transform","translate","scale","rotate","perspective"].some(c=>s[c]?s[c]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!o&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!o&&(s.filter?s.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(c=>(s.willChange||"").includes(c))||["paint","layout","strict","content"].some(c=>(s.contain||"").includes(c))}function L2(a){let o=tl(a);for(;an(o)&&!Ta(o);){if(Bu(o))return o;if(Co(o))return null;o=tl(o)}return null}function ku(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ta(a){return["html","body","#document"].includes(Oa(a))}function $t(a){return Dt(a).getComputedStyle(a)}function jo(a){return Jt(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function tl(a){if(Oa(a)==="html")return a;const o=a.assignedSlot||a.parentNode||xv(a)&&a.host||rn(a);return xv(o)?o.host:o}function Dg(a){const o=tl(a);return Ta(o)?a.ownerDocument?a.ownerDocument.body:a.body:an(o)&&qi(o)?o:Dg(o)}function Bi(a,o,s){var c;o===void 0&&(o=[]),s===void 0&&(s=!0);const f=Dg(a),m=f===((c=a.ownerDocument)==null?void 0:c.body),h=Dt(f);if(m){const v=bu(h);return o.concat(h,h.visualViewport||[],qi(f)?f:[],v&&s?Bi(v):[])}return o.concat(f,Bi(f,[],s))}function bu(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function zg(a){const o=$t(a);let s=parseFloat(o.width)||0,c=parseFloat(o.height)||0;const f=an(a),m=f?a.offsetWidth:s,h=f?a.offsetHeight:c,v=po(s)!==m||po(c)!==h;return v&&(s=m,c=h),{width:s,height:c,$:v}}function qu(a){return Jt(a)?a:a.contextElement}function Na(a){const o=qu(a);if(!an(o))return ln(1);const s=o.getBoundingClientRect(),{width:c,height:f,$:m}=zg(o);let h=(m?po(s.width):s.width)/c,v=(m?po(s.height):s.height)/f;return(!h||!Number.isFinite(h))&&(h=1),(!v||!Number.isFinite(v))&&(v=1),{x:h,y:v}}const B2=ln(0);function Ug(a){const o=Dt(a);return!ku()||!o.visualViewport?B2:{x:o.visualViewport.offsetLeft,y:o.visualViewport.offsetTop}}function k2(a,o,s){return o===void 0&&(o=!1),!s||o&&s!==Dt(a)?!1:o}function jl(a,o,s,c){o===void 0&&(o=!1),s===void 0&&(s=!1);const f=a.getBoundingClientRect(),m=qu(a);let h=ln(1);o&&(c?Jt(c)&&(h=Na(c)):h=Na(a));const v=k2(m,s,c)?Ug(m):ln(0);let y=(f.left+v.x)/h.x,p=(f.top+v.y)/h.y,S=f.width/h.x,N=f.height/h.y;if(m){const C=Dt(m),O=c&&Jt(c)?Dt(c):c;let R=C,b=bu(R);for(;b&&c&&O!==R;){const T=Na(b),L=b.getBoundingClientRect(),D=$t(b),B=L.left+(b.clientLeft+parseFloat(D.paddingLeft))*T.x,K=L.top+(b.clientTop+parseFloat(D.paddingTop))*T.y;y*=T.x,p*=T.y,S*=T.x,N*=T.y,y+=B,p+=K,R=Dt(b),b=bu(R)}}return xo({width:S,height:N,x:y,y:p})}function Vu(a,o){const s=jo(a).scrollLeft;return o?o.left+s:jl(rn(a)).left+s}function Hg(a,o,s){s===void 0&&(s=!1);const c=a.getBoundingClientRect(),f=c.left+o.scrollLeft-(s?0:Vu(a,c)),m=c.top+o.scrollTop;return{x:f,y:m}}function q2(a){let{elements:o,rect:s,offsetParent:c,strategy:f}=a;const m=f==="fixed",h=rn(c),v=o?Co(o.floating):!1;if(c===h||v&&m)return s;let y={scrollLeft:0,scrollTop:0},p=ln(1);const S=ln(0),N=an(c);if((N||!N&&!m)&&((Oa(c)!=="body"||qi(h))&&(y=jo(c)),an(c))){const O=jl(c);p=Na(c),S.x=O.x+c.clientLeft,S.y=O.y+c.clientTop}const C=h&&!N&&!m?Hg(h,y,!0):ln(0);return{width:s.width*p.x,height:s.height*p.y,x:s.x*p.x-y.scrollLeft*p.x+S.x+C.x,y:s.y*p.y-y.scrollTop*p.y+S.y+C.y}}function V2(a){return Array.from(a.getClientRects())}function Y2(a){const o=rn(a),s=jo(a),c=a.ownerDocument.body,f=Rt(o.scrollWidth,o.clientWidth,c.scrollWidth,c.clientWidth),m=Rt(o.scrollHeight,o.clientHeight,c.scrollHeight,c.clientHeight);let h=-s.scrollLeft+Vu(a);const v=-s.scrollTop;return $t(c).direction==="rtl"&&(h+=Rt(o.clientWidth,c.clientWidth)-f),{width:f,height:m,x:h,y:v}}function G2(a,o){const s=Dt(a),c=rn(a),f=s.visualViewport;let m=c.clientWidth,h=c.clientHeight,v=0,y=0;if(f){m=f.width,h=f.height;const p=ku();(!p||p&&o==="fixed")&&(v=f.offsetLeft,y=f.offsetTop)}return{width:m,height:h,x:v,y}}function X2(a,o){const s=jl(a,!0,o==="fixed"),c=s.top+a.clientTop,f=s.left+a.clientLeft,m=an(a)?Na(a):ln(1),h=a.clientWidth*m.x,v=a.clientHeight*m.y,y=f*m.x,p=c*m.y;return{width:h,height:v,x:y,y:p}}function bv(a,o,s){let c;if(o==="viewport")c=G2(a,s);else if(o==="document")c=Y2(rn(a));else if(Jt(o))c=X2(o,s);else{const f=Ug(a);c={x:o.x-f.x,y:o.y-f.y,width:o.width,height:o.height}}return xo(c)}function Lg(a,o){const s=tl(a);return s===o||!Jt(s)||Ta(s)?!1:$t(s).position==="fixed"||Lg(s,o)}function Q2(a,o){const s=o.get(a);if(s)return s;let c=Bi(a,[],!1).filter(v=>Jt(v)&&Oa(v)!=="body"),f=null;const m=$t(a).position==="fixed";let h=m?tl(a):a;for(;Jt(h)&&!Ta(h);){const v=$t(h),y=Bu(h);!y&&v.position==="fixed"&&(f=null),(m?!y&&!f:!y&&v.position==="static"&&!!f&&["absolute","fixed"].includes(f.position)||qi(h)&&!y&&Lg(a,h))?c=c.filter(S=>S!==h):f=v,h=tl(h)}return o.set(a,c),c}function Z2(a){let{element:o,boundary:s,rootBoundary:c,strategy:f}=a;const h=[...s==="clippingAncestors"?Co(o)?[]:Q2(o,this._c):[].concat(s),c],v=h[0],y=h.reduce((p,S)=>{const N=bv(o,S,f);return p.top=Rt(N.top,p.top),p.right=el(N.right,p.right),p.bottom=el(N.bottom,p.bottom),p.left=Rt(N.left,p.left),p},bv(o,v,f));return{width:y.right-y.left,height:y.bottom-y.top,x:y.left,y:y.top}}function K2(a){const{width:o,height:s}=zg(a);return{width:o,height:s}}function J2(a,o,s){const c=an(o),f=rn(o),m=s==="fixed",h=jl(a,!0,m,o);let v={scrollLeft:0,scrollTop:0};const y=ln(0);function p(){y.x=Vu(f)}if(c||!c&&!m)if((Oa(o)!=="body"||qi(f))&&(v=jo(o)),c){const O=jl(o,!0,m,o);y.x=O.x+o.clientLeft,y.y=O.y+o.clientTop}else f&&p();m&&!c&&f&&p();const S=f&&!c&&!m?Hg(f,v):ln(0),N=h.left+v.scrollLeft-y.x-S.x,C=h.top+v.scrollTop-y.y-S.y;return{x:N,y:C,width:h.width,height:h.height}}function iu(a){return $t(a).position==="static"}function Sv(a,o){if(!an(a)||$t(a).position==="fixed")return null;if(o)return o(a);let s=a.offsetParent;return rn(a)===s&&(s=s.ownerDocument.body),s}function Bg(a,o){const s=Dt(a);if(Co(a))return s;if(!an(a)){let f=tl(a);for(;f&&!Ta(f);){if(Jt(f)&&!iu(f))return f;f=tl(f)}return s}let c=Sv(a,o);for(;c&&H2(c)&&iu(c);)c=Sv(c,o);return c&&Ta(c)&&iu(c)&&!Bu(c)?s:c||L2(a)||s}const $2=async function(a){const o=this.getOffsetParent||Bg,s=this.getDimensions,c=await s(a.floating);return{reference:J2(a.reference,await o(a.floating),a.strategy),floating:{x:0,y:0,width:c.width,height:c.height}}};function W2(a){return $t(a).direction==="rtl"}const P2={convertOffsetParentRelativeRectToViewportRelativeRect:q2,getDocumentElement:rn,getClippingRect:Z2,getOffsetParent:Bg,getElementRects:$2,getClientRects:V2,getDimensions:K2,getScale:Na,isElement:Jt,isRTL:W2};function kg(a,o){return a.x===o.x&&a.y===o.y&&a.width===o.width&&a.height===o.height}function F2(a,o){let s=null,c;const f=rn(a);function m(){var v;clearTimeout(c),(v=s)==null||v.disconnect(),s=null}function h(v,y){v===void 0&&(v=!1),y===void 0&&(y=1),m();const p=a.getBoundingClientRect(),{left:S,top:N,width:C,height:O}=p;if(v||o(),!C||!O)return;const R=co(N),b=co(f.clientWidth-(S+C)),T=co(f.clientHeight-(N+O)),L=co(S),B={rootMargin:-R+"px "+-b+"px "+-T+"px "+-L+"px",threshold:Rt(0,el(1,y))||1};let K=!0;function V(ee){const P=ee[0].intersectionRatio;if(P!==y){if(!K)return h();P?h(!1,P):c=setTimeout(()=>{h(!1,1e-7)},1e3)}P===1&&!kg(p,a.getBoundingClientRect())&&h(),K=!1}try{s=new IntersectionObserver(V,{...B,root:f.ownerDocument})}catch{s=new IntersectionObserver(V,B)}s.observe(a)}return h(!0),m}function I2(a,o,s,c){c===void 0&&(c={});const{ancestorScroll:f=!0,ancestorResize:m=!0,elementResize:h=typeof ResizeObserver=="function",layoutShift:v=typeof IntersectionObserver=="function",animationFrame:y=!1}=c,p=qu(a),S=f||m?[...p?Bi(p):[],...Bi(o)]:[];S.forEach(L=>{f&&L.addEventListener("scroll",s,{passive:!0}),m&&L.addEventListener("resize",s)});const N=p&&v?F2(p,s):null;let C=-1,O=null;h&&(O=new ResizeObserver(L=>{let[D]=L;D&&D.target===p&&O&&(O.unobserve(o),cancelAnimationFrame(C),C=requestAnimationFrame(()=>{var B;(B=O)==null||B.observe(o)})),s()}),p&&!y&&O.observe(p),O.observe(o));let R,b=y?jl(a):null;y&&T();function T(){const L=jl(a);b&&!kg(b,L)&&s(),b=L,R=requestAnimationFrame(T)}return s(),()=>{var L;S.forEach(D=>{f&&D.removeEventListener("scroll",s),m&&D.removeEventListener("resize",s)}),N==null||N(),(L=O)==null||L.disconnect(),O=null,y&&cancelAnimationFrame(R)}}const eS=R2,tS=D2,nS=_2,lS=U2,aS=O2,wv=j2,iS=z2,rS=(a,o,s)=>{const c=new Map,f={platform:P2,...s},m={...f.platform,_c:c};return C2(a,o,{...f,platform:m})};var ho=typeof document<"u"?x.useLayoutEffect:x.useEffect;function bo(a,o){if(a===o)return!0;if(typeof a!=typeof o)return!1;if(typeof a=="function"&&a.toString()===o.toString())return!0;let s,c,f;if(a&&o&&typeof a=="object"){if(Array.isArray(a)){if(s=a.length,s!==o.length)return!1;for(c=s;c--!==0;)if(!bo(a[c],o[c]))return!1;return!0}if(f=Object.keys(a),s=f.length,s!==Object.keys(o).length)return!1;for(c=s;c--!==0;)if(!{}.hasOwnProperty.call(o,f[c]))return!1;for(c=s;c--!==0;){const m=f[c];if(!(m==="_owner"&&a.$$typeof)&&!bo(a[m],o[m]))return!1}return!0}return a!==a&&o!==o}function qg(a){return typeof window>"u"?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function Ev(a,o){const s=qg(a);return Math.round(o*s)/s}function ru(a){const o=x.useRef(a);return ho(()=>{o.current=a}),o}function oS(a){a===void 0&&(a={});const{placement:o="bottom",strategy:s="absolute",middleware:c=[],platform:f,elements:{reference:m,floating:h}={},transform:v=!0,whileElementsMounted:y,open:p}=a,[S,N]=x.useState({x:0,y:0,strategy:s,placement:o,middlewareData:{},isPositioned:!1}),[C,O]=x.useState(c);bo(C,c)||O(c);const[R,b]=x.useState(null),[T,L]=x.useState(null),D=x.useCallback(k=>{k!==ee.current&&(ee.current=k,b(k))},[]),B=x.useCallback(k=>{k!==P.current&&(P.current=k,L(k))},[]),K=m||R,V=h||T,ee=x.useRef(null),P=x.useRef(null),$=x.useRef(S),se=y!=null,J=ru(y),ue=ru(f),oe=ru(p),ve=x.useCallback(()=>{if(!ee.current||!P.current)return;const k={placement:o,strategy:s,middleware:C};ue.current&&(k.platform=ue.current),rS(ee.current,P.current,k).then(ie=>{const E={...ie,isPositioned:oe.current!==!1};pe.current&&!bo($.current,E)&&($.current=E,ki.flushSync(()=>{N(E)}))})},[C,o,s,ue,oe]);ho(()=>{p===!1&&$.current.isPositioned&&($.current.isPositioned=!1,N(k=>({...k,isPositioned:!1})))},[p]);const pe=x.useRef(!1);ho(()=>(pe.current=!0,()=>{pe.current=!1}),[]),ho(()=>{if(K&&(ee.current=K),V&&(P.current=V),K&&V){if(J.current)return J.current(K,V,ve);ve()}},[K,V,ve,J,se]);const Y=x.useMemo(()=>({reference:ee,floating:P,setReference:D,setFloating:B}),[D,B]),_=x.useMemo(()=>({reference:K,floating:V}),[K,V]),Z=x.useMemo(()=>{const k={position:s,left:0,top:0};if(!_.floating)return k;const ie=Ev(_.floating,S.x),E=Ev(_.floating,S.y);return v?{...k,transform:"translate("+ie+"px, "+E+"px)",...qg(_.floating)>=1.5&&{willChange:"transform"}}:{position:s,left:ie,top:E}},[s,v,_.floating,S.x,S.y]);return x.useMemo(()=>({...S,update:ve,refs:Y,elements:_,floatingStyles:Z}),[S,ve,Y,_,Z])}const cS=a=>{function o(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:a,fn(s){const{element:c,padding:f}=typeof a=="function"?a(s):a;return c&&o(c)?c.current!=null?wv({element:c.current,padding:f}).fn(s):{}:c?wv({element:c,padding:f}).fn(s):{}}}},sS=(a,o)=>({...eS(a),options:[a,o]}),uS=(a,o)=>({...tS(a),options:[a,o]}),fS=(a,o)=>({...iS(a),options:[a,o]}),dS=(a,o)=>({...nS(a),options:[a,o]}),mS=(a,o)=>({...lS(a),options:[a,o]}),hS=(a,o)=>({...aS(a),options:[a,o]}),vS=(a,o)=>({...cS(a),options:[a,o]});var gS="Arrow",Vg=x.forwardRef((a,o)=>{const{children:s,width:c=10,height:f=5,...m}=a;return u.jsx(Le.svg,{...m,ref:o,width:c,height:f,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?s:u.jsx("polygon",{points:"0,0 30,0 15,10"})})});Vg.displayName=gS;var pS=Vg;function yS(a){const[o,s]=x.useState(void 0);return ht(()=>{if(a){s({width:a.offsetWidth,height:a.offsetHeight});const c=new ResizeObserver(f=>{if(!Array.isArray(f)||!f.length)return;const m=f[0];let h,v;if("borderBoxSize"in m){const y=m.borderBoxSize,p=Array.isArray(y)?y[0]:y;h=p.inlineSize,v=p.blockSize}else h=a.offsetWidth,v=a.offsetHeight;s({width:h,height:v})});return c.observe(a,{box:"border-box"}),()=>c.unobserve(a)}else s(void 0)},[a]),o}var Yu="Popper",[Yg,Gg]=wo(Yu),[xS,Xg]=Yg(Yu),Qg=a=>{const{__scopePopper:o,children:s}=a,[c,f]=x.useState(null);return u.jsx(xS,{scope:o,anchor:c,onAnchorChange:f,children:s})};Qg.displayName=Yu;var Zg="PopperAnchor",Kg=x.forwardRef((a,o)=>{const{__scopePopper:s,virtualRef:c,...f}=a,m=Xg(Zg,s),h=x.useRef(null),v=Qe(o,h);return x.useEffect(()=>{m.onAnchorChange((c==null?void 0:c.current)||h.current)}),c?null:u.jsx(Le.div,{...f,ref:v})});Kg.displayName=Zg;var Gu="PopperContent",[bS,SS]=Yg(Gu),Jg=x.forwardRef((a,o)=>{var I,fe,Oe,Te,we,Ee;const{__scopePopper:s,side:c="bottom",sideOffset:f=0,align:m="center",alignOffset:h=0,arrowPadding:v=0,avoidCollisions:y=!0,collisionBoundary:p=[],collisionPadding:S=0,sticky:N="partial",hideWhenDetached:C=!1,updatePositionStrategy:O="optimized",onPlaced:R,...b}=a,T=Xg(Gu,s),[L,D]=x.useState(null),B=Qe(o,it=>D(it)),[K,V]=x.useState(null),ee=yS(K),P=(ee==null?void 0:ee.width)??0,$=(ee==null?void 0:ee.height)??0,se=c+(m!=="center"?"-"+m:""),J=typeof S=="number"?S:{top:0,right:0,bottom:0,left:0,...S},ue=Array.isArray(p)?p:[p],oe=ue.length>0,ve={padding:J,boundary:ue.filter(ES),altBoundary:oe},{refs:pe,floatingStyles:Y,placement:_,isPositioned:Z,middlewareData:k}=oS({strategy:"fixed",placement:se,whileElementsMounted:(...it)=>I2(...it,{animationFrame:O==="always"}),elements:{reference:T.anchor},middleware:[sS({mainAxis:f+$,alignmentAxis:h}),y&&uS({mainAxis:!0,crossAxis:!1,limiter:N==="partial"?fS():void 0,...ve}),y&&dS({...ve}),mS({...ve,apply:({elements:it,rects:gt,availableWidth:al,availableHeight:il})=>{const{width:ut,height:Ro}=gt.reference,rl=it.floating.style;rl.setProperty("--radix-popper-available-width",`${al}px`),rl.setProperty("--radix-popper-available-height",`${il}px`),rl.setProperty("--radix-popper-anchor-width",`${ut}px`),rl.setProperty("--radix-popper-anchor-height",`${Ro}px`)}}),K&&vS({element:K,padding:v}),NS({arrowWidth:P,arrowHeight:$}),C&&hS({strategy:"referenceHidden",...ve})]}),[ie,E]=Pg(_),G=Tl(R);ht(()=>{Z&&(G==null||G())},[Z,G]);const F=(I=k.arrow)==null?void 0:I.x,W=(fe=k.arrow)==null?void 0:fe.y,te=((Oe=k.arrow)==null?void 0:Oe.centerOffset)!==0,[ge,ce]=x.useState();return ht(()=>{L&&ce(window.getComputedStyle(L).zIndex)},[L]),u.jsx("div",{ref:pe.setFloating,"data-radix-popper-content-wrapper":"",style:{...Y,transform:Z?Y.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ge,"--radix-popper-transform-origin":[(Te=k.transformOrigin)==null?void 0:Te.x,(we=k.transformOrigin)==null?void 0:we.y].join(" "),...((Ee=k.hide)==null?void 0:Ee.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:u.jsx(bS,{scope:s,placedSide:ie,onArrowChange:V,arrowX:F,arrowY:W,shouldHideArrow:te,children:u.jsx(Le.div,{"data-side":ie,"data-align":E,...b,ref:B,style:{...b.style,animation:Z?void 0:"none"}})})})});Jg.displayName=Gu;var $g="PopperArrow",wS={top:"bottom",right:"left",bottom:"top",left:"right"},Wg=x.forwardRef(function(o,s){const{__scopePopper:c,...f}=o,m=SS($g,c),h=wS[m.placedSide];return u.jsx("span",{ref:m.onArrowChange,style:{position:"absolute",left:m.arrowX,top:m.arrowY,[h]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[m.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[m.placedSide],visibility:m.shouldHideArrow?"hidden":void 0},children:u.jsx(pS,{...f,ref:s,style:{...f.style,display:"block"}})})});Wg.displayName=$g;function ES(a){return a!==null}var NS=a=>({name:"transformOrigin",options:a,fn(o){var T,L,D;const{placement:s,rects:c,middlewareData:f}=o,h=((T=f.arrow)==null?void 0:T.centerOffset)!==0,v=h?0:a.arrowWidth,y=h?0:a.arrowHeight,[p,S]=Pg(s),N={start:"0%",center:"50%",end:"100%"}[S],C=(((L=f.arrow)==null?void 0:L.x)??0)+v/2,O=(((D=f.arrow)==null?void 0:D.y)??0)+y/2;let R="",b="";return p==="bottom"?(R=h?N:`${C}px`,b=`${-y}px`):p==="top"?(R=h?N:`${C}px`,b=`${c.floating.height+y}px`):p==="right"?(R=`${-y}px`,b=h?N:`${O}px`):p==="left"&&(R=`${c.floating.width+y}px`,b=h?N:`${O}px`),{data:{x:R,y:b}}}});function Pg(a){const[o,s="center"]=a.split("-");return[o,s]}var AS=Qg,TS=Kg,CS=Jg,jS=Wg;function _S(a){const o=x.useRef({value:a,previous:a});return x.useMemo(()=>(o.current.value!==a&&(o.current.previous=o.current.value,o.current.value=a),o.current.previous),[a])}var Fg=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),OS="VisuallyHidden",MS=x.forwardRef((a,o)=>u.jsx(Le.span,{...a,ref:o,style:{...Fg,...a.style}}));MS.displayName=OS;var RS=[" ","Enter","ArrowUp","ArrowDown"],DS=[" ","Enter"],_l="Select",[_o,Oo,zS]=g2(_l),[Ma,Sw]=wo(_l,[zS,Gg]),Mo=Gg(),[US,nl]=Ma(_l),[HS,LS]=Ma(_l),Ig=a=>{const{__scopeSelect:o,children:s,open:c,defaultOpen:f,onOpenChange:m,value:h,defaultValue:v,onValueChange:y,dir:p,name:S,autoComplete:N,disabled:C,required:O,form:R}=a,b=Mo(o),[T,L]=x.useState(null),[D,B]=x.useState(null),[K,V]=x.useState(!1),ee=y2(p),[P,$]=vu({prop:c,defaultProp:f??!1,onChange:m,caller:_l}),[se,J]=vu({prop:h,defaultProp:v,onChange:y,caller:_l}),ue=x.useRef(null),oe=T?R||!!T.closest("form"):!0,[ve,pe]=x.useState(new Set),Y=Array.from(ve).map(_=>_.props.value).join(";");return u.jsx(AS,{...b,children:u.jsxs(US,{required:O,scope:o,trigger:T,onTriggerChange:L,valueNode:D,onValueNodeChange:B,valueNodeHasChildren:K,onValueNodeHasChildrenChange:V,contentId:wa(),value:se,onValueChange:J,open:P,onOpenChange:$,dir:ee,triggerPointerDownPosRef:ue,disabled:C,children:[u.jsx(_o.Provider,{scope:o,children:u.jsx(HS,{scope:a.__scopeSelect,onNativeOptionAdd:x.useCallback(_=>{pe(Z=>new Set(Z).add(_))},[]),onNativeOptionRemove:x.useCallback(_=>{pe(Z=>{const k=new Set(Z);return k.delete(_),k})},[]),children:s})}),oe?u.jsxs(Sp,{"aria-hidden":!0,required:O,tabIndex:-1,name:S,autoComplete:N,value:se,onChange:_=>J(_.target.value),disabled:C,form:R,children:[se===void 0?u.jsx("option",{value:""}):null,Array.from(ve)]},Y):null]})})};Ig.displayName=_l;var ep="SelectTrigger",tp=x.forwardRef((a,o)=>{const{__scopeSelect:s,disabled:c=!1,...f}=a,m=Mo(s),h=nl(ep,s),v=h.disabled||c,y=Qe(o,h.onTriggerChange),p=Oo(s),S=x.useRef("touch"),[N,C,O]=Ep(b=>{const T=p().filter(B=>!B.disabled),L=T.find(B=>B.value===h.value),D=Np(T,b,L);D!==void 0&&h.onValueChange(D.value)}),R=b=>{v||(h.onOpenChange(!0),O()),b&&(h.triggerPointerDownPosRef.current={x:Math.round(b.pageX),y:Math.round(b.pageY)})};return u.jsx(TS,{asChild:!0,...m,children:u.jsx(Le.button,{type:"button",role:"combobox","aria-controls":h.contentId,"aria-expanded":h.open,"aria-required":h.required,"aria-autocomplete":"none",dir:h.dir,"data-state":h.open?"open":"closed",disabled:v,"data-disabled":v?"":void 0,"data-placeholder":wp(h.value)?"":void 0,...f,ref:y,onClick:ke(f.onClick,b=>{b.currentTarget.focus(),S.current!=="mouse"&&R(b)}),onPointerDown:ke(f.onPointerDown,b=>{S.current=b.pointerType;const T=b.target;T.hasPointerCapture(b.pointerId)&&T.releasePointerCapture(b.pointerId),b.button===0&&b.ctrlKey===!1&&b.pointerType==="mouse"&&(R(b),b.preventDefault())}),onKeyDown:ke(f.onKeyDown,b=>{const T=N.current!=="";!(b.ctrlKey||b.altKey||b.metaKey)&&b.key.length===1&&C(b.key),!(T&&b.key===" ")&&RS.includes(b.key)&&(R(),b.preventDefault())})})})});tp.displayName=ep;var np="SelectValue",lp=x.forwardRef((a,o)=>{const{__scopeSelect:s,className:c,style:f,children:m,placeholder:h="",...v}=a,y=nl(np,s),{onValueNodeHasChildrenChange:p}=y,S=m!==void 0,N=Qe(o,y.onValueNodeChange);return ht(()=>{p(S)},[p,S]),u.jsx(Le.span,{...v,ref:N,style:{pointerEvents:"none"},children:wp(y.value)?u.jsx(u.Fragment,{children:h}):m})});lp.displayName=np;var BS="SelectIcon",ap=x.forwardRef((a,o)=>{const{__scopeSelect:s,children:c,...f}=a;return u.jsx(Le.span,{"aria-hidden":!0,...f,ref:o,children:c||"▼"})});ap.displayName=BS;var kS="SelectPortal",ip=a=>u.jsx(Ou,{asChild:!0,...a});ip.displayName=kS;var Ol="SelectContent",rp=x.forwardRef((a,o)=>{const s=nl(Ol,a.__scopeSelect),[c,f]=x.useState();if(ht(()=>{f(new DocumentFragment)},[]),!s.open){const m=c;return m?ki.createPortal(u.jsx(op,{scope:a.__scopeSelect,children:u.jsx(_o.Slot,{scope:a.__scopeSelect,children:u.jsx("div",{children:a.children})})}),m):null}return u.jsx(cp,{...a,ref:o})});rp.displayName=Ol;var Zt=10,[op,ll]=Ma(Ol),qS="SelectContentImpl",VS=Aa("SelectContent.RemoveScroll"),cp=x.forwardRef((a,o)=>{const{__scopeSelect:s,position:c="item-aligned",onCloseAutoFocus:f,onEscapeKeyDown:m,onPointerDownOutside:h,side:v,sideOffset:y,align:p,alignOffset:S,arrowPadding:N,collisionBoundary:C,collisionPadding:O,sticky:R,hideWhenDetached:b,avoidCollisions:T,...L}=a,D=nl(Ol,s),[B,K]=x.useState(null),[V,ee]=x.useState(null),P=Qe(o,I=>K(I)),[$,se]=x.useState(null),[J,ue]=x.useState(null),oe=Oo(s),[ve,pe]=x.useState(!1),Y=x.useRef(!1);x.useEffect(()=>{if(B)return rg(B)},[B]),Pv();const _=x.useCallback(I=>{const[fe,...Oe]=oe().map(Ee=>Ee.ref.current),[Te]=Oe.slice(-1),we=document.activeElement;for(const Ee of I)if(Ee===we||(Ee==null||Ee.scrollIntoView({block:"nearest"}),Ee===fe&&V&&(V.scrollTop=0),Ee===Te&&V&&(V.scrollTop=V.scrollHeight),Ee==null||Ee.focus(),document.activeElement!==we))return},[oe,V]),Z=x.useCallback(()=>_([$,B]),[_,$,B]);x.useEffect(()=>{ve&&Z()},[ve,Z]);const{onOpenChange:k,triggerPointerDownPosRef:ie}=D;x.useEffect(()=>{if(B){let I={x:0,y:0};const fe=Te=>{var we,Ee;I={x:Math.abs(Math.round(Te.pageX)-(((we=ie.current)==null?void 0:we.x)??0)),y:Math.abs(Math.round(Te.pageY)-(((Ee=ie.current)==null?void 0:Ee.y)??0))}},Oe=Te=>{I.x<=10&&I.y<=10?Te.preventDefault():B.contains(Te.target)||k(!1),document.removeEventListener("pointermove",fe),ie.current=null};return ie.current!==null&&(document.addEventListener("pointermove",fe),document.addEventListener("pointerup",Oe,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",fe),document.removeEventListener("pointerup",Oe,{capture:!0})}}},[B,k,ie]),x.useEffect(()=>{const I=()=>k(!1);return window.addEventListener("blur",I),window.addEventListener("resize",I),()=>{window.removeEventListener("blur",I),window.removeEventListener("resize",I)}},[k]);const[E,G]=Ep(I=>{const fe=oe().filter(we=>!we.disabled),Oe=fe.find(we=>we.ref.current===document.activeElement),Te=Np(fe,I,Oe);Te&&setTimeout(()=>Te.ref.current.focus())}),F=x.useCallback((I,fe,Oe)=>{const Te=!Y.current&&!Oe;(D.value!==void 0&&D.value===fe||Te)&&(se(I),Te&&(Y.current=!0))},[D.value]),W=x.useCallback(()=>B==null?void 0:B.focus(),[B]),te=x.useCallback((I,fe,Oe)=>{const Te=!Y.current&&!Oe;(D.value!==void 0&&D.value===fe||Te)&&ue(I)},[D.value]),ge=c==="popper"?Su:sp,ce=ge===Su?{side:v,sideOffset:y,align:p,alignOffset:S,arrowPadding:N,collisionBoundary:C,collisionPadding:O,sticky:R,hideWhenDetached:b,avoidCollisions:T}:{};return u.jsx(op,{scope:s,content:B,viewport:V,onViewportChange:ee,itemRefCallback:F,selectedItem:$,onItemLeave:W,itemTextRefCallback:te,focusSelectedItem:Z,selectedItemText:J,position:c,isPositioned:ve,searchRef:E,children:u.jsx(Mu,{as:VS,allowPinchZoom:!0,children:u.jsx(_u,{asChild:!0,trapped:D.open,onMountAutoFocus:I=>{I.preventDefault()},onUnmountAutoFocus:ke(f,I=>{var fe;(fe=D.trigger)==null||fe.focus({preventScroll:!0}),I.preventDefault()}),children:u.jsx(ju,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:m,onPointerDownOutside:h,onFocusOutside:I=>I.preventDefault(),onDismiss:()=>D.onOpenChange(!1),children:u.jsx(ge,{role:"listbox",id:D.contentId,"data-state":D.open?"open":"closed",dir:D.dir,onContextMenu:I=>I.preventDefault(),...L,...ce,onPlaced:()=>pe(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...L.style},onKeyDown:ke(L.onKeyDown,I=>{const fe=I.ctrlKey||I.altKey||I.metaKey;if(I.key==="Tab"&&I.preventDefault(),!fe&&I.key.length===1&&G(I.key),["ArrowUp","ArrowDown","Home","End"].includes(I.key)){let Te=oe().filter(we=>!we.disabled).map(we=>we.ref.current);if(["ArrowUp","End"].includes(I.key)&&(Te=Te.slice().reverse()),["ArrowUp","ArrowDown"].includes(I.key)){const we=I.target,Ee=Te.indexOf(we);Te=Te.slice(Ee+1)}setTimeout(()=>_(Te)),I.preventDefault()}})})})})})})});cp.displayName=qS;var YS="SelectItemAlignedPosition",sp=x.forwardRef((a,o)=>{const{__scopeSelect:s,onPlaced:c,...f}=a,m=nl(Ol,s),h=ll(Ol,s),[v,y]=x.useState(null),[p,S]=x.useState(null),N=Qe(o,P=>S(P)),C=Oo(s),O=x.useRef(!1),R=x.useRef(!0),{viewport:b,selectedItem:T,selectedItemText:L,focusSelectedItem:D}=h,B=x.useCallback(()=>{if(m.trigger&&m.valueNode&&v&&p&&b&&T&&L){const P=m.trigger.getBoundingClientRect(),$=p.getBoundingClientRect(),se=m.valueNode.getBoundingClientRect(),J=L.getBoundingClientRect();if(m.dir!=="rtl"){const we=J.left-$.left,Ee=se.left-we,it=P.left-Ee,gt=P.width+it,al=Math.max(gt,$.width),il=window.innerWidth-Zt,ut=vv(Ee,[Zt,Math.max(Zt,il-al)]);v.style.minWidth=gt+"px",v.style.left=ut+"px"}else{const we=$.right-J.right,Ee=window.innerWidth-se.right-we,it=window.innerWidth-P.right-Ee,gt=P.width+it,al=Math.max(gt,$.width),il=window.innerWidth-Zt,ut=vv(Ee,[Zt,Math.max(Zt,il-al)]);v.style.minWidth=gt+"px",v.style.right=ut+"px"}const ue=C(),oe=window.innerHeight-Zt*2,ve=b.scrollHeight,pe=window.getComputedStyle(p),Y=parseInt(pe.borderTopWidth,10),_=parseInt(pe.paddingTop,10),Z=parseInt(pe.borderBottomWidth,10),k=parseInt(pe.paddingBottom,10),ie=Y+_+ve+k+Z,E=Math.min(T.offsetHeight*5,ie),G=window.getComputedStyle(b),F=parseInt(G.paddingTop,10),W=parseInt(G.paddingBottom,10),te=P.top+P.height/2-Zt,ge=oe-te,ce=T.offsetHeight/2,I=T.offsetTop+ce,fe=Y+_+I,Oe=ie-fe;if(fe<=te){const we=ue.length>0&&T===ue[ue.length-1].ref.current;v.style.bottom="0px";const Ee=p.clientHeight-b.offsetTop-b.offsetHeight,it=Math.max(ge,ce+(we?W:0)+Ee+Z),gt=fe+it;v.style.height=gt+"px"}else{const we=ue.length>0&&T===ue[0].ref.current;v.style.top="0px";const it=Math.max(te,Y+b.offsetTop+(we?F:0)+ce)+Oe;v.style.height=it+"px",b.scrollTop=fe-te+b.offsetTop}v.style.margin=`${Zt}px 0`,v.style.minHeight=E+"px",v.style.maxHeight=oe+"px",c==null||c(),requestAnimationFrame(()=>O.current=!0)}},[C,m.trigger,m.valueNode,v,p,b,T,L,m.dir,c]);ht(()=>B(),[B]);const[K,V]=x.useState();ht(()=>{p&&V(window.getComputedStyle(p).zIndex)},[p]);const ee=x.useCallback(P=>{P&&R.current===!0&&(B(),D==null||D(),R.current=!1)},[B,D]);return u.jsx(XS,{scope:s,contentWrapper:v,shouldExpandOnScrollRef:O,onScrollButtonChange:ee,children:u.jsx("div",{ref:y,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:K},children:u.jsx(Le.div,{...f,ref:N,style:{boxSizing:"border-box",maxHeight:"100%",...f.style}})})})});sp.displayName=YS;var GS="SelectPopperPosition",Su=x.forwardRef((a,o)=>{const{__scopeSelect:s,align:c="start",collisionPadding:f=Zt,...m}=a,h=Mo(s);return u.jsx(CS,{...h,...m,ref:o,align:c,collisionPadding:f,style:{boxSizing:"border-box",...m.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Su.displayName=GS;var[XS,Xu]=Ma(Ol,{}),wu="SelectViewport",up=x.forwardRef((a,o)=>{const{__scopeSelect:s,nonce:c,...f}=a,m=ll(wu,s),h=Xu(wu,s),v=Qe(o,m.onViewportChange),y=x.useRef(0);return u.jsxs(u.Fragment,{children:[u.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:c}),u.jsx(_o.Slot,{scope:s,children:u.jsx(Le.div,{"data-radix-select-viewport":"",role:"presentation",...f,ref:v,style:{position:"relative",flex:1,overflow:"hidden auto",...f.style},onScroll:ke(f.onScroll,p=>{const S=p.currentTarget,{contentWrapper:N,shouldExpandOnScrollRef:C}=h;if(C!=null&&C.current&&N){const O=Math.abs(y.current-S.scrollTop);if(O>0){const R=window.innerHeight-Zt*2,b=parseFloat(N.style.minHeight),T=parseFloat(N.style.height),L=Math.max(b,T);if(L<R){const D=L+O,B=Math.min(R,D),K=D-B;N.style.height=B+"px",N.style.bottom==="0px"&&(S.scrollTop=K>0?K:0,N.style.justifyContent="flex-end")}}}y.current=S.scrollTop})})})]})});up.displayName=wu;var fp="SelectGroup",[QS,ZS]=Ma(fp),KS=x.forwardRef((a,o)=>{const{__scopeSelect:s,...c}=a,f=wa();return u.jsx(QS,{scope:s,id:f,children:u.jsx(Le.div,{role:"group","aria-labelledby":f,...c,ref:o})})});KS.displayName=fp;var dp="SelectLabel",JS=x.forwardRef((a,o)=>{const{__scopeSelect:s,...c}=a,f=ZS(dp,s);return u.jsx(Le.div,{id:f.id,...c,ref:o})});JS.displayName=dp;var So="SelectItem",[$S,mp]=Ma(So),hp=x.forwardRef((a,o)=>{const{__scopeSelect:s,value:c,disabled:f=!1,textValue:m,...h}=a,v=nl(So,s),y=ll(So,s),p=v.value===c,[S,N]=x.useState(m??""),[C,O]=x.useState(!1),R=Qe(o,D=>{var B;return(B=y.itemRefCallback)==null?void 0:B.call(y,D,c,f)}),b=wa(),T=x.useRef("touch"),L=()=>{f||(v.onValueChange(c),v.onOpenChange(!1))};if(c==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return u.jsx($S,{scope:s,value:c,disabled:f,textId:b,isSelected:p,onItemTextChange:x.useCallback(D=>{N(B=>B||((D==null?void 0:D.textContent)??"").trim())},[]),children:u.jsx(_o.ItemSlot,{scope:s,value:c,disabled:f,textValue:S,children:u.jsx(Le.div,{role:"option","aria-labelledby":b,"data-highlighted":C?"":void 0,"aria-selected":p&&C,"data-state":p?"checked":"unchecked","aria-disabled":f||void 0,"data-disabled":f?"":void 0,tabIndex:f?void 0:-1,...h,ref:R,onFocus:ke(h.onFocus,()=>O(!0)),onBlur:ke(h.onBlur,()=>O(!1)),onClick:ke(h.onClick,()=>{T.current!=="mouse"&&L()}),onPointerUp:ke(h.onPointerUp,()=>{T.current==="mouse"&&L()}),onPointerDown:ke(h.onPointerDown,D=>{T.current=D.pointerType}),onPointerMove:ke(h.onPointerMove,D=>{var B;T.current=D.pointerType,f?(B=y.onItemLeave)==null||B.call(y):T.current==="mouse"&&D.currentTarget.focus({preventScroll:!0})}),onPointerLeave:ke(h.onPointerLeave,D=>{var B;D.currentTarget===document.activeElement&&((B=y.onItemLeave)==null||B.call(y))}),onKeyDown:ke(h.onKeyDown,D=>{var K;((K=y.searchRef)==null?void 0:K.current)!==""&&D.key===" "||(DS.includes(D.key)&&L(),D.key===" "&&D.preventDefault())})})})})});hp.displayName=So;var Ri="SelectItemText",vp=x.forwardRef((a,o)=>{const{__scopeSelect:s,className:c,style:f,...m}=a,h=nl(Ri,s),v=ll(Ri,s),y=mp(Ri,s),p=LS(Ri,s),[S,N]=x.useState(null),C=Qe(o,L=>N(L),y.onItemTextChange,L=>{var D;return(D=v.itemTextRefCallback)==null?void 0:D.call(v,L,y.value,y.disabled)}),O=S==null?void 0:S.textContent,R=x.useMemo(()=>u.jsx("option",{value:y.value,disabled:y.disabled,children:O},y.value),[y.disabled,y.value,O]),{onNativeOptionAdd:b,onNativeOptionRemove:T}=p;return ht(()=>(b(R),()=>T(R)),[b,T,R]),u.jsxs(u.Fragment,{children:[u.jsx(Le.span,{id:y.textId,...m,ref:C}),y.isSelected&&h.valueNode&&!h.valueNodeHasChildren?ki.createPortal(m.children,h.valueNode):null]})});vp.displayName=Ri;var gp="SelectItemIndicator",pp=x.forwardRef((a,o)=>{const{__scopeSelect:s,...c}=a;return mp(gp,s).isSelected?u.jsx(Le.span,{"aria-hidden":!0,...c,ref:o}):null});pp.displayName=gp;var Eu="SelectScrollUpButton",yp=x.forwardRef((a,o)=>{const s=ll(Eu,a.__scopeSelect),c=Xu(Eu,a.__scopeSelect),[f,m]=x.useState(!1),h=Qe(o,c.onScrollButtonChange);return ht(()=>{if(s.viewport&&s.isPositioned){let v=function(){const p=y.scrollTop>0;m(p)};const y=s.viewport;return v(),y.addEventListener("scroll",v),()=>y.removeEventListener("scroll",v)}},[s.viewport,s.isPositioned]),f?u.jsx(bp,{...a,ref:h,onAutoScroll:()=>{const{viewport:v,selectedItem:y}=s;v&&y&&(v.scrollTop=v.scrollTop-y.offsetHeight)}}):null});yp.displayName=Eu;var Nu="SelectScrollDownButton",xp=x.forwardRef((a,o)=>{const s=ll(Nu,a.__scopeSelect),c=Xu(Nu,a.__scopeSelect),[f,m]=x.useState(!1),h=Qe(o,c.onScrollButtonChange);return ht(()=>{if(s.viewport&&s.isPositioned){let v=function(){const p=y.scrollHeight-y.clientHeight,S=Math.ceil(y.scrollTop)<p;m(S)};const y=s.viewport;return v(),y.addEventListener("scroll",v),()=>y.removeEventListener("scroll",v)}},[s.viewport,s.isPositioned]),f?u.jsx(bp,{...a,ref:h,onAutoScroll:()=>{const{viewport:v,selectedItem:y}=s;v&&y&&(v.scrollTop=v.scrollTop+y.offsetHeight)}}):null});xp.displayName=Nu;var bp=x.forwardRef((a,o)=>{const{__scopeSelect:s,onAutoScroll:c,...f}=a,m=ll("SelectScrollButton",s),h=x.useRef(null),v=Oo(s),y=x.useCallback(()=>{h.current!==null&&(window.clearInterval(h.current),h.current=null)},[]);return x.useEffect(()=>()=>y(),[y]),ht(()=>{var S;const p=v().find(N=>N.ref.current===document.activeElement);(S=p==null?void 0:p.ref.current)==null||S.scrollIntoView({block:"nearest"})},[v]),u.jsx(Le.div,{"aria-hidden":!0,...f,ref:o,style:{flexShrink:0,...f.style},onPointerDown:ke(f.onPointerDown,()=>{h.current===null&&(h.current=window.setInterval(c,50))}),onPointerMove:ke(f.onPointerMove,()=>{var p;(p=m.onItemLeave)==null||p.call(m),h.current===null&&(h.current=window.setInterval(c,50))}),onPointerLeave:ke(f.onPointerLeave,()=>{y()})})}),WS="SelectSeparator",PS=x.forwardRef((a,o)=>{const{__scopeSelect:s,...c}=a;return u.jsx(Le.div,{"aria-hidden":!0,...c,ref:o})});PS.displayName=WS;var Au="SelectArrow",FS=x.forwardRef((a,o)=>{const{__scopeSelect:s,...c}=a,f=Mo(s),m=nl(Au,s),h=ll(Au,s);return m.open&&h.position==="popper"?u.jsx(jS,{...f,...c,ref:o}):null});FS.displayName=Au;var IS="SelectBubbleInput",Sp=x.forwardRef(({__scopeSelect:a,value:o,...s},c)=>{const f=x.useRef(null),m=Qe(c,f),h=_S(o);return x.useEffect(()=>{const v=f.current;if(!v)return;const y=window.HTMLSelectElement.prototype,S=Object.getOwnPropertyDescriptor(y,"value").set;if(h!==o&&S){const N=new Event("change",{bubbles:!0});S.call(v,o),v.dispatchEvent(N)}},[h,o]),u.jsx(Le.select,{...s,style:{...Fg,...s.style},ref:m,defaultValue:o})});Sp.displayName=IS;function wp(a){return a===""||a===void 0}function Ep(a){const o=Tl(a),s=x.useRef(""),c=x.useRef(0),f=x.useCallback(h=>{const v=s.current+h;o(v),function y(p){s.current=p,window.clearTimeout(c.current),p!==""&&(c.current=window.setTimeout(()=>y(""),1e3))}(v)},[o]),m=x.useCallback(()=>{s.current="",window.clearTimeout(c.current)},[]);return x.useEffect(()=>()=>window.clearTimeout(c.current),[]),[s,f,m]}function Np(a,o,s){const f=o.length>1&&Array.from(o).every(p=>p===o[0])?o[0]:o,m=s?a.indexOf(s):-1;let h=ew(a,Math.max(m,0));f.length===1&&(h=h.filter(p=>p!==s));const y=h.find(p=>p.textValue.toLowerCase().startsWith(f.toLowerCase()));return y!==s?y:void 0}function ew(a,o){return a.map((s,c)=>a[(o+c)%a.length])}var tw=Ig,nw=tp,lw=lp,aw=ap,iw=ip,rw=rp,ow=up,cw=hp,sw=vp,uw=pp,fw=yp,dw=xp;function Di({...a}){return u.jsx(tw,{"data-slot":"select",...a})}function zi({...a}){return u.jsx(lw,{"data-slot":"select-value",...a})}function Ui({className:a,size:o="default",children:s,...c}){return u.jsxs(nw,{"data-slot":"select-trigger","data-size":o,className:Je("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...c,children:[s,u.jsx(aw,{asChild:!0,children:u.jsx(jv,{className:"size-4 opacity-50"})})]})}function Hi({className:a,children:o,position:s="popper",...c}){return u.jsx(iw,{children:u.jsxs(rw,{"data-slot":"select-content",className:Je("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",s==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:s,...c,children:[u.jsx(mw,{}),u.jsx(ow,{className:Je("p-1",s==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:o}),u.jsx(hw,{})]})})}function Pe({className:a,children:o,...s}){return u.jsxs(cw,{"data-slot":"select-item",className:Je("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...s,children:[u.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:u.jsx(uw,{children:u.jsx(ux,{className:"size-4"})})}),u.jsx(sw,{children:o})]})}function mw({className:a,...o}){return u.jsx(fw,{"data-slot":"select-scroll-up-button",className:Je("flex cursor-default items-center justify-center py-1",a),...o,children:u.jsx(mx,{className:"size-4"})})}function hw({className:a,...o}){return u.jsx(dw,{"data-slot":"select-scroll-down-button",className:Je("flex cursor-default items-center justify-center py-1",a),...o,children:u.jsx(jv,{className:"size-4"})})}const vw=()=>{const[a,o]=x.useState([]),[s,c]=x.useState(!0),[f,m]=x.useState(""),[h,v]=x.useState("all"),[y,p]=x.useState(null),[S,N]=x.useState(!1),[C,O]=x.useState(!1),[R,b]=x.useState({phone_number:"",name:"",email:"",notes:"",status:"new",deal_value:"",tags:[]});x.useEffect(()=>{T()},[h]);const T=async()=>{try{c(!0);const J=new URLSearchParams;h!=="all"&&J.append("status",h),f&&J.append("search",f);const ue=await fetch(`/api/contacts?${J}`);if(ue.ok){const oe=await ue.json();o(oe.contacts||[])}}catch(J){console.error("Erro ao buscar contatos:",J)}finally{c(!1)}},L=()=>{T()},D=async()=>{try{(await fetch("/api/contacts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...R,tags:R.tags,deal_value:R.deal_value?parseFloat(R.deal_value):null})})).ok&&(N(!1),P(),T())}catch(J){console.error("Erro ao criar contato:",J)}},B=async()=>{try{(await fetch(`/api/contacts/${y.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...R,tags:R.tags,deal_value:R.deal_value?parseFloat(R.deal_value):null})})).ok&&(N(!1),O(!1),P(),T())}catch(J){console.error("Erro ao atualizar contato:",J)}},K=async J=>{if(confirm("Tem certeza que deseja deletar este contato?"))try{(await fetch(`/api/contacts/${J}`,{method:"DELETE"})).ok&&T()}catch(ue){console.error("Erro ao deletar contato:",ue)}},V=async()=>{try{const J=await fetch("/api/contacts/export");if(J.ok){const ue=await J.blob(),oe=window.URL.createObjectURL(ue),ve=document.createElement("a");ve.style.display="none",ve.href=oe,ve.download="contacts.csv",document.body.appendChild(ve),ve.click(),window.URL.revokeObjectURL(oe)}}catch(J){console.error("Erro ao exportar contatos:",J)}},ee=J=>{p(J),b({phone_number:J.phone_number,name:J.name||"",email:J.email||"",notes:J.notes||"",status:J.status,deal_value:J.deal_value||"",tags:JSON.parse(J.tags||"[]")}),O(!0),N(!0)},P=()=>{b({phone_number:"",name:"",email:"",notes:"",status:"new",deal_value:"",tags:[]}),p(null),O(!1)},$=J=>({new:"bg-blue-100 text-blue-800",contacted:"bg-yellow-100 text-yellow-800",qualified:"bg-green-100 text-green-800",converted:"bg-purple-100 text-purple-800"})[J]||"bg-gray-100 text-gray-800",se=J=>({new:"Novo",contacted:"Contatado",qualified:"Qualificado",converted:"Convertido"})[J]||J;return u.jsxs("div",{className:"space-y-6",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Contatos"}),u.jsx("p",{className:"text-gray-600",children:"Gerencie seus contatos do WhatsApp"})]}),u.jsxs("div",{className:"flex space-x-2",children:[u.jsxs(st,{variant:"outline",onClick:V,children:[u.jsx(xx,{className:"h-4 w-4 mr-2"}),"Exportar"]}),u.jsxs(Eg,{open:S,onOpenChange:N,children:[u.jsx(Ng,{asChild:!0,children:u.jsxs(st,{onClick:P,children:[u.jsx(vo,{className:"h-4 w-4 mr-2"}),"Novo Contato"]})}),u.jsxs(Ag,{className:"max-w-md",children:[u.jsxs(Tg,{children:[u.jsx(Cg,{children:C?"Editar Contato":"Novo Contato"}),u.jsx(jg,{children:C?"Atualize as informações do contato":"Adicione um novo contato"})]}),u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"phone",children:"Telefone *"}),u.jsx(En,{id:"phone",value:R.phone_number,onChange:J=>b({...R,phone_number:J.target.value}),placeholder:"+55 11 99999-9999",disabled:C})]}),u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"name",children:"Nome"}),u.jsx(En,{id:"name",value:R.name,onChange:J=>b({...R,name:J.target.value}),placeholder:"Nome do contato"})]}),u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"email",children:"Email"}),u.jsx(En,{id:"email",type:"email",value:R.email,onChange:J=>b({...R,email:J.target.value}),placeholder:"<EMAIL>"})]}),u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"status",children:"Status"}),u.jsxs(Di,{value:R.status,onValueChange:J=>b({...R,status:J}),children:[u.jsx(Ui,{children:u.jsx(zi,{})}),u.jsxs(Hi,{children:[u.jsx(Pe,{value:"new",children:"Novo"}),u.jsx(Pe,{value:"contacted",children:"Contatado"}),u.jsx(Pe,{value:"qualified",children:"Qualificado"}),u.jsx(Pe,{value:"converted",children:"Convertido"})]})]})]}),u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"deal_value",children:"Valor da Negociação"}),u.jsx(En,{id:"deal_value",type:"number",value:R.deal_value,onChange:J=>b({...R,deal_value:J.target.value}),placeholder:"0.00"})]}),u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"notes",children:"Observações"}),u.jsx(Og,{id:"notes",value:R.notes,onChange:J=>b({...R,notes:J.target.value}),placeholder:"Observações sobre o contato",rows:3})]}),u.jsxs("div",{className:"flex space-x-2",children:[u.jsx(st,{onClick:C?B:D,className:"flex-1",children:C?"Atualizar":"Criar"}),u.jsx(st,{variant:"outline",onClick:()=>N(!1),className:"flex-1",children:"Cancelar"})]})]})]})]})]})]}),u.jsx(ba,{children:u.jsx(Sa,{className:"pt-6",children:u.jsxs("div",{className:"flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4",children:[u.jsx("div",{className:"flex-1",children:u.jsxs("div",{className:"relative",children:[u.jsx(ou,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),u.jsx(En,{placeholder:"Buscar por nome, telefone ou email...",value:f,onChange:J=>m(J.target.value),className:"pl-10",onKeyPress:J=>J.key==="Enter"&&L()})]})}),u.jsxs(Di,{value:h,onValueChange:v,children:[u.jsx(Ui,{className:"w-full md:w-48",children:u.jsx(zi,{placeholder:"Filtrar por status"})}),u.jsxs(Hi,{children:[u.jsx(Pe,{value:"all",children:"Todos os status"}),u.jsx(Pe,{value:"new",children:"Novo"}),u.jsx(Pe,{value:"contacted",children:"Contatado"}),u.jsx(Pe,{value:"qualified",children:"Qualificado"}),u.jsx(Pe,{value:"converted",children:"Convertido"})]})]}),u.jsxs(st,{onClick:L,children:[u.jsx(ou,{className:"h-4 w-4 mr-2"}),"Buscar"]})]})})}),u.jsxs(ba,{children:[u.jsx(mu,{children:u.jsxs(Kv,{className:"flex items-center space-x-2",children:[u.jsx(uo,{className:"h-5 w-5"}),u.jsxs("span",{children:["Lista de Contatos (",a.length,")"]})]})}),u.jsx(Sa,{children:s?u.jsx("div",{className:"space-y-4",children:[1,2,3].map(J=>u.jsx("div",{className:"animate-pulse",children:u.jsx("div",{className:"h-20 bg-gray-200 rounded"})},J))}):a.length>0?u.jsx("div",{className:"space-y-4",children:a.map(J=>u.jsx("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx("div",{className:"flex-1",children:u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:u.jsx(uo,{className:"h-6 w-6 text-green-600"})}),u.jsxs("div",{className:"flex-1",children:[u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx("h3",{className:"font-medium text-gray-900",children:J.name||"Sem nome"}),u.jsx(hu,{className:$(J.status),children:se(J.status)})]}),u.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mt-1",children:[u.jsxs("div",{className:"flex items-center space-x-1",children:[u.jsx(_v,{className:"h-4 w-4"}),u.jsx("span",{children:J.phone_number})]}),J.email&&u.jsxs("div",{className:"flex items-center space-x-1",children:[u.jsx(Nx,{className:"h-4 w-4"}),u.jsx("span",{children:J.email})]}),J.deal_value&&u.jsxs("div",{className:"flex items-center space-x-1",children:[u.jsx(px,{className:"h-4 w-4"}),u.jsxs("span",{children:["R$ ",J.deal_value.toLocaleString("pt-BR")]})]})]}),J.tags&&JSON.parse(J.tags).length>0&&u.jsxs("div",{className:"flex items-center space-x-2 mt-2",children:[u.jsx(Lx,{className:"h-4 w-4 text-gray-400"}),u.jsx("div",{className:"flex space-x-1",children:JSON.parse(J.tags).map((ue,oe)=>u.jsx(hu,{variant:"outline",className:"text-xs",children:ue},oe))})]})]})]})}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx(st,{variant:"outline",size:"sm",onClick:()=>ee(J),children:u.jsx(Ov,{className:"h-4 w-4"})}),u.jsx(st,{variant:"outline",size:"sm",onClick:()=>K(J.id),children:u.jsx(Mv,{className:"h-4 w-4"})})]})]})},J.id))}):u.jsxs("div",{className:"text-center py-12",children:[u.jsx(uo,{className:"h-12 w-12 mx-auto text-gray-300 mb-4"}),u.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Nenhum contato encontrado"}),u.jsx("p",{className:"text-gray-600 mb-4",children:f||h!=="all"?"Tente ajustar os filtros de busca":"Comece adicionando seu primeiro contato"}),!f&&h==="all"&&u.jsxs(st,{onClick:()=>N(!0),children:[u.jsx(vo,{className:"h-4 w-4 mr-2"}),"Adicionar Contato"]})]})})]})]})},gw=()=>{const[a,o]=x.useState([]),[s,c]=x.useState([]),[f,m]=x.useState(!0),[h,v]=x.useState(""),[y,p]=x.useState("all"),[S,N]=x.useState("all"),[C,O]=x.useState(null),[R,b]=x.useState(!1),[T,L]=x.useState(!1),[D,B]=x.useState({name:"",content:"",template_type:"text",category:"",media_url:"",variables:[]});x.useEffect(()=>{K(),V()},[y,S]);const K=async()=>{try{m(!0);const Y=new URLSearchParams;y!=="all"&&Y.append("category",y),S!=="all"&&Y.append("type",S);const _=await fetch(`/api/templates?${Y}`);if(_.ok){const Z=await _.json();o(Z||[])}}catch(Y){console.error("Erro ao buscar templates:",Y)}finally{m(!1)}},V=async()=>{try{const Y=await fetch("/api/templates/categories");if(Y.ok){const _=await Y.json();c(_||[])}}catch(Y){console.error("Erro ao buscar categorias:",Y)}},ee=async()=>{try{(await fetch("/api/templates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...D,variables:D.variables})})).ok&&(b(!1),ue(),K(),V())}catch(Y){console.error("Erro ao criar template:",Y)}},P=async()=>{try{(await fetch(`/api/templates/${C.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...D,variables:D.variables})})).ok&&(b(!1),L(!1),ue(),K(),V())}catch(Y){console.error("Erro ao atualizar template:",Y)}},$=async Y=>{if(confirm("Tem certeza que deseja deletar este template?"))try{(await fetch(`/api/templates/${Y}`,{method:"DELETE"})).ok&&K()}catch(_){console.error("Erro ao deletar template:",_)}},se=Y=>{navigator.clipboard.writeText(Y.content)},J=Y=>{O(Y),B({name:Y.name,content:Y.content,template_type:Y.template_type,category:Y.category||"",media_url:Y.media_url||"",variables:JSON.parse(Y.variables||"[]")}),L(!0),b(!0)},ue=()=>{B({name:"",content:"",template_type:"text",category:"",media_url:"",variables:[]}),O(null),L(!1)},oe=Y=>({text:so,image:wx,audio:_x,video:Vx})[Y]||so,ve=Y=>({text:"bg-blue-100 text-blue-800",image:"bg-green-100 text-green-800",audio:"bg-purple-100 text-purple-800",video:"bg-red-100 text-red-800"})[Y]||"bg-gray-100 text-gray-800",pe=a.filter(Y=>Y.name.toLowerCase().includes(h.toLowerCase())||Y.content.toLowerCase().includes(h.toLowerCase()));return u.jsxs("div",{className:"space-y-6",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Templates"}),u.jsx("p",{className:"text-gray-600",children:"Gerencie seus templates de mensagens"})]}),u.jsxs(Eg,{open:R,onOpenChange:b,children:[u.jsx(Ng,{asChild:!0,children:u.jsxs(st,{onClick:ue,children:[u.jsx(vo,{className:"h-4 w-4 mr-2"}),"Novo Template"]})}),u.jsxs(Ag,{className:"max-w-2xl",children:[u.jsxs(Tg,{children:[u.jsx(Cg,{children:T?"Editar Template":"Novo Template"}),u.jsx(jg,{children:T?"Atualize as informações do template":"Crie um novo template de mensagem"})]}),u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"name",children:"Nome do Template *"}),u.jsx(En,{id:"name",value:D.name,onChange:Y=>B({...D,name:Y.target.value}),placeholder:"Nome do template"})]}),u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"type",children:"Tipo"}),u.jsxs(Di,{value:D.template_type,onValueChange:Y=>B({...D,template_type:Y}),children:[u.jsx(Ui,{children:u.jsx(zi,{})}),u.jsxs(Hi,{children:[u.jsx(Pe,{value:"text",children:"Texto"}),u.jsx(Pe,{value:"image",children:"Imagem"}),u.jsx(Pe,{value:"audio",children:"Áudio"}),u.jsx(Pe,{value:"video",children:"Vídeo"})]})]})]})]}),u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"category",children:"Categoria"}),u.jsx(En,{id:"category",value:D.category,onChange:Y=>B({...D,category:Y.target.value}),placeholder:"Ex: saudacao, follow_up, fechamento"})]}),D.template_type!=="text"&&u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"media_url",children:"URL da Mídia"}),u.jsx(En,{id:"media_url",value:D.media_url,onChange:Y=>B({...D,media_url:Y.target.value}),placeholder:"https://exemplo.com/arquivo.jpg"})]}),u.jsxs("div",{children:[u.jsx(Kt,{htmlFor:"content",children:"Conteúdo *"}),u.jsx(Og,{id:"content",value:D.content,onChange:Y=>B({...D,content:Y.target.value}),placeholder:"Olá {name}, como posso ajudá-lo hoje?",rows:6}),u.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["Use ","{name}",", ","{phone}"," para variáveis dinâmicas"]})]}),u.jsxs("div",{className:"flex space-x-2",children:[u.jsx(st,{onClick:T?P:ee,className:"flex-1",children:T?"Atualizar":"Criar"}),u.jsx(st,{variant:"outline",onClick:()=>b(!1),className:"flex-1",children:"Cancelar"})]})]})]})]})]}),u.jsx(ba,{children:u.jsx(Sa,{className:"pt-6",children:u.jsxs("div",{className:"flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4",children:[u.jsx("div",{className:"flex-1",children:u.jsxs("div",{className:"relative",children:[u.jsx(ou,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),u.jsx(En,{placeholder:"Buscar templates...",value:h,onChange:Y=>v(Y.target.value),className:"pl-10"})]})}),u.jsxs(Di,{value:y,onValueChange:p,children:[u.jsx(Ui,{className:"w-full md:w-48",children:u.jsx(zi,{placeholder:"Filtrar por categoria"})}),u.jsxs(Hi,{children:[u.jsx(Pe,{value:"all",children:"Todas as categorias"}),s.map(Y=>u.jsx(Pe,{value:Y,children:Y},Y))]})]}),u.jsxs(Di,{value:S,onValueChange:N,children:[u.jsx(Ui,{className:"w-full md:w-48",children:u.jsx(zi,{placeholder:"Filtrar por tipo"})}),u.jsxs(Hi,{children:[u.jsx(Pe,{value:"all",children:"Todos os tipos"}),u.jsx(Pe,{value:"text",children:"Texto"}),u.jsx(Pe,{value:"image",children:"Imagem"}),u.jsx(Pe,{value:"audio",children:"Áudio"}),u.jsx(Pe,{value:"video",children:"Vídeo"})]})]})]})})}),u.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f?Array.from({length:6}).map((Y,_)=>u.jsxs(ba,{className:"animate-pulse",children:[u.jsxs(mu,{children:[u.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),u.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]}),u.jsx(Sa,{children:u.jsxs("div",{className:"space-y-2",children:[u.jsx("div",{className:"h-3 bg-gray-200 rounded"}),u.jsx("div",{className:"h-3 bg-gray-200 rounded"}),u.jsx("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]})})]},_)):pe.length>0?pe.map(Y=>{const _=oe(Y.template_type);return u.jsxs(ba,{className:"hover:shadow-lg transition-shadow duration-200",children:[u.jsxs(mu,{className:"pb-3",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx(Kv,{className:"text-lg",children:Y.name}),u.jsxs(hu,{className:ve(Y.template_type),children:[u.jsx(_,{className:"h-3 w-3 mr-1"}),Y.template_type]})]}),Y.category&&u.jsxs(Db,{children:["Categoria: ",Y.category]})]}),u.jsx(Sa,{children:u.jsxs("div",{className:"space-y-4",children:[u.jsx("div",{className:"bg-gray-50 p-3 rounded-lg",children:u.jsx("p",{className:"text-sm text-gray-700 line-clamp-3",children:Y.content})}),Y.media_url&&u.jsxs("div",{className:"text-xs text-gray-500",children:[u.jsx("span",{className:"font-medium",children:"Mídia:"})," ",Y.media_url]}),u.jsxs("div",{className:"flex items-center justify-between pt-2",children:[u.jsxs("div",{className:"text-xs text-gray-500",children:["Criado em ",new Date(Y.created_at).toLocaleDateString("pt-BR")]}),u.jsxs("div",{className:"flex space-x-1",children:[u.jsx(st,{variant:"outline",size:"sm",onClick:()=>se(Y),title:"Copiar conteúdo",children:u.jsx(vx,{className:"h-4 w-4"})}),u.jsx(st,{variant:"outline",size:"sm",onClick:()=>J(Y),title:"Editar template",children:u.jsx(Ov,{className:"h-4 w-4"})}),u.jsx(st,{variant:"outline",size:"sm",onClick:()=>$(Y.id),title:"Deletar template",children:u.jsx(Mv,{className:"h-4 w-4"})})]})]})]})})]},Y.id)}):u.jsx("div",{className:"col-span-full",children:u.jsx(ba,{children:u.jsxs(Sa,{className:"text-center py-12",children:[u.jsx(so,{className:"h-12 w-12 mx-auto text-gray-300 mb-4"}),u.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Nenhum template encontrado"}),u.jsx("p",{className:"text-gray-600 mb-4",children:h||y!=="all"||S!=="all"?"Tente ajustar os filtros de busca":"Comece criando seu primeiro template de mensagem"}),!h&&y==="all"&&S==="all"&&u.jsxs(st,{onClick:()=>b(!0),children:[u.jsx(vo,{className:"h-4 w-4 mr-2"}),"Criar Template"]})]})})})})]})},pw=()=>{const[a,o]=x.useState([]),[s,c]=x.useState(!0),[f,m]=x.useState(null),h=[{id:"inbox",title:"Inbox",color:"bg-gray-100",textColor:"text-gray-700",count:0},{id:"teste",title:"Teste",color:"bg-yellow-100",textColor:"text-yellow-700",count:0},{id:"primeiro_contato",title:"Primeiro Contato",color:"bg-blue-100",textColor:"text-blue-700",count:0},{id:"informacao",title:"Só Informação",color:"bg-purple-100",textColor:"text-purple-700",count:0},{id:"negociacao",title:"Negociação",color:"bg-orange-100",textColor:"text-orange-700",count:0},{id:"pagamento",title:"Pagamento Pendente",color:"bg-red-100",textColor:"text-red-700",count:0},{id:"cliente",title:"Cliente",color:"bg-green-100",textColor:"text-green-700",count:0}];x.useEffect(()=>{v()},[]);const v=async()=>{try{c(!0);const b=await du(fu.CONTACTS);o(b.contacts||[])}catch(b){console.error("Erro ao buscar contatos:",b),o([{id:1,name:"Rafaela Cliente",phone:"+5511999999999",status:"inbox",lastMessage:"Olá, tenho interesse no produto",avatar:null,priority:"high"},{id:2,name:"Thayna",phone:"+5511888888888",status:"teste",lastMessage:"Quando posso testar?",avatar:null,priority:"medium"},{id:3,name:"Cliente Exemplo",phone:"+5511777777777",status:"primeiro_contato",lastMessage:"Primeira mensagem",avatar:null,priority:"low"},{id:4,name:"Jamile Televendas",phone:"+5511666666666",status:"informacao",lastMessage:"Preciso de mais informações",avatar:null,priority:"medium"},{id:5,name:"Cliente Exemplo 2",phone:"+5511555555555",status:"cliente",lastMessage:"Obrigado pelo atendimento!",avatar:null,priority:"high"}])}finally{c(!1)}},y=b=>a.filter(T=>T.status===b),p=b=>y(b).length,S=(b,T)=>{m(T),b.dataTransfer.effectAllowed="move"},N=b=>{b.preventDefault(),b.dataTransfer.dropEffect="move"},C=async(b,T)=>{if(b.preventDefault(),!f||f.status===T){m(null);return}try{await du(`${fu.CONTACTS}/${f.id}/status`,{method:"PUT",body:JSON.stringify({status:T})}),o(L=>L.map(D=>D.id===f.id?{...D,status:T}:D))}catch(L){console.error("Erro ao atualizar status:",L),o(D=>D.map(B=>B.id===f.id?{...B,status:T}:B))}m(null)},O=({contact:b})=>{const T=L=>{switch(L){case"high":return"border-l-red-500";case"medium":return"border-l-yellow-500";case"low":return"border-l-green-500";default:return"border-l-gray-300"}};return u.jsxs("div",{draggable:!0,onDragStart:L=>S(L,b),className:`bg-white p-3 rounded-lg shadow-sm border-l-4 ${T(b.priority)} cursor-move hover:shadow-md transition-shadow mb-3`,children:[u.jsxs("div",{className:"flex items-center space-x-3",children:[u.jsx("div",{className:"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center",children:b.avatar?u.jsx("img",{src:b.avatar,alt:b.name,className:"w-10 h-10 rounded-full"}):u.jsx("span",{className:"text-gray-600 font-medium",children:b.name.charAt(0).toUpperCase()})}),u.jsxs("div",{className:"flex-1 min-w-0",children:[u.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:b.name}),u.jsx("p",{className:"text-xs text-gray-500 truncate",children:b.lastMessage})]})]}),u.jsxs("div",{className:"flex items-center justify-between mt-3",children:[u.jsxs("div",{className:"flex space-x-2",children:[u.jsx("button",{className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",children:u.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),u.jsx("button",{className:"p-1 text-gray-400 hover:text-green-600 transition-colors",children:u.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),u.jsx("button",{className:"p-1 text-gray-400 hover:text-purple-600 transition-colors",children:u.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})})})]}),u.jsxs("div",{className:"flex items-center space-x-1",children:[u.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),u.jsx("span",{className:"text-xs text-gray-500",children:"Online"})]})]})]})},R=({column:b})=>{const T=y(b.id),L=p(b.id);return u.jsxs("div",{className:"flex-shrink-0 w-80",onDragOver:N,onDrop:D=>C(D,b.id),children:[u.jsx("div",{className:`${b.color} rounded-lg p-4 mb-4`,children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx("h3",{className:`font-semibold ${b.textColor}`,children:b.title}),u.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${b.textColor} bg-white bg-opacity-50`,children:L})]})}),u.jsxs("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[T.map(D=>u.jsx(O,{contact:D},D.id)),T.length===0&&u.jsxs("div",{className:"text-center py-8 text-gray-400",children:[u.jsx("svg",{className:"w-12 h-12 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"})}),u.jsx("p",{className:"text-sm",children:"Nenhum contato"})]})]})]})};return s?u.jsx("div",{className:"p-6",children:u.jsxs("div",{className:"animate-pulse",children:[u.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),u.jsx("div",{className:"flex space-x-4 overflow-x-auto",children:h.map((b,T)=>u.jsxs("div",{className:"flex-shrink-0 w-80",children:[u.jsx("div",{className:"bg-gray-200 rounded-lg p-4 mb-4 h-16"}),u.jsx("div",{className:"space-y-3",children:[1,2,3].map(L=>u.jsx("div",{className:"bg-gray-200 rounded-lg h-24"},L))})]},T))})]})}):u.jsxs("div",{className:"p-6",children:[u.jsxs("div",{className:"flex justify-between items-center mb-6",children:[u.jsxs("div",{children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Conversas"}),u.jsx("p",{className:"text-gray-600",children:"Gerencie suas conversas do WhatsApp"})]}),u.jsx("button",{onClick:v,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors",children:"Atualizar"})]}),u.jsx("div",{className:"flex space-x-6 overflow-x-auto pb-4",children:h.map(b=>u.jsx(R,{column:b},b.id))})]})};function yw(){const[a,o]=x.useState("dashboard"),s=()=>{switch(a){case"dashboard":return u.jsx(nv,{});case"conversas":return u.jsx(pw,{});case"contatos":return u.jsx(vw,{});case"templates":return u.jsx(gw,{});case"automacao":return u.jsxs("div",{className:"p-6",children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Automação"}),u.jsx("p",{className:"text-gray-600",children:"Configure suas regras de automação aqui."})]});case"whatsapp":return u.jsxs("div",{className:"p-6",children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"WhatsApp"}),u.jsx("p",{className:"text-gray-600",children:"Configurações de integração com WhatsApp Business API."})]});case"analise":return u.jsxs("div",{className:"p-6",children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Análise IA"}),u.jsx("p",{className:"text-gray-600",children:"Análises inteligentes das suas conversas."})]});case"configuracoes":return u.jsxs("div",{className:"p-6",children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Configurações"}),u.jsx("p",{className:"text-gray-600",children:"Configurações gerais do sistema."})]});default:return u.jsx(nv,{})}};return u.jsx(Rb,{currentPage:a,setCurrentPage:o,children:s()})}Iy.createRoot(document.getElementById("root")).render(u.jsx(x.StrictMode,{children:u.jsx(yw,{})}));
