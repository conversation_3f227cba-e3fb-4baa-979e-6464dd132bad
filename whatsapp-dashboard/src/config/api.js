// Configuração da API para o frontend
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

// Configurações de endpoints
export const API_ENDPOINTS = {
  // Contatos
  CONTACTS: `${API_BASE_URL}/api/contacts`,
  CONTACTS_STATS: `${API_BASE_URL}/api/contacts/stats`,
  
  // Templates
  TEMPLATES: `${API_BASE_URL}/api/templates`,
  TEMPLATES_CATEGORIES: `${API_BASE_URL}/api/templates/categories`,
  
  // Automação
  AUTOMATION_RULES: `${API_BASE_URL}/api/automation/rules`,
  AUTOMATION_STATS: `${API_BASE_URL}/api/automation/stats`,
  
  // WhatsApp
  WHATSAPP_SEND: `${API_BASE_URL}/api/whatsapp/send`,
  WHATSAPP_WEBHOOK: `${API_BASE_URL}/api/whatsapp/webhook`,
  WHATSAPP_ACCOUNTS: `${API_BASE_URL}/api/whatsapp/accounts`,
  
  // Análise IA
  AI_ANALYZE: `${API_BASE_URL}/api/ai/analyze`,
  AI_SUGGESTIONS: `${API_BASE_URL}/api/ai/suggestions`,
  
  // Autenticação
  AUTH_LOGIN: `${API_BASE_URL}/api/auth/login`,
  AUTH_VERIFY: `${API_BASE_URL}/api/auth/verify-token`,
  AUTH_REFRESH: `${API_BASE_URL}/api/auth/refresh`
};

// Função helper para fazer requisições
export const apiRequest = async (endpoint, options = {}) => {
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  };

  try {
    const response = await fetch(endpoint, defaultOptions);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

export { API_BASE_URL };

