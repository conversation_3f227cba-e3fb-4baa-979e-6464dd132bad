import { useState, useEffect } from 'react';
import { 
  MessageSquare, 
  Users, 
  TrendingUp, 
  Activity,
  ArrowUpRight,
  Phone,
  Mail,
  Calendar,
  BarChart3
} from 'lucide-react';

const Home = () => {
  const [stats, setStats] = useState({
    totalMessages: 2847,
    activeContacts: 156,
    responseRate: 94.2,
    avgResponseTime: '2.3min'
  });

  const recentActivity = [
    {
      id: 1,
      type: 'message',
      contact: '<PERSON>',
      content: 'Interessado no produto X',
      time: '2 min atrás',
      status: 'new'
    },
    {
      id: 2,
      type: 'lead',
      contact: '<PERSON>',
      content: 'Lead qualificado automaticamente',
      time: '5 min atrás',
      status: 'qualified'
    },
    {
      id: 3,
      type: 'automation',
      contact: '<PERSON>',
      content: 'Template de boas-vindas enviado',
      time: '8 min atrás',
      status: 'automated'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Bem-vindo ao WhatsApp AI</h1>
            <p className="text-green-100 text-lg">
              Seu assistente inteligente para automação de conversas
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/20 rounded-2xl flex items-center justify-center">
              <MessageSquare className="w-12 h-12 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200/60 hover:shadow-md transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Mensagens Hoje</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalMessages.toLocaleString()}</p>
              <p className="text-xs text-green-600 font-medium flex items-center mt-2">
                <ArrowUpRight className="w-3 h-3 mr-1" />
                +12% vs ontem
              </p>
            </div>
            <div className="p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl">
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200/60 hover:shadow-md transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Contatos Ativos</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeContacts}</p>
              <p className="text-xs text-green-600 font-medium flex items-center mt-2">
                <ArrowUpRight className="w-3 h-3 mr-1" />
                +8% esta semana
              </p>
            </div>
            <div className="p-3 bg-gradient-to-br from-green-50 to-green-100 rounded-xl">
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200/60 hover:shadow-md transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Taxa de Resposta</p>
              <p className="text-2xl font-bold text-gray-900">{stats.responseRate}%</p>
              <p className="text-xs text-green-600 font-medium flex items-center mt-2">
                <ArrowUpRight className="w-3 h-3 mr-1" />
                +2.1% este mês
              </p>
            </div>
            <div className="p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200/60 hover:shadow-md transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Tempo Médio</p>
              <p className="text-2xl font-bold text-gray-900">{stats.avgResponseTime}</p>
              <p className="text-xs text-green-600 font-medium flex items-center mt-2">
                <ArrowUpRight className="w-3 h-3 mr-1" />
                -15% mais rápido
              </p>
            </div>
            <div className="p-3 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl">
              <Activity className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60">
          <div className="p-6 border-b border-gray-200/60">
            <div className="flex items-center">
              <div className="p-2 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg mr-3">
                <Activity className="w-5 h-5 text-gray-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Atividade Recente</h3>
                <p className="text-sm text-gray-600">Últimas interações do sistema</p>
              </div>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    activity.status === 'new' ? 'bg-blue-500' :
                    activity.status === 'qualified' ? 'bg-green-500' :
                    'bg-purple-500'
                  }`}></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.contact}</p>
                    <p className="text-sm text-gray-600">{activity.content}</p>
                    <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200/60">
          <div className="p-6 border-b border-gray-200/60">
            <div className="flex items-center">
              <div className="p-2 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg mr-3">
                <BarChart3 className="w-5 h-5 text-gray-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Ações Rápidas</h3>
                <p className="text-sm text-gray-600">Acesso rápido às principais funções</p>
              </div>
            </div>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-2 gap-4">
              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <Phone className="w-6 h-6 text-green-600 mb-2" />
                <p className="text-sm font-medium text-gray-900">Nova Conversa</p>
                <p className="text-xs text-gray-600">Iniciar chat</p>
              </button>
              
              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <Users className="w-6 h-6 text-blue-600 mb-2" />
                <p className="text-sm font-medium text-gray-900">Contatos</p>
                <p className="text-xs text-gray-600">Gerenciar</p>
              </button>
              
              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <Mail className="w-6 h-6 text-purple-600 mb-2" />
                <p className="text-sm font-medium text-gray-900">Templates</p>
                <p className="text-xs text-gray-600">Criar novo</p>
              </button>
              
              <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                <Calendar className="w-6 h-6 text-orange-600 mb-2" />
                <p className="text-sm font-medium text-gray-900">Relatórios</p>
                <p className="text-xs text-gray-600">Ver dados</p>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
