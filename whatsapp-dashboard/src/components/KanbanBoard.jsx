import { useState, useEffect } from 'react';
import { API_ENDPOINTS, apiRequest } from '../config/api';

const KanbanBoard = () => {
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [draggedContact, setDraggedContact] = useState(null);

  const columns = [
    { id: 'inbox', title: 'Inbox', color: 'bg-gray-100', textColor: 'text-gray-700', count: 0 },
    { id: 'teste', title: 'Teste', color: 'bg-yellow-100', textColor: 'text-yellow-700', count: 0 },
    { id: 'primeiro_contato', title: 'Primeiro Contato', color: 'bg-blue-100', textColor: 'text-blue-700', count: 0 },
    { id: 'informacao', title: 'Só Informação', color: 'bg-purple-100', textColor: 'text-purple-700', count: 0 },
    { id: 'negociacao', title: 'Negociação', color: 'bg-orange-100', textColor: 'text-orange-700', count: 0 },
    { id: 'pagamento', title: 'Pagamento Pendente', color: 'bg-red-100', textColor: 'text-red-700', count: 0 },
    { id: 'cliente', title: 'Cliente', color: 'bg-green-100', textColor: 'text-green-700', count: 0 }
  ];

  useEffect(() => {
    fetchContacts();
  }, []);

  const fetchContacts = async () => {
    try {
      setLoading(true);
      const data = await apiRequest(API_ENDPOINTS.CONTACTS);
      setContacts(data.contacts || []);
    } catch (error) {
      console.error('Erro ao buscar contatos:', error);
      // Dados de demonstração
      setContacts([
        {
          id: 1,
          name: 'Rafaela Cliente',
          phone: '+5511999999999',
          status: 'inbox',
          lastMessage: 'Olá, tenho interesse no produto',
          avatar: null,
          priority: 'high'
        },
        {
          id: 2,
          name: 'Thayna',
          phone: '+5511888888888',
          status: 'teste',
          lastMessage: 'Quando posso testar?',
          avatar: null,
          priority: 'medium'
        },
        {
          id: 3,
          name: 'Cliente Exemplo',
          phone: '+5511777777777',
          status: 'primeiro_contato',
          lastMessage: 'Primeira mensagem',
          avatar: null,
          priority: 'low'
        },
        {
          id: 4,
          name: 'Jamile Televendas',
          phone: '+5511666666666',
          status: 'informacao',
          lastMessage: 'Preciso de mais informações',
          avatar: null,
          priority: 'medium'
        },
        {
          id: 5,
          name: 'Cliente Exemplo 2',
          phone: '+5511555555555',
          status: 'cliente',
          lastMessage: 'Obrigado pelo atendimento!',
          avatar: null,
          priority: 'high'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getContactsByStatus = (status) => {
    return contacts.filter(contact => contact.status === status);
  };

  const getColumnCount = (status) => {
    return getContactsByStatus(status).length;
  };

  const handleDragStart = (e, contact) => {
    setDraggedContact(contact);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e, newStatus) => {
    e.preventDefault();
    
    if (!draggedContact || draggedContact.status === newStatus) {
      setDraggedContact(null);
      return;
    }

    try {
      // Atualizar no backend
      await apiRequest(`${API_ENDPOINTS.CONTACTS}/${draggedContact.id}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status: newStatus })
      });

      // Atualizar estado local
      setContacts(prevContacts =>
        prevContacts.map(contact =>
          contact.id === draggedContact.id
            ? { ...contact, status: newStatus }
            : contact
        )
      );
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      // Atualizar apenas localmente em caso de erro
      setContacts(prevContacts =>
        prevContacts.map(contact =>
          contact.id === draggedContact.id
            ? { ...contact, status: newStatus }
            : contact
        )
      );
    }

    setDraggedContact(null);
  };

  const ContactCard = ({ contact }) => {
    const getPriorityColor = (priority) => {
      switch (priority) {
        case 'high': return 'border-l-red-500';
        case 'medium': return 'border-l-yellow-500';
        case 'low': return 'border-l-green-500';
        default: return 'border-l-gray-300';
      }
    };

    return (
      <div
        draggable
        onDragStart={(e) => handleDragStart(e, contact)}
        className={`bg-white p-3 rounded-lg shadow-sm border-l-4 ${getPriorityColor(contact.priority)} cursor-move hover:shadow-md transition-shadow mb-3`}
      >
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
            {contact.avatar ? (
              <img src={contact.avatar} alt={contact.name} className="w-10 h-10 rounded-full" />
            ) : (
              <span className="text-gray-600 font-medium">
                {contact.name.charAt(0).toUpperCase()}
              </span>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {contact.name}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {contact.lastMessage}
            </p>
          </div>
        </div>
        
        <div className="flex items-center justify-between mt-3">
          <div className="flex space-x-2">
            <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </button>
            <button className="p-1 text-gray-400 hover:text-green-600 transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </button>
            <button className="p-1 text-gray-400 hover:text-purple-600 transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </button>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-xs text-gray-500">Online</span>
          </div>
        </div>
      </div>
    );
  };

  const KanbanColumn = ({ column }) => {
    const columnContacts = getContactsByStatus(column.id);
    const count = getColumnCount(column.id);

    return (
      <div
        className="flex-shrink-0 w-80"
        onDragOver={handleDragOver}
        onDrop={(e) => handleDrop(e, column.id)}
      >
        <div className={`${column.color} rounded-lg p-4 mb-4`}>
          <div className="flex items-center justify-between">
            <h3 className={`font-semibold ${column.textColor}`}>
              {column.title}
            </h3>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${column.textColor} bg-white bg-opacity-50`}>
              {count}
            </span>
          </div>
        </div>
        
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {columnContacts.map(contact => (
            <ContactCard key={contact.id} contact={contact} />
          ))}
          
          {columnContacts.length === 0 && (
            <div className="text-center py-8 text-gray-400">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
              </svg>
              <p className="text-sm">Nenhum contato</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="flex space-x-4 overflow-x-auto">
            {columns.map((_, index) => (
              <div key={index} className="flex-shrink-0 w-80">
                <div className="bg-gray-200 rounded-lg p-4 mb-4 h-16"></div>
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="bg-gray-200 rounded-lg h-24"></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Conversas</h1>
          <p className="text-gray-600">Gerencie suas conversas do WhatsApp</p>
        </div>
        <button
          onClick={fetchContacts}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          Atualizar
        </button>
      </div>

      <div className="flex space-x-6 overflow-x-auto pb-4">
        {columns.map(column => (
          <KanbanColumn key={column.id} column={column} />
        ))}
      </div>
    </div>
  );
};

export default KanbanBoard;

