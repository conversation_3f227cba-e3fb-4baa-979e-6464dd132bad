import { useState } from 'react';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import Contacts from './components/Contacts';
import Templates from './components/Templates';
import KanbanBoard from './components/KanbanBoard';

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');

  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'conversas':
        return <KanbanBoard />;
      case 'contatos':
        return <Contacts />;
      case 'templates':
        return <Templates />;
      case 'automacao':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Automação</h1>
            <p className="text-gray-600">Configure suas regras de automação aqui.</p>
          </div>
        );
      case 'whatsapp':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">WhatsApp</h1>
            <p className="text-gray-600">Configurações de integração com WhatsApp Business API.</p>
          </div>
        );
      case 'analise':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Análise IA</h1>
            <p className="text-gray-600">Análises inteligentes das suas conversas.</p>
          </div>
        );
      case 'configuracoes':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Configurações</h1>
            <p className="text-gray-600">Configurações gerais do sistema.</p>
          </div>
        );
      default:
        return <Dashboard />;
    }
  };

  return (
    <Layout currentPage={currentPage} setCurrentPage={setCurrentPage}>
      {renderPage()}
    </Layout>
  );
}

export default App;

