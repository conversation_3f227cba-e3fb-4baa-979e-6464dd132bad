# Guia de Configuração do VS Code para WhatsApp AI Agent

Este guia detalha os passos para configurar seu ambiente de desenvolvimento no Visual Studio Code (VS Code) para trabalhar eficientemente com o projeto WhatsApp AI Agent. Ele abrange a instalação de pré-requisitos, extensões recomendadas e configurações específicas para o desenvolvimento de backend (Python/Flask) e frontend (React).

## 1. Pré-requisitos

Antes de começar, certifique-se de que os seguintes softwares estão instalados em seu sistema:

- **Visual Studio Code**: Baixe e instale a versão mais recente do [VS Code](https://code.visualstudio.com/).
- **Git**: Instale o [Git](https://git-scm.com/downloads) para controle de versão.
- **Python 3.11 ou superior**: Verifique sua instalação com `python3 --version` no terminal. Se não tiver, baixe em [python.org](https://www.python.org/downloads/).
- **Node.js 20 ou superior**: Verifique sua instalação com `node -v` e `npm -v` ou `pnpm -v` no terminal. Se não tiver, baixe em [nodejs.org](https://nodejs.org/en/download/). Recomenda-se usar `pnpm` para o gerenciamento de pacotes do frontend, que pode ser instalado via `npm install -g pnpm`.

## 2. Clonando o Repositório

Primeiro, clone o repositório do projeto para sua máquina local. Se você já tem o projeto, pode pular esta etapa.

```bash
git clone <URL_DO_SEU_REPOSITORIO>
cd whatsapp-ai-agent
```

Substitua `<URL_DO_SEU_REPOSITORIO>` pela URL real do seu repositório Git.

## 3. Extensões Essenciais do VS Code

As extensões do VS Code aprimoram significativamente a experiência de desenvolvimento. Abra o VS Code, vá para a aba de Extensões (Ctrl+Shift+X ou Cmd+Shift+X) e instale as seguintes:

- **Python** (Microsoft): Essencial para desenvolvimento Python, oferece IntelliSense, depuração, formatação de código e refatoração.
- **Pylance** (Microsoft): Extensão de linguagem para Python que fornece recursos avançados como verificação de tipo, auto-completar e navegação de código.
- **ESLint** (Microsoft): Para linting de JavaScript e TypeScript, garantindo a qualidade e consistência do código frontend.
- **Prettier - Code formatter** (Prettier): Formata automaticamente seu código JavaScript, TypeScript, CSS, HTML, JSON, Markdown, etc., mantendo um estilo consistente.
- **Tailwind CSS IntelliSense** (Tailwind Labs): Oferece auto-completar, linting e recursos de hover para classes Tailwind CSS, acelerando o desenvolvimento do frontend.
- **React Developer Tools** (Facebook): Ferramentas para inspecionar e depurar componentes React diretamente no VS Code (requer a extensão do navegador também).
- **Docker** (Microsoft): Se você planeja usar Docker para desenvolvimento ou deploy, esta extensão facilita o gerenciamento de contêineres e imagens.
- **Remote - SSH** (Microsoft): Se você for desenvolver em um servidor remoto via SSH, esta extensão permite que você use o VS Code como se estivesse desenvolvendo localmente.

## 4. Configuração do Ambiente Python (Backend)

O backend do projeto é desenvolvido em Flask e utiliza um ambiente virtual para gerenciar suas dependências.

### 4.1. Criar e Ativar o Ambiente Virtual

Abra o terminal integrado do VS Code (Ctrl+` ou Cmd+`). Navegue até o diretório `whatsapp-ai-agent` (se ainda não estiver lá) e execute:

```bash
python3 -m venv venv
source venv/bin/activate  # No Linux/macOS
# Para Windows: .\venv\Scripts\activate
```

### 4.2. Instalar Dependências Python

Com o ambiente virtual ativado, instale as dependências listadas no `requirements.txt`:

```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### 4.3. Configurar o VS Code para Usar o Ambiente Virtual

O VS Code geralmente detecta automaticamente o ambiente virtual. Se não o fizer, você pode selecioná-lo manualmente:

1. Pressione `Ctrl+Shift+P` (ou `Cmd+Shift+P`) para abrir a Paleta de Comandos.
2. Digite `Python: Select Interpreter` e selecione a opção.
3. Escolha o interpretador que está dentro da pasta `venv` do seu projeto (ex: `./venv/bin/python`).

## 5. Configuração do Ambiente Node.js (Frontend)

O frontend é um aplicativo React e utiliza `pnpm` para gerenciamento de pacotes.

### 5.1. Instalar Dependências Node.js

Navegue até o diretório `whatsapp-dashboard` no terminal integrado do VS Code:

```bash
cd whatsapp-dashboard
pnpm install
```

### 5.2. Fazer o Build do Frontend para o Backend

Para que o backend Flask possa servir o frontend, você precisa fazer o build do projeto React e copiar os arquivos para o diretório `static` do Flask. Certifique-se de estar no diretório `whatsapp-dashboard`:

```bash
pnpm run build
rm -rf ../whatsapp-ai-agent/src/static/*  # Limpa o diretório static do backend
cp -r dist/* ../whatsapp-ai-agent/src/static/ # Copia os arquivos compilados
```

## 6. Configuração de Variáveis de Ambiente

O projeto utiliza um arquivo `.env` para gerenciar variáveis de ambiente. Um exemplo (`.env.example`) é fornecido. Crie uma cópia e preencha com suas credenciais:

```bash
cd whatsapp-ai-agent
cp .env.example .env
```

Edite o arquivo `.env` e preencha as variáveis como `WHATSAPP_ACCESS_TOKEN`, `WHATSAPP_PHONE_NUMBER_ID`, `WHATSAPP_VERIFY_TOKEN`, `SECRET_KEY`, etc. **Nunca exponha suas chaves secretas em repositórios públicos.**

## 7. Executando o Projeto Localmente

### 7.1. Iniciando o Backend (Flask)

No terminal integrado do VS Code, navegue até o diretório `whatsapp-ai-agent`, ative o ambiente virtual e execute:

```bash
cd whatsapp-ai-agent
source venv/bin/activate
export FLASK_ENV=development # Define o ambiente para desenvolvimento
python src/main.py
```

O backend estará disponível em `http://localhost:5000`.

### 7.2. Iniciando o Frontend (React - Opcional para Desenvolvimento)

Se você estiver fazendo alterações ativas no frontend e quiser ver as mudanças em tempo real com hot-reloading, você pode iniciar o servidor de desenvolvimento do React separadamente. Abra um **novo terminal** no VS Code, navegue até o diretório `whatsapp-dashboard` e execute:

```bash
cd whatsapp-dashboard
pnpm run dev
```

O frontend estará disponível em `http://localhost:5173`. Lembre-se que, ao rodar o frontend separadamente, ele tentará se conectar ao backend em `http://localhost:5000` (ou a URL configurada no `.env`).

## 8. Depuração (Debugging)

O VS Code possui excelentes ferramentas de depuração. Para configurar a depuração:

1. Vá para a aba 


Depuração (Ctrl+Shift+D ou Cmd+Shift+D).
2. Clique em `create a launch.json file` e selecione `Python` ou `Node.js` dependendo do que você quer depurar.

### 8.1. Depuração do Backend (Python/Flask)

No arquivo `launch.json`, adicione uma configuração similar a esta para o Flask:

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Flask",
            "type": "python",
            "request": "launch",
            "module": "flask",
            "env": {
                "FLASK_APP": "src/main.py",
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "1"
            },
            "args": [
                "run",
                "--no-debugger",
                "--no-reloader",
                "--host=0.0.0.0",
                "--port=5000"
            ],
            "jinja": true,
            "cwd": "${workspaceFolder}/whatsapp-ai-agent"
        }
    ]
}
```

Com esta configuração, você pode definir breakpoints em seu código Python e iniciar a depuração. O VS Code irá pausar a execução nos breakpoints, permitindo que você inspecione variáveis, execute código passo a passo, etc.

### 8.2. Depuração do Frontend (React)

Para depurar o frontend React, você pode usar a extensão `Debugger for Chrome` ou `Debugger for Edge`. No `launch.json`, adicione uma configuração para anexar ao seu navegador:

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Chrome against localhost",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:5173",
            "webRoot": "${workspaceFolder}/whatsapp-dashboard"
        }
    ]
}
```

Certifique-se de que o servidor de desenvolvimento do React (`pnpm run dev`) esteja rodando antes de iniciar a depuração do frontend.

## 9. Controle de Versão com Git no VS Code

O VS Code tem integração nativa com o Git, facilitando o controle de versão.

- **Visualização de Alterações**: A aba de Controle de Código-Fonte (Ctrl+Shift+G ou Cmd+Shift+G) mostra todas as alterações nos arquivos.
- **Staging e Commit**: Você pode adicionar arquivos alterados para o staging area e fazer commits diretamente do VS Code.
- **Branching e Merging**: Crie e alterne entre branches, e realize operações de merge.
- **Push/Pull**: Envie (push) suas alterações para o repositório remoto ou puxe (pull) as últimas atualizações.

É altamente recomendável fazer commits pequenos e frequentes com mensagens claras para facilitar o rastreamento das mudanças.

## 10. Dicas de Produtividade no VS Code

- **Atalhos de Teclado**: Aprenda os atalhos de teclado mais comuns (Ctrl+P para busca de arquivos, Ctrl+Shift+F para busca global, etc.).
- **Comando Paleta**: Use `Ctrl+Shift+P` para acessar rapidamente qualquer comando do VS Code.
- **Snippets**: Crie seus próprios snippets de código para trechos de código usados frequentemente.
- **Configurações de Usuário e Workspace**: Personalize o VS Code para suas preferências. As configurações de workspace (`.vscode/settings.json`) são úteis para manter configurações específicas do projeto.
- **Terminal Integrado**: Utilize o terminal integrado para executar comandos Git, scripts de build e iniciar servidores.
- **Multi-cursor Editing**: Pressione `Alt` e clique para adicionar múltiplos cursores e editar várias linhas simultaneamente.

## 11. Integração com Docker (Opcional)

Se você planeja usar Docker para desenvolvimento ou deploy, a extensão Docker para VS Code é muito útil:

- **Gerenciamento de Imagens e Contêineres**: Visualize, inicie, pare e remova contêineres e imagens Docker.
- **Anexar a Contêineres**: Conecte-se a um contêiner em execução para inspecionar arquivos ou executar comandos.
- **Docker Compose**: Suporte para arquivos `docker-compose.yml` para gerenciar múltiplos serviços.

## 12. Considerações Finais

Com este guia, você terá um ambiente de desenvolvimento robusto e eficiente no VS Code para o projeto WhatsApp AI Agent. Lembre-se de manter suas extensões atualizadas e explorar novas funcionalidades do VS Code para otimizar ainda mais seu fluxo de trabalho.

Para qualquer dúvida ou problema, consulte a documentação oficial do VS Code ou a comunidade. A prática leva à perfeição no uso de qualquer ferramenta de desenvolvimento.

---

**Autor**: Manus AI  
**Data**: 09 de Julho de 2025

