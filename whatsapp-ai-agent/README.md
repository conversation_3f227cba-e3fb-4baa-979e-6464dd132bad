# WhatsApp AI Agent

🤖 **Agente de IA completo para automação de interações no WhatsApp**

Solução robusta que replica e expande as funcionalidades do WhaScale.com.br, oferecendo etiquetagem automática, salvamento de contatos, respostas rápidas, dashboard de gerenciamento e integração completa com WhatsApp Business API.

## ✨ Funcionalidades Principais

- 🏷️ **Etiquetagem Automática**: Análise em tempo real e aplicação de etiquetas personalizáveis
- 📱 **Salvamento de Contatos**: Identificação e salvamento automático de novos contatos
- ⚡ **Respostas Rápidas**: Templates de texto, áudio, imagens e vídeos com um clique
- 📊 **Dashboard Completo**: Interface web para gerenciamento e métricas
- 🤖 **IA Avançada**: Análise de sentimento, detecção de intenções e sugestões inteligentes
- 🔐 **Segurança LGPD**: Conformidade total com regulamentações de privacidade
- 🔗 **WhatsApp Business API**: Integração nativa com múltiplas contas
- 📈 **Escalabilidade**: Suporte a alto volume de mensagens e usuários

## 🚀 Quick Start

### Pré-requisitos
- Python 3.11+
- Node.js 20+
- Conta WhatsApp Business API

### Instalação

1. **Clone o repositório:**
```bash
git clone <repository-url>
cd whatsapp-ai-agent
```

2. **Configure o backend:**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
```

3. **Configure o frontend:**
```bash
cd ../whatsapp-dashboard
pnpm install
pnpm run build
cp -r dist/* ../whatsapp-ai-agent/src/static/
```

4. **Configure as variáveis de ambiente:**
```bash
export WHATSAPP_ACCESS_TOKEN=your_token
export WHATSAPP_VERIFY_TOKEN=your_verify_token
export WHATSAPP_PHONE_NUMBER_ID=your_phone_id
```

5. **Inicie a aplicação:**
```bash
cd ../whatsapp-ai-agent
python src/main.py
```

Acesse: http://localhost:5000

## 📁 Estrutura do Projeto

```
whatsapp-ai-agent/
├── src/
│   ├── models/           # Modelos de dados
│   ├── routes/           # APIs REST
│   ├── services/         # Lógica de negócio
│   ├── static/           # Frontend React
│   └── main.py          # Aplicação principal
├── whatsapp-dashboard/   # Código fonte React
├── requirements.txt      # Dependências Python
└── README.md            # Este arquivo
```

## 🔧 Configuração

### WhatsApp Business API

1. Crie uma conta no [Meta for Developers](https://developers.facebook.com/)
2. Configure um app WhatsApp Business
3. Obtenha o Access Token e Phone Number ID
4. Configure o webhook: `https://yourdomain.com/api/whatsapp/webhook`

### Variáveis de Ambiente

```bash
# WhatsApp
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_VERIFY_TOKEN=your_verify_token

# Aplicação
FLASK_ENV=development
SECRET_KEY=your_secret_key
DATABASE_URL=sqlite:///database/app.db
```

## 📚 API Endpoints

### Autenticação
- `POST /api/auth/register` - Registrar usuário
- `POST /api/auth/login` - Login
- `POST /api/auth/verify-token` - Verificar token

### Contatos
- `GET /api/contacts` - Listar contatos
- `POST /api/contacts` - Criar contato
- `PUT /api/contacts/{id}` - Atualizar contato
- `DELETE /api/contacts/{id}` - Deletar contato

### Templates
- `GET /api/templates` - Listar templates
- `POST /api/templates` - Criar template
- `PUT /api/templates/{id}` - Atualizar template

### WhatsApp
- `POST /api/whatsapp/send-message` - Enviar mensagem
- `POST /api/whatsapp/webhook` - Webhook para receber mensagens

### Automação
- `GET /api/automation/rules` - Listar regras
- `POST /api/automation/rules` - Criar regra

### IA
- `POST /api/ai/analyze-message` - Analisar mensagem
- `POST /api/ai/suggest-response` - Sugerir resposta

## 🎨 Dashboard

O dashboard oferece uma interface completa para:

- **Visão Geral**: Métricas e estatísticas em tempo real
- **Contatos**: Gerenciamento completo de contatos e leads
- **Templates**: Criação e edição de templates de mensagens
- **Automação**: Configuração de regras de automação
- **WhatsApp**: Gestão de contas e envio de mensagens
- **Análise IA**: Insights e análises inteligentes

## 🔒 Segurança

- **Autenticação JWT** com tokens seguros
- **Criptografia** de dados sensíveis
- **Conformidade LGPD** com anonimização automática
- **Rate Limiting** para proteção contra ataques
- **Validação** rigorosa de entrada de dados
- **Logs de auditoria** para rastreabilidade

## 🐳 Deploy com Docker

```bash
# Build da imagem
docker build -t whatsapp-ai-agent .

# Executar com docker-compose
docker-compose up -d
```

## 📖 Documentação Completa

Para documentação detalhada, consulte:
- [Documentação Completa](DOCUMENTACAO_WHATSAPP_AI_AGENT.md)
- [API Reference](docs/api.md)
- [Guia de Deploy](docs/deploy.md)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🆘 Suporte

- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Documentação**: [docs.whatsapp-ai-agent.com](https://docs.whatsapp-ai-agent.com)

## 🙏 Agradecimentos

- [WhatsApp Business API](https://developers.facebook.com/docs/whatsapp)
- [Flask](https://flask.palletsprojects.com/)
- [React](https://reactjs.org/)
- [Tailwind CSS](https://tailwindcss.com/)

---

**Desenvolvido com ❤️ para automatizar e otimizar o atendimento via WhatsApp**

