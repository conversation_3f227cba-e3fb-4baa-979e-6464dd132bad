function Q0(a,r){for(var s=0;s<r.length;s++){const c=r[s];if(typeof c!="string"&&!Array.isArray(c)){for(const u in c)if(u!=="default"&&!(u in a)){const m=Object.getOwnPropertyDescriptor(c,u);m&&Object.defineProperty(a,u,m.get?m:{enumerable:!0,get:()=>c[u]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))c(u);new MutationObserver(u=>{for(const m of u)if(m.type==="childList")for(const h of m.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&c(h)}).observe(document,{childList:!0,subtree:!0});function s(u){const m={};return u.integrity&&(m.integrity=u.integrity),u.referrerPolicy&&(m.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?m.credentials="include":u.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function c(u){if(u.ep)return;u.ep=!0;const m=s(u);fetch(u.href,m)}})();function Tv(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var Ks={exports:{}},Mi={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kh;function Z0(){if(kh)return Mi;kh=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function s(c,u,m){var h=null;if(m!==void 0&&(h=""+m),u.key!==void 0&&(h=""+u.key),"key"in u){m={};for(var g in u)g!=="key"&&(m[g]=u[g])}else m=u;return u=m.ref,{$$typeof:a,type:c,key:h,ref:u!==void 0?u:null,props:m}}return Mi.Fragment=r,Mi.jsx=s,Mi.jsxs=s,Mi}var qh;function K0(){return qh||(qh=1,Ks.exports=Z0()),Ks.exports}var f=K0(),Js={exports:{}},ye={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vh;function J0(){if(Vh)return ye;Vh=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),m=Symbol.for("react.consumer"),h=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),N=Symbol.iterator;function C(w){return w===null||typeof w!="object"?null:(w=N&&w[N]||w["@@iterator"],typeof w=="function"?w:null)}var R={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,E={};function _(w,G,F){this.props=w,this.context=G,this.refs=E,this.updater=F||R}_.prototype.isReactComponent={},_.prototype.setState=function(w,G){if(typeof w!="object"&&typeof w!="function"&&w!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,w,G,"setState")},_.prototype.forceUpdate=function(w){this.updater.enqueueForceUpdate(this,w,"forceUpdate")};function k(){}k.prototype=_.prototype;function U(w,G,F){this.props=w,this.context=G,this.refs=E,this.updater=F||R}var B=U.prototype=new k;B.constructor=U,M(B,_.prototype),B.isPureReactComponent=!0;var K=Array.isArray,V={H:null,A:null,T:null,S:null,V:null},ee=Object.prototype.hasOwnProperty;function P(w,G,F,W,te,ge){return F=ge.ref,{$$typeof:a,type:w,key:G,ref:F!==void 0?F:null,props:ge}}function $(w,G){return P(w.type,G,void 0,void 0,void 0,w.props)}function se(w){return typeof w=="object"&&w!==null&&w.$$typeof===a}function J(w){var G={"=":"=0",":":"=2"};return"$"+w.replace(/[=:]/g,function(F){return G[F]})}var ue=/\/+/g;function re(w,G){return typeof w=="object"&&w!==null&&w.key!=null?J(""+w.key):G.toString(36)}function ve(){}function pe(w){switch(w.status){case"fulfilled":return w.value;case"rejected":throw w.reason;default:switch(typeof w.status=="string"?w.then(ve,ve):(w.status="pending",w.then(function(G){w.status==="pending"&&(w.status="fulfilled",w.value=G)},function(G){w.status==="pending"&&(w.status="rejected",w.reason=G)})),w.status){case"fulfilled":return w.value;case"rejected":throw w.reason}}throw w}function Y(w,G,F,W,te){var ge=typeof w;(ge==="undefined"||ge==="boolean")&&(w=null);var ce=!1;if(w===null)ce=!0;else switch(ge){case"bigint":case"string":case"number":ce=!0;break;case"object":switch(w.$$typeof){case a:case r:ce=!0;break;case b:return ce=w._init,Y(ce(w._payload),G,F,W,te)}}if(ce)return te=te(w),ce=W===""?"."+re(w,0):W,K(te)?(F="",ce!=null&&(F=ce.replace(ue,"$&/")+"/"),Y(te,G,F,"",function(Re){return Re})):te!=null&&(se(te)&&(te=$(te,F+(te.key==null||w&&w.key===te.key?"":(""+te.key).replace(ue,"$&/")+"/")+ce)),G.push(te)),1;ce=0;var I=W===""?".":W+":";if(K(w))for(var fe=0;fe<w.length;fe++)W=w[fe],ge=I+re(W,fe),ce+=Y(W,G,F,ge,te);else if(fe=C(w),typeof fe=="function")for(w=fe.call(w),fe=0;!(W=w.next()).done;)W=W.value,ge=I+re(W,fe++),ce+=Y(W,G,F,ge,te);else if(ge==="object"){if(typeof w.then=="function")return Y(pe(w),G,F,W,te);throw G=String(w),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(w).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return ce}function j(w,G,F){if(w==null)return w;var W=[],te=0;return Y(w,W,"","",function(ge){return G.call(F,ge,te++)}),W}function Z(w){if(w._status===-1){var G=w._result;G=G(),G.then(function(F){(w._status===0||w._status===-1)&&(w._status=1,w._result=F)},function(F){(w._status===0||w._status===-1)&&(w._status=2,w._result=F)}),w._status===-1&&(w._status=0,w._result=G)}if(w._status===1)return w._result.default;throw w._result}var L=typeof reportError=="function"?reportError:function(w){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof w=="object"&&w!==null&&typeof w.message=="string"?String(w.message):String(w),error:w});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",w);return}console.error(w)};function ie(){}return ye.Children={map:j,forEach:function(w,G,F){j(w,function(){G.apply(this,arguments)},F)},count:function(w){var G=0;return j(w,function(){G++}),G},toArray:function(w){return j(w,function(G){return G})||[]},only:function(w){if(!se(w))throw Error("React.Children.only expected to receive a single React element child.");return w}},ye.Component=_,ye.Fragment=s,ye.Profiler=u,ye.PureComponent=U,ye.StrictMode=c,ye.Suspense=y,ye.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=V,ye.__COMPILER_RUNTIME={__proto__:null,c:function(w){return V.H.useMemoCache(w)}},ye.cache=function(w){return function(){return w.apply(null,arguments)}},ye.cloneElement=function(w,G,F){if(w==null)throw Error("The argument must be a React element, but you passed "+w+".");var W=M({},w.props),te=w.key,ge=void 0;if(G!=null)for(ce in G.ref!==void 0&&(ge=void 0),G.key!==void 0&&(te=""+G.key),G)!ee.call(G,ce)||ce==="key"||ce==="__self"||ce==="__source"||ce==="ref"&&G.ref===void 0||(W[ce]=G[ce]);var ce=arguments.length-2;if(ce===1)W.children=F;else if(1<ce){for(var I=Array(ce),fe=0;fe<ce;fe++)I[fe]=arguments[fe+2];W.children=I}return P(w.type,te,void 0,void 0,ge,W)},ye.createContext=function(w){return w={$$typeof:h,_currentValue:w,_currentValue2:w,_threadCount:0,Provider:null,Consumer:null},w.Provider=w,w.Consumer={$$typeof:m,_context:w},w},ye.createElement=function(w,G,F){var W,te={},ge=null;if(G!=null)for(W in G.key!==void 0&&(ge=""+G.key),G)ee.call(G,W)&&W!=="key"&&W!=="__self"&&W!=="__source"&&(te[W]=G[W]);var ce=arguments.length-2;if(ce===1)te.children=F;else if(1<ce){for(var I=Array(ce),fe=0;fe<ce;fe++)I[fe]=arguments[fe+2];te.children=I}if(w&&w.defaultProps)for(W in ce=w.defaultProps,ce)te[W]===void 0&&(te[W]=ce[W]);return P(w,ge,void 0,void 0,null,te)},ye.createRef=function(){return{current:null}},ye.forwardRef=function(w){return{$$typeof:g,render:w}},ye.isValidElement=se,ye.lazy=function(w){return{$$typeof:b,_payload:{_status:-1,_result:w},_init:Z}},ye.memo=function(w,G){return{$$typeof:v,type:w,compare:G===void 0?null:G}},ye.startTransition=function(w){var G=V.T,F={};V.T=F;try{var W=w(),te=V.S;te!==null&&te(F,W),typeof W=="object"&&W!==null&&typeof W.then=="function"&&W.then(ie,L)}catch(ge){L(ge)}finally{V.T=G}},ye.unstable_useCacheRefresh=function(){return V.H.useCacheRefresh()},ye.use=function(w){return V.H.use(w)},ye.useActionState=function(w,G,F){return V.H.useActionState(w,G,F)},ye.useCallback=function(w,G){return V.H.useCallback(w,G)},ye.useContext=function(w){return V.H.useContext(w)},ye.useDebugValue=function(){},ye.useDeferredValue=function(w,G){return V.H.useDeferredValue(w,G)},ye.useEffect=function(w,G,F){var W=V.H;if(typeof F=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return W.useEffect(w,G)},ye.useId=function(){return V.H.useId()},ye.useImperativeHandle=function(w,G,F){return V.H.useImperativeHandle(w,G,F)},ye.useInsertionEffect=function(w,G){return V.H.useInsertionEffect(w,G)},ye.useLayoutEffect=function(w,G){return V.H.useLayoutEffect(w,G)},ye.useMemo=function(w,G){return V.H.useMemo(w,G)},ye.useOptimistic=function(w,G){return V.H.useOptimistic(w,G)},ye.useReducer=function(w,G,F){return V.H.useReducer(w,G,F)},ye.useRef=function(w){return V.H.useRef(w)},ye.useState=function(w){return V.H.useState(w)},ye.useSyncExternalStore=function(w,G,F){return V.H.useSyncExternalStore(w,G,F)},ye.useTransition=function(){return V.H.useTransition()},ye.version="19.1.0",ye}var Yh;function ju(){return Yh||(Yh=1,Js.exports=J0()),Js.exports}var x=ju();const In=Tv(x),Cv=Q0({__proto__:null,default:In},[x]);var $s={exports:{}},Di={},Ws={exports:{}},Ps={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gh;function $0(){return Gh||(Gh=1,function(a){function r(j,Z){var L=j.length;j.push(Z);e:for(;0<L;){var ie=L-1>>>1,w=j[ie];if(0<u(w,Z))j[ie]=Z,j[L]=w,L=ie;else break e}}function s(j){return j.length===0?null:j[0]}function c(j){if(j.length===0)return null;var Z=j[0],L=j.pop();if(L!==Z){j[0]=L;e:for(var ie=0,w=j.length,G=w>>>1;ie<G;){var F=2*(ie+1)-1,W=j[F],te=F+1,ge=j[te];if(0>u(W,L))te<w&&0>u(ge,W)?(j[ie]=ge,j[te]=L,ie=te):(j[ie]=W,j[F]=L,ie=F);else if(te<w&&0>u(ge,L))j[ie]=ge,j[te]=L,ie=te;else break e}}return Z}function u(j,Z){var L=j.sortIndex-Z.sortIndex;return L!==0?L:j.id-Z.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;a.unstable_now=function(){return m.now()}}else{var h=Date,g=h.now();a.unstable_now=function(){return h.now()-g}}var y=[],v=[],b=1,N=null,C=3,R=!1,M=!1,E=!1,_=!1,k=typeof setTimeout=="function"?setTimeout:null,U=typeof clearTimeout=="function"?clearTimeout:null,B=typeof setImmediate<"u"?setImmediate:null;function K(j){for(var Z=s(v);Z!==null;){if(Z.callback===null)c(v);else if(Z.startTime<=j)c(v),Z.sortIndex=Z.expirationTime,r(y,Z);else break;Z=s(v)}}function V(j){if(E=!1,K(j),!M)if(s(y)!==null)M=!0,ee||(ee=!0,re());else{var Z=s(v);Z!==null&&Y(V,Z.startTime-j)}}var ee=!1,P=-1,$=5,se=-1;function J(){return _?!0:!(a.unstable_now()-se<$)}function ue(){if(_=!1,ee){var j=a.unstable_now();se=j;var Z=!0;try{e:{M=!1,E&&(E=!1,U(P),P=-1),R=!0;var L=C;try{t:{for(K(j),N=s(y);N!==null&&!(N.expirationTime>j&&J());){var ie=N.callback;if(typeof ie=="function"){N.callback=null,C=N.priorityLevel;var w=ie(N.expirationTime<=j);if(j=a.unstable_now(),typeof w=="function"){N.callback=w,K(j),Z=!0;break t}N===s(y)&&c(y),K(j)}else c(y);N=s(y)}if(N!==null)Z=!0;else{var G=s(v);G!==null&&Y(V,G.startTime-j),Z=!1}}break e}finally{N=null,C=L,R=!1}Z=void 0}}finally{Z?re():ee=!1}}}var re;if(typeof B=="function")re=function(){B(ue)};else if(typeof MessageChannel<"u"){var ve=new MessageChannel,pe=ve.port2;ve.port1.onmessage=ue,re=function(){pe.postMessage(null)}}else re=function(){k(ue,0)};function Y(j,Z){P=k(function(){j(a.unstable_now())},Z)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(j){j.callback=null},a.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<j?Math.floor(1e3/j):5},a.unstable_getCurrentPriorityLevel=function(){return C},a.unstable_next=function(j){switch(C){case 1:case 2:case 3:var Z=3;break;default:Z=C}var L=C;C=Z;try{return j()}finally{C=L}},a.unstable_requestPaint=function(){_=!0},a.unstable_runWithPriority=function(j,Z){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var L=C;C=j;try{return Z()}finally{C=L}},a.unstable_scheduleCallback=function(j,Z,L){var ie=a.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?ie+L:ie):L=ie,j){case 1:var w=-1;break;case 2:w=250;break;case 5:w=1073741823;break;case 4:w=1e4;break;default:w=5e3}return w=L+w,j={id:b++,callback:Z,priorityLevel:j,startTime:L,expirationTime:w,sortIndex:-1},L>ie?(j.sortIndex=L,r(v,j),s(y)===null&&j===s(v)&&(E?(U(P),P=-1):E=!0,Y(V,L-ie))):(j.sortIndex=w,r(y,j),M||R||(M=!0,ee||(ee=!0,re()))),j},a.unstable_shouldYield=J,a.unstable_wrapCallback=function(j){var Z=C;return function(){var L=C;C=Z;try{return j.apply(this,arguments)}finally{C=L}}}}(Ps)),Ps}var Xh;function W0(){return Xh||(Xh=1,Ws.exports=$0()),Ws.exports}var Fs={exports:{}},mt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qh;function P0(){if(Qh)return mt;Qh=1;var a=ju();function r(y){var v="https://react.dev/errors/"+y;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)v+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+y+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var c={d:{f:s,r:function(){throw Error(r(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},u=Symbol.for("react.portal");function m(y,v,b){var N=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:N==null?null:""+N,children:y,containerInfo:v,implementation:b}}var h=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(y,v){if(y==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return mt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,mt.createPortal=function(y,v){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(r(299));return m(y,v,null,b)},mt.flushSync=function(y){var v=h.T,b=c.p;try{if(h.T=null,c.p=2,y)return y()}finally{h.T=v,c.p=b,c.d.f()}},mt.preconnect=function(y,v){typeof y=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,c.d.C(y,v))},mt.prefetchDNS=function(y){typeof y=="string"&&c.d.D(y)},mt.preinit=function(y,v){if(typeof y=="string"&&v&&typeof v.as=="string"){var b=v.as,N=g(b,v.crossOrigin),C=typeof v.integrity=="string"?v.integrity:void 0,R=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;b==="style"?c.d.S(y,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:N,integrity:C,fetchPriority:R}):b==="script"&&c.d.X(y,{crossOrigin:N,integrity:C,fetchPriority:R,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},mt.preinitModule=function(y,v){if(typeof y=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var b=g(v.as,v.crossOrigin);c.d.M(y,{crossOrigin:b,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&c.d.M(y)},mt.preload=function(y,v){if(typeof y=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var b=v.as,N=g(b,v.crossOrigin);c.d.L(y,b,{crossOrigin:N,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},mt.preloadModule=function(y,v){if(typeof y=="string")if(v){var b=g(v.as,v.crossOrigin);c.d.m(y,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:b,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else c.d.m(y)},mt.requestFormReset=function(y){c.d.r(y)},mt.unstable_batchedUpdates=function(y,v){return y(v)},mt.useFormState=function(y,v,b){return h.H.useFormState(y,v,b)},mt.useFormStatus=function(){return h.H.useHostTransitionStatus()},mt.version="19.1.0",mt}var Zh;function jv(){if(Zh)return Fs.exports;Zh=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),Fs.exports=P0(),Fs.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kh;function F0(){if(Kh)return Di;Kh=1;var a=W0(),r=ju(),s=jv();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function m(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(m(e)!==e)throw Error(c(188))}function y(e){var t=e.alternate;if(!t){if(t=m(e),t===null)throw Error(c(188));return t!==e?null:e}for(var n=e,l=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return g(i),e;if(o===l)return g(i),t;o=o.sibling}throw Error(c(188))}if(n.return!==l.return)n=i,l=o;else{for(var d=!1,p=i.child;p;){if(p===n){d=!0,n=i,l=o;break}if(p===l){d=!0,l=i,n=o;break}p=p.sibling}if(!d){for(p=o.child;p;){if(p===n){d=!0,n=o,l=i;break}if(p===l){d=!0,l=o,n=i;break}p=p.sibling}if(!d)throw Error(c(189))}}if(n.alternate!==l)throw Error(c(190))}if(n.tag!==3)throw Error(c(188));return n.stateNode.current===n?e:t}function v(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=v(e),t!==null)return t;e=e.sibling}return null}var b=Object.assign,N=Symbol.for("react.element"),C=Symbol.for("react.transitional.element"),R=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),_=Symbol.for("react.profiler"),k=Symbol.for("react.provider"),U=Symbol.for("react.consumer"),B=Symbol.for("react.context"),K=Symbol.for("react.forward_ref"),V=Symbol.for("react.suspense"),ee=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),$=Symbol.for("react.lazy"),se=Symbol.for("react.activity"),J=Symbol.for("react.memo_cache_sentinel"),ue=Symbol.iterator;function re(e){return e===null||typeof e!="object"?null:(e=ue&&e[ue]||e["@@iterator"],typeof e=="function"?e:null)}var ve=Symbol.for("react.client.reference");function pe(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ve?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case M:return"Fragment";case _:return"Profiler";case E:return"StrictMode";case V:return"Suspense";case ee:return"SuspenseList";case se:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case R:return"Portal";case B:return(e.displayName||"Context")+".Provider";case U:return(e._context.displayName||"Context")+".Consumer";case K:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case P:return t=e.displayName||null,t!==null?t:pe(e.type)||"Memo";case $:t=e._payload,e=e._init;try{return pe(e(t))}catch{}}return null}var Y=Array.isArray,j=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Z=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,L={pending:!1,data:null,method:null,action:null},ie=[],w=-1;function G(e){return{current:e}}function F(e){0>w||(e.current=ie[w],ie[w]=null,w--)}function W(e,t){w++,ie[w]=e.current,e.current=t}var te=G(null),ge=G(null),ce=G(null),I=G(null);function fe(e,t){switch(W(ce,t),W(ge,e),W(te,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?mh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=mh(t),e=hh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(te),W(te,e)}function Re(){F(te),F(ge),F(ce)}function Te(e){e.memoizedState!==null&&W(I,e);var t=te.current,n=hh(t,e.type);t!==n&&(W(ge,e),W(te,n))}function we(e){ge.current===e&&(F(te),F(ge)),I.current===e&&(F(I),Ci._currentValue=L)}var Ee=Object.prototype.hasOwnProperty,ot=a.unstable_scheduleCallback,gt=a.unstable_cancelCallback,ol=a.unstable_shouldYield,rl=a.unstable_requestPaint,ut=a.unstable_now,Ur=a.unstable_getCurrentPriorityLevel,cl=a.unstable_ImmediatePriority,Ku=a.unstable_UserBlockingPriority,Qi=a.unstable_NormalPriority,Tp=a.unstable_LowPriority,Ju=a.unstable_IdlePriority,Cp=a.log,jp=a.unstable_setDisableYieldValue,Ua=null,wt=null;function jn(e){if(typeof Cp=="function"&&jp(e),wt&&typeof wt.setStrictMode=="function")try{wt.setStrictMode(Ua,e)}catch{}}var Et=Math.clz32?Math.clz32:Op,_p=Math.log,Rp=Math.LN2;function Op(e){return e>>>=0,e===0?32:31-(_p(e)/Rp|0)|0}var Zi=256,Ki=4194304;function sl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Ji(e,t,n){var l=e.pendingLanes;if(l===0)return 0;var i=0,o=e.suspendedLanes,d=e.pingedLanes;e=e.warmLanes;var p=l&134217727;return p!==0?(l=p&~o,l!==0?i=sl(l):(d&=p,d!==0?i=sl(d):n||(n=p&~e,n!==0&&(i=sl(n))))):(p=l&~o,p!==0?i=sl(p):d!==0?i=sl(d):n||(n=l&~e,n!==0&&(i=sl(n)))),i===0?0:t!==0&&t!==i&&(t&o)===0&&(o=i&-i,n=t&-t,o>=n||o===32&&(n&4194048)!==0)?t:i}function Ha(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Mp(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function $u(){var e=Zi;return Zi<<=1,(Zi&4194048)===0&&(Zi=256),e}function Wu(){var e=Ki;return Ki<<=1,(Ki&62914560)===0&&(Ki=4194304),e}function Hr(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ba(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Dp(e,t,n,l,i,o){var d=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var p=e.entanglements,S=e.expirationTimes,D=e.hiddenUpdates;for(n=d&~n;0<n;){var q=31-Et(n),Q=1<<q;p[q]=0,S[q]=-1;var z=D[q];if(z!==null)for(D[q]=null,q=0;q<z.length;q++){var H=z[q];H!==null&&(H.lane&=-536870913)}n&=~Q}l!==0&&Pu(e,l,0),o!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=o&~(d&~t))}function Pu(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-Et(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|n&4194090}function Fu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var l=31-Et(n),i=1<<l;i&t|e[l]&t&&(e[l]|=t),n&=~i}}function Br(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Lr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Iu(){var e=Z.p;return e!==0?e:(e=window.event,e===void 0?32:Dh(e.type))}function zp(e,t){var n=Z.p;try{return Z.p=e,t()}finally{Z.p=n}}var _n=Math.random().toString(36).slice(2),ft="__reactFiber$"+_n,pt="__reactProps$"+_n,Dl="__reactContainer$"+_n,kr="__reactEvents$"+_n,Up="__reactListeners$"+_n,Hp="__reactHandles$"+_n,ef="__reactResources$"+_n,La="__reactMarker$"+_n;function qr(e){delete e[ft],delete e[pt],delete e[kr],delete e[Up],delete e[Hp]}function zl(e){var t=e[ft];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Dl]||n[ft]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=yh(e);e!==null;){if(n=e[ft])return n;e=yh(e)}return t}e=n,n=e.parentNode}return null}function Ul(e){if(e=e[ft]||e[Dl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ka(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function Hl(e){var t=e[ef];return t||(t=e[ef]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function nt(e){e[La]=!0}var tf=new Set,nf={};function ul(e,t){Bl(e,t),Bl(e+"Capture",t)}function Bl(e,t){for(nf[e]=t,e=0;e<t.length;e++)tf.add(t[e])}var Bp=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),lf={},af={};function Lp(e){return Ee.call(af,e)?!0:Ee.call(lf,e)?!1:Bp.test(e)?af[e]=!0:(lf[e]=!0,!1)}function $i(e,t,n){if(Lp(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Wi(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function cn(e,t,n,l){if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+l)}}var Vr,of;function Ll(e){if(Vr===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Vr=t&&t[1]||"",of=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Vr+e+of}var Yr=!1;function Gr(e,t){if(!e||Yr)return"";Yr=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(H){var z=H}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(H){z=H}e.call(Q.prototype)}}else{try{throw Error()}catch(H){z=H}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(H){if(H&&z&&typeof H.stack=="string")return[H.stack,z.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=l.DetermineComponentFrameRoot(),d=o[0],p=o[1];if(d&&p){var S=d.split(`
`),D=p.split(`
`);for(i=l=0;l<S.length&&!S[l].includes("DetermineComponentFrameRoot");)l++;for(;i<D.length&&!D[i].includes("DetermineComponentFrameRoot");)i++;if(l===S.length||i===D.length)for(l=S.length-1,i=D.length-1;1<=l&&0<=i&&S[l]!==D[i];)i--;for(;1<=l&&0<=i;l--,i--)if(S[l]!==D[i]){if(l!==1||i!==1)do if(l--,i--,0>i||S[l]!==D[i]){var q=`
`+S[l].replace(" at new "," at ");return e.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",e.displayName)),q}while(1<=l&&0<=i);break}}}finally{Yr=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Ll(n):""}function kp(e){switch(e.tag){case 26:case 27:case 5:return Ll(e.type);case 16:return Ll("Lazy");case 13:return Ll("Suspense");case 19:return Ll("SuspenseList");case 0:case 15:return Gr(e.type,!1);case 11:return Gr(e.type.render,!1);case 1:return Gr(e.type,!0);case 31:return Ll("Activity");default:return""}}function rf(e){try{var t="";do t+=kp(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Dt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function cf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function qp(e){var t=cf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(d){l=""+d,o.call(this,d)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(d){l=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Pi(e){e._valueTracker||(e._valueTracker=qp(e))}function sf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),l="";return e&&(l=cf(e)?e.checked?"true":"false":e.value),e=l,e!==n?(t.setValue(e),!0):!1}function Fi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Vp=/[\n"\\]/g;function zt(e){return e.replace(Vp,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Xr(e,t,n,l,i,o,d,p){e.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.type=d:e.removeAttribute("type"),t!=null?d==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Dt(t)):e.value!==""+Dt(t)&&(e.value=""+Dt(t)):d!=="submit"&&d!=="reset"||e.removeAttribute("value"),t!=null?Qr(e,d,Dt(t)):n!=null?Qr(e,d,Dt(n)):l!=null&&e.removeAttribute("value"),i==null&&o!=null&&(e.defaultChecked=!!o),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+Dt(p):e.removeAttribute("name")}function uf(e,t,n,l,i,o,d,p){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(e.type=o),t!=null||n!=null){if(!(o!=="submit"&&o!=="reset"||t!=null))return;n=n!=null?""+Dt(n):"",t=t!=null?""+Dt(t):n,p||t===e.value||(e.value=t),e.defaultValue=t}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=p?e.checked:!!l,e.defaultChecked=!!l,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.name=d)}function Qr(e,t,n){t==="number"&&Fi(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function kl(e,t,n,l){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&l&&(e[n].defaultSelected=!0)}else{for(n=""+Dt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,l&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function ff(e,t,n){if(t!=null&&(t=""+Dt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Dt(n):""}function df(e,t,n,l){if(t==null){if(l!=null){if(n!=null)throw Error(c(92));if(Y(l)){if(1<l.length)throw Error(c(93));l=l[0]}n=l}n==null&&(n=""),t=n}n=Dt(t),e.defaultValue=n,l=e.textContent,l===n&&l!==""&&l!==null&&(e.value=l)}function ql(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Yp=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function mf(e,t,n){var l=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,n):typeof n!="number"||n===0||Yp.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function hf(e,t,n){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var i in t)l=t[i],t.hasOwnProperty(i)&&n[i]!==l&&mf(e,i,l)}else for(var o in t)t.hasOwnProperty(o)&&mf(e,o,t[o])}function Zr(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Gp=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Xp=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ii(e){return Xp.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Kr=null;function Jr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Vl=null,Yl=null;function vf(e){var t=Ul(e);if(t&&(e=t.stateNode)){var n=e[pt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Xr(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+zt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var l=n[t];if(l!==e&&l.form===e.form){var i=l[pt]||null;if(!i)throw Error(c(90));Xr(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)l=n[t],l.form===e.form&&sf(l)}break e;case"textarea":ff(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&kl(e,!!n.multiple,t,!1)}}}var $r=!1;function gf(e,t,n){if($r)return e(t,n);$r=!0;try{var l=e(t);return l}finally{if($r=!1,(Vl!==null||Yl!==null)&&(ko(),Vl&&(t=Vl,e=Yl,Yl=Vl=null,vf(t),e)))for(t=0;t<e.length;t++)vf(e[t])}}function qa(e,t){var n=e.stateNode;if(n===null)return null;var l=n[pt]||null;if(l===null)return null;n=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(c(231,t,typeof n));return n}var sn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Wr=!1;if(sn)try{var Va={};Object.defineProperty(Va,"passive",{get:function(){Wr=!0}}),window.addEventListener("test",Va,Va),window.removeEventListener("test",Va,Va)}catch{Wr=!1}var Rn=null,Pr=null,eo=null;function pf(){if(eo)return eo;var e,t=Pr,n=t.length,l,i="value"in Rn?Rn.value:Rn.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var d=n-e;for(l=1;l<=d&&t[n-l]===i[o-l];l++);return eo=i.slice(e,1<l?1-l:void 0)}function to(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function no(){return!0}function yf(){return!1}function yt(e){function t(n,l,i,o,d){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=o,this.target=d,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(n=e[p],this[p]=n?n(o):o[p]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?no:yf,this.isPropagationStopped=yf,this}return b(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=no)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=no)},persist:function(){},isPersistent:no}),t}var fl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},lo=yt(fl),Ya=b({},fl,{view:0,detail:0}),Qp=yt(Ya),Fr,Ir,Ga,ao=b({},Ya,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:tc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ga&&(Ga&&e.type==="mousemove"?(Fr=e.screenX-Ga.screenX,Ir=e.screenY-Ga.screenY):Ir=Fr=0,Ga=e),Fr)},movementY:function(e){return"movementY"in e?e.movementY:Ir}}),xf=yt(ao),Zp=b({},ao,{dataTransfer:0}),Kp=yt(Zp),Jp=b({},Ya,{relatedTarget:0}),ec=yt(Jp),$p=b({},fl,{animationName:0,elapsedTime:0,pseudoElement:0}),Wp=yt($p),Pp=b({},fl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Fp=yt(Pp),Ip=b({},fl,{data:0}),bf=yt(Ip),ey={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ty={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ny={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ly(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=ny[e])?!!t[e]:!1}function tc(){return ly}var ay=b({},Ya,{key:function(e){if(e.key){var t=ey[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=to(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ty[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:tc,charCode:function(e){return e.type==="keypress"?to(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?to(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),iy=yt(ay),oy=b({},ao,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Sf=yt(oy),ry=b({},Ya,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:tc}),cy=yt(ry),sy=b({},fl,{propertyName:0,elapsedTime:0,pseudoElement:0}),uy=yt(sy),fy=b({},ao,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),dy=yt(fy),my=b({},fl,{newState:0,oldState:0}),hy=yt(my),vy=[9,13,27,32],nc=sn&&"CompositionEvent"in window,Xa=null;sn&&"documentMode"in document&&(Xa=document.documentMode);var gy=sn&&"TextEvent"in window&&!Xa,wf=sn&&(!nc||Xa&&8<Xa&&11>=Xa),Ef=" ",Nf=!1;function Af(e,t){switch(e){case"keyup":return vy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Gl=!1;function py(e,t){switch(e){case"compositionend":return Tf(t);case"keypress":return t.which!==32?null:(Nf=!0,Ef);case"textInput":return e=t.data,e===Ef&&Nf?null:e;default:return null}}function yy(e,t){if(Gl)return e==="compositionend"||!nc&&Af(e,t)?(e=pf(),eo=Pr=Rn=null,Gl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return wf&&t.locale!=="ko"?null:t.data;default:return null}}var xy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Cf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!xy[e.type]:t==="textarea"}function jf(e,t,n,l){Vl?Yl?Yl.push(l):Yl=[l]:Vl=l,t=Qo(t,"onChange"),0<t.length&&(n=new lo("onChange","change",null,n,l),e.push({event:n,listeners:t}))}var Qa=null,Za=null;function by(e){ch(e,0)}function io(e){var t=ka(e);if(sf(t))return e}function _f(e,t){if(e==="change")return t}var Rf=!1;if(sn){var lc;if(sn){var ac="oninput"in document;if(!ac){var Of=document.createElement("div");Of.setAttribute("oninput","return;"),ac=typeof Of.oninput=="function"}lc=ac}else lc=!1;Rf=lc&&(!document.documentMode||9<document.documentMode)}function Mf(){Qa&&(Qa.detachEvent("onpropertychange",Df),Za=Qa=null)}function Df(e){if(e.propertyName==="value"&&io(Za)){var t=[];jf(t,Za,e,Jr(e)),gf(by,t)}}function Sy(e,t,n){e==="focusin"?(Mf(),Qa=t,Za=n,Qa.attachEvent("onpropertychange",Df)):e==="focusout"&&Mf()}function wy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return io(Za)}function Ey(e,t){if(e==="click")return io(t)}function Ny(e,t){if(e==="input"||e==="change")return io(t)}function Ay(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Nt=typeof Object.is=="function"?Object.is:Ay;function Ka(e,t){if(Nt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!Ee.call(t,i)||!Nt(e[i],t[i]))return!1}return!0}function zf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Uf(e,t){var n=zf(e);e=0;for(var l;n;){if(n.nodeType===3){if(l=e+n.textContent.length,e<=t&&l>=t)return{node:n,offset:t-e};e=l}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=zf(n)}}function Hf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Hf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Bf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Fi(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Fi(e.document)}return t}function ic(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Ty=sn&&"documentMode"in document&&11>=document.documentMode,Xl=null,oc=null,Ja=null,rc=!1;function Lf(e,t,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;rc||Xl==null||Xl!==Fi(l)||(l=Xl,"selectionStart"in l&&ic(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Ja&&Ka(Ja,l)||(Ja=l,l=Qo(oc,"onSelect"),0<l.length&&(t=new lo("onSelect","select",null,t,n),e.push({event:t,listeners:l}),t.target=Xl)))}function dl(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ql={animationend:dl("Animation","AnimationEnd"),animationiteration:dl("Animation","AnimationIteration"),animationstart:dl("Animation","AnimationStart"),transitionrun:dl("Transition","TransitionRun"),transitionstart:dl("Transition","TransitionStart"),transitioncancel:dl("Transition","TransitionCancel"),transitionend:dl("Transition","TransitionEnd")},cc={},kf={};sn&&(kf=document.createElement("div").style,"AnimationEvent"in window||(delete Ql.animationend.animation,delete Ql.animationiteration.animation,delete Ql.animationstart.animation),"TransitionEvent"in window||delete Ql.transitionend.transition);function ml(e){if(cc[e])return cc[e];if(!Ql[e])return e;var t=Ql[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in kf)return cc[e]=t[n];return e}var qf=ml("animationend"),Vf=ml("animationiteration"),Yf=ml("animationstart"),Cy=ml("transitionrun"),jy=ml("transitionstart"),_y=ml("transitioncancel"),Gf=ml("transitionend"),Xf=new Map,sc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");sc.push("scrollEnd");function Yt(e,t){Xf.set(e,t),ul(t,[e])}var Qf=new WeakMap;function Ut(e,t){if(typeof e=="object"&&e!==null){var n=Qf.get(e);return n!==void 0?n:(t={value:e,source:t,stack:rf(t)},Qf.set(e,t),t)}return{value:e,source:t,stack:rf(t)}}var Ht=[],Zl=0,uc=0;function oo(){for(var e=Zl,t=uc=Zl=0;t<e;){var n=Ht[t];Ht[t++]=null;var l=Ht[t];Ht[t++]=null;var i=Ht[t];Ht[t++]=null;var o=Ht[t];if(Ht[t++]=null,l!==null&&i!==null){var d=l.pending;d===null?i.next=i:(i.next=d.next,d.next=i),l.pending=i}o!==0&&Zf(n,i,o)}}function ro(e,t,n,l){Ht[Zl++]=e,Ht[Zl++]=t,Ht[Zl++]=n,Ht[Zl++]=l,uc|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function fc(e,t,n,l){return ro(e,t,n,l),co(e)}function Kl(e,t){return ro(e,null,null,t),co(e)}function Zf(e,t,n){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n);for(var i=!1,o=e.return;o!==null;)o.childLanes|=n,l=o.alternate,l!==null&&(l.childLanes|=n),o.tag===22&&(e=o.stateNode,e===null||e._visibility&1||(i=!0)),e=o,o=o.return;return e.tag===3?(o=e.stateNode,i&&t!==null&&(i=31-Et(n),e=o.hiddenUpdates,l=e[i],l===null?e[i]=[t]:l.push(t),t.lane=n|536870912),o):null}function co(e){if(50<xi)throw xi=0,ps=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Jl={};function Ry(e,t,n,l){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function At(e,t,n,l){return new Ry(e,t,n,l)}function dc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function un(e,t){var n=e.alternate;return n===null?(n=At(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Kf(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function so(e,t,n,l,i,o){var d=0;if(l=e,typeof e=="function")dc(e)&&(d=1);else if(typeof e=="string")d=M0(e,n,te.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case se:return e=At(31,n,t,i),e.elementType=se,e.lanes=o,e;case M:return hl(n.children,i,o,t);case E:d=8,i|=24;break;case _:return e=At(12,n,t,i|2),e.elementType=_,e.lanes=o,e;case V:return e=At(13,n,t,i),e.elementType=V,e.lanes=o,e;case ee:return e=At(19,n,t,i),e.elementType=ee,e.lanes=o,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case k:case B:d=10;break e;case U:d=9;break e;case K:d=11;break e;case P:d=14;break e;case $:d=16,l=null;break e}d=29,n=Error(c(130,e===null?"null":typeof e,"")),l=null}return t=At(d,n,t,i),t.elementType=e,t.type=l,t.lanes=o,t}function hl(e,t,n,l){return e=At(7,e,l,t),e.lanes=n,e}function mc(e,t,n){return e=At(6,e,null,t),e.lanes=n,e}function hc(e,t,n){return t=At(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var $l=[],Wl=0,uo=null,fo=0,Bt=[],Lt=0,vl=null,fn=1,dn="";function gl(e,t){$l[Wl++]=fo,$l[Wl++]=uo,uo=e,fo=t}function Jf(e,t,n){Bt[Lt++]=fn,Bt[Lt++]=dn,Bt[Lt++]=vl,vl=e;var l=fn;e=dn;var i=32-Et(l)-1;l&=~(1<<i),n+=1;var o=32-Et(t)+i;if(30<o){var d=i-i%5;o=(l&(1<<d)-1).toString(32),l>>=d,i-=d,fn=1<<32-Et(t)+i|n<<i|l,dn=o+e}else fn=1<<o|n<<i|l,dn=e}function vc(e){e.return!==null&&(gl(e,1),Jf(e,1,0))}function gc(e){for(;e===uo;)uo=$l[--Wl],$l[Wl]=null,fo=$l[--Wl],$l[Wl]=null;for(;e===vl;)vl=Bt[--Lt],Bt[Lt]=null,dn=Bt[--Lt],Bt[Lt]=null,fn=Bt[--Lt],Bt[Lt]=null}var vt=null,Ge=null,_e=!1,pl=null,Ft=!1,pc=Error(c(519));function yl(e){var t=Error(c(418,""));throw Pa(Ut(t,e)),pc}function $f(e){var t=e.stateNode,n=e.type,l=e.memoizedProps;switch(t[ft]=e,t[pt]=l,n){case"dialog":Ae("cancel",t),Ae("close",t);break;case"iframe":case"object":case"embed":Ae("load",t);break;case"video":case"audio":for(n=0;n<Si.length;n++)Ae(Si[n],t);break;case"source":Ae("error",t);break;case"img":case"image":case"link":Ae("error",t),Ae("load",t);break;case"details":Ae("toggle",t);break;case"input":Ae("invalid",t),uf(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Pi(t);break;case"select":Ae("invalid",t);break;case"textarea":Ae("invalid",t),df(t,l.value,l.defaultValue,l.children),Pi(t)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||l.suppressHydrationWarning===!0||dh(t.textContent,n)?(l.popover!=null&&(Ae("beforetoggle",t),Ae("toggle",t)),l.onScroll!=null&&Ae("scroll",t),l.onScrollEnd!=null&&Ae("scrollend",t),l.onClick!=null&&(t.onclick=Zo),t=!0):t=!1,t||yl(e)}function Wf(e){for(vt=e.return;vt;)switch(vt.tag){case 5:case 13:Ft=!1;return;case 27:case 3:Ft=!0;return;default:vt=vt.return}}function $a(e){if(e!==vt)return!1;if(!_e)return Wf(e),_e=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Ds(e.type,e.memoizedProps)),n=!n),n&&Ge&&yl(e),Wf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Ge=Xt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Ge=null}}else t===27?(t=Ge,Zn(e.type)?(e=Bs,Bs=null,Ge=e):Ge=t):Ge=vt?Xt(e.stateNode.nextSibling):null;return!0}function Wa(){Ge=vt=null,_e=!1}function Pf(){var e=pl;return e!==null&&(St===null?St=e:St.push.apply(St,e),pl=null),e}function Pa(e){pl===null?pl=[e]:pl.push(e)}var yc=G(null),xl=null,mn=null;function On(e,t,n){W(yc,t._currentValue),t._currentValue=n}function hn(e){e._currentValue=yc.current,F(yc)}function xc(e,t,n){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===n)break;e=e.return}}function bc(e,t,n,l){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var o=i.dependencies;if(o!==null){var d=i.child;o=o.firstContext;e:for(;o!==null;){var p=o;o=i;for(var S=0;S<t.length;S++)if(p.context===t[S]){o.lanes|=n,p=o.alternate,p!==null&&(p.lanes|=n),xc(o.return,n,e),l||(d=null);break e}o=p.next}}else if(i.tag===18){if(d=i.return,d===null)throw Error(c(341));d.lanes|=n,o=d.alternate,o!==null&&(o.lanes|=n),xc(d,n,e),d=null}else d=i.child;if(d!==null)d.return=i;else for(d=i;d!==null;){if(d===e){d=null;break}if(i=d.sibling,i!==null){i.return=d.return,d=i;break}d=d.return}i=d}}function Fa(e,t,n,l){e=null;for(var i=t,o=!1;i!==null;){if(!o){if((i.flags&524288)!==0)o=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var d=i.alternate;if(d===null)throw Error(c(387));if(d=d.memoizedProps,d!==null){var p=i.type;Nt(i.pendingProps.value,d.value)||(e!==null?e.push(p):e=[p])}}else if(i===I.current){if(d=i.alternate,d===null)throw Error(c(387));d.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(Ci):e=[Ci])}i=i.return}e!==null&&bc(t,e,n,l),t.flags|=262144}function mo(e){for(e=e.firstContext;e!==null;){if(!Nt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function bl(e){xl=e,mn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function dt(e){return Ff(xl,e)}function ho(e,t){return xl===null&&bl(e),Ff(e,t)}function Ff(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},mn===null){if(e===null)throw Error(c(308));mn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else mn=mn.next=t;return n}var Oy=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},My=a.unstable_scheduleCallback,Dy=a.unstable_NormalPriority,Ie={$$typeof:B,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Sc(){return{controller:new Oy,data:new Map,refCount:0}}function Ia(e){e.refCount--,e.refCount===0&&My(Dy,function(){e.controller.abort()})}var ei=null,wc=0,Pl=0,Fl=null;function zy(e,t){if(ei===null){var n=ei=[];wc=0,Pl=Ns(),Fl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return wc++,t.then(If,If),t}function If(){if(--wc===0&&ei!==null){Fl!==null&&(Fl.status="fulfilled");var e=ei;ei=null,Pl=0,Fl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Uy(e,t){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var i=0;i<n.length;i++)(0,n[i])(t)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var ed=j.S;j.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&zy(e,t),ed!==null&&ed(e,t)};var Sl=G(null);function Ec(){var e=Sl.current;return e!==null?e:ke.pooledCache}function vo(e,t){t===null?W(Sl,Sl.current):W(Sl,t.pool)}function td(){var e=Ec();return e===null?null:{parent:Ie._currentValue,pool:e}}var ti=Error(c(460)),nd=Error(c(474)),go=Error(c(542)),Nc={then:function(){}};function ld(e){return e=e.status,e==="fulfilled"||e==="rejected"}function po(){}function ad(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(po,po),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,od(e),e;default:if(typeof t.status=="string")t.then(po,po);else{if(e=ke,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=l}},function(l){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,od(e),e}throw ni=t,ti}}var ni=null;function id(){if(ni===null)throw Error(c(459));var e=ni;return ni=null,e}function od(e){if(e===ti||e===go)throw Error(c(483))}var Mn=!1;function Ac(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Tc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Dn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function zn(e,t,n){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Oe&2)!==0){var i=l.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),l.pending=t,t=co(e),Zf(e,null,n),t}return ro(e,l,t,n),co(e)}function li(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,Fu(e,n)}}function Cc(e,t){var n=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var d={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};o===null?i=o=d:o=o.next=d,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:l.shared,callbacks:l.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var jc=!1;function ai(){if(jc){var e=Fl;if(e!==null)throw e}}function ii(e,t,n,l){jc=!1;var i=e.updateQueue;Mn=!1;var o=i.firstBaseUpdate,d=i.lastBaseUpdate,p=i.shared.pending;if(p!==null){i.shared.pending=null;var S=p,D=S.next;S.next=null,d===null?o=D:d.next=D,d=S;var q=e.alternate;q!==null&&(q=q.updateQueue,p=q.lastBaseUpdate,p!==d&&(p===null?q.firstBaseUpdate=D:p.next=D,q.lastBaseUpdate=S))}if(o!==null){var Q=i.baseState;d=0,q=D=S=null,p=o;do{var z=p.lane&-536870913,H=z!==p.lane;if(H?(Ce&z)===z:(l&z)===z){z!==0&&z===Pl&&(jc=!0),q!==null&&(q=q.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var he=e,de=p;z=t;var He=n;switch(de.tag){case 1:if(he=de.payload,typeof he=="function"){Q=he.call(He,Q,z);break e}Q=he;break e;case 3:he.flags=he.flags&-65537|128;case 0:if(he=de.payload,z=typeof he=="function"?he.call(He,Q,z):he,z==null)break e;Q=b({},Q,z);break e;case 2:Mn=!0}}z=p.callback,z!==null&&(e.flags|=64,H&&(e.flags|=8192),H=i.callbacks,H===null?i.callbacks=[z]:H.push(z))}else H={lane:z,tag:p.tag,payload:p.payload,callback:p.callback,next:null},q===null?(D=q=H,S=Q):q=q.next=H,d|=z;if(p=p.next,p===null){if(p=i.shared.pending,p===null)break;H=p,p=H.next,H.next=null,i.lastBaseUpdate=H,i.shared.pending=null}}while(!0);q===null&&(S=Q),i.baseState=S,i.firstBaseUpdate=D,i.lastBaseUpdate=q,o===null&&(i.shared.lanes=0),Yn|=d,e.lanes=d,e.memoizedState=Q}}function rd(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function cd(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)rd(n[e],t)}var Il=G(null),yo=G(0);function sd(e,t){e=Sn,W(yo,e),W(Il,t),Sn=e|t.baseLanes}function _c(){W(yo,Sn),W(Il,Il.current)}function Rc(){Sn=yo.current,F(Il),F(yo)}var Un=0,xe=null,ze=null,We=null,xo=!1,ea=!1,wl=!1,bo=0,oi=0,ta=null,Hy=0;function Ke(){throw Error(c(321))}function Oc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Nt(e[n],t[n]))return!1;return!0}function Mc(e,t,n,l,i,o){return Un=o,xe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,j.H=e===null||e.memoizedState===null?Zd:Kd,wl=!1,o=n(l,i),wl=!1,ea&&(o=fd(t,n,l,i)),ud(e),o}function ud(e){j.H=To;var t=ze!==null&&ze.next!==null;if(Un=0,We=ze=xe=null,xo=!1,oi=0,ta=null,t)throw Error(c(300));e===null||lt||(e=e.dependencies,e!==null&&mo(e)&&(lt=!0))}function fd(e,t,n,l){xe=e;var i=0;do{if(ea&&(ta=null),oi=0,ea=!1,25<=i)throw Error(c(301));if(i+=1,We=ze=null,e.updateQueue!=null){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}j.H=Gy,o=t(n,l)}while(ea);return o}function By(){var e=j.H,t=e.useState()[0];return t=typeof t.then=="function"?ri(t):t,e=e.useState()[0],(ze!==null?ze.memoizedState:null)!==e&&(xe.flags|=1024),t}function Dc(){var e=bo!==0;return bo=0,e}function zc(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Uc(e){if(xo){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}xo=!1}Un=0,We=ze=xe=null,ea=!1,oi=bo=0,ta=null}function xt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return We===null?xe.memoizedState=We=e:We=We.next=e,We}function Pe(){if(ze===null){var e=xe.alternate;e=e!==null?e.memoizedState:null}else e=ze.next;var t=We===null?xe.memoizedState:We.next;if(t!==null)We=t,ze=e;else{if(e===null)throw xe.alternate===null?Error(c(467)):Error(c(310));ze=e,e={memoizedState:ze.memoizedState,baseState:ze.baseState,baseQueue:ze.baseQueue,queue:ze.queue,next:null},We===null?xe.memoizedState=We=e:We=We.next=e}return We}function Hc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ri(e){var t=oi;return oi+=1,ta===null&&(ta=[]),e=ad(ta,e,t),t=xe,(We===null?t.memoizedState:We.next)===null&&(t=t.alternate,j.H=t===null||t.memoizedState===null?Zd:Kd),e}function So(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ri(e);if(e.$$typeof===B)return dt(e)}throw Error(c(438,String(e)))}function Bc(e){var t=null,n=xe.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var l=xe.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Hc(),xe.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),l=0;l<e;l++)n[l]=J;return t.index++,n}function vn(e,t){return typeof t=="function"?t(e):t}function wo(e){var t=Pe();return Lc(t,ze,e)}function Lc(e,t,n){var l=e.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=n;var i=e.baseQueue,o=l.pending;if(o!==null){if(i!==null){var d=i.next;i.next=o.next,o.next=d}t.baseQueue=i=o,l.pending=null}if(o=e.baseState,i===null)e.memoizedState=o;else{t=i.next;var p=d=null,S=null,D=t,q=!1;do{var Q=D.lane&-536870913;if(Q!==D.lane?(Ce&Q)===Q:(Un&Q)===Q){var z=D.revertLane;if(z===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),Q===Pl&&(q=!0);else if((Un&z)===z){D=D.next,z===Pl&&(q=!0);continue}else Q={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},S===null?(p=S=Q,d=o):S=S.next=Q,xe.lanes|=z,Yn|=z;Q=D.action,wl&&n(o,Q),o=D.hasEagerState?D.eagerState:n(o,Q)}else z={lane:Q,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},S===null?(p=S=z,d=o):S=S.next=z,xe.lanes|=Q,Yn|=Q;D=D.next}while(D!==null&&D!==t);if(S===null?d=o:S.next=p,!Nt(o,e.memoizedState)&&(lt=!0,q&&(n=Fl,n!==null)))throw n;e.memoizedState=o,e.baseState=d,e.baseQueue=S,l.lastRenderedState=o}return i===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function kc(e){var t=Pe(),n=t.queue;if(n===null)throw Error(c(311));n.lastRenderedReducer=e;var l=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var d=i=i.next;do o=e(o,d.action),d=d.next;while(d!==i);Nt(o,t.memoizedState)||(lt=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,l]}function dd(e,t,n){var l=xe,i=Pe(),o=_e;if(o){if(n===void 0)throw Error(c(407));n=n()}else n=t();var d=!Nt((ze||i).memoizedState,n);d&&(i.memoizedState=n,lt=!0),i=i.queue;var p=vd.bind(null,l,i,e);if(ci(2048,8,p,[e]),i.getSnapshot!==t||d||We!==null&&We.memoizedState.tag&1){if(l.flags|=2048,na(9,Eo(),hd.bind(null,l,i,n,t),null),ke===null)throw Error(c(349));o||(Un&124)!==0||md(l,t,n)}return n}function md(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=xe.updateQueue,t===null?(t=Hc(),xe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function hd(e,t,n,l){t.value=n,t.getSnapshot=l,gd(t)&&pd(e)}function vd(e,t,n){return n(function(){gd(t)&&pd(e)})}function gd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Nt(e,n)}catch{return!0}}function pd(e){var t=Kl(e,2);t!==null&&Rt(t,e,2)}function qc(e){var t=xt();if(typeof e=="function"){var n=e;if(e=n(),wl){jn(!0);try{n()}finally{jn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:vn,lastRenderedState:e},t}function yd(e,t,n,l){return e.baseState=n,Lc(e,ze,typeof l=="function"?l:vn)}function Ly(e,t,n,l,i){if(Ao(e))throw Error(c(485));if(e=t.action,e!==null){var o={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){o.listeners.push(d)}};j.T!==null?n(!0):o.isTransition=!1,l(o),n=t.pending,n===null?(o.next=t.pending=o,xd(t,o)):(o.next=n.next,t.pending=n.next=o)}}function xd(e,t){var n=t.action,l=t.payload,i=e.state;if(t.isTransition){var o=j.T,d={};j.T=d;try{var p=n(i,l),S=j.S;S!==null&&S(d,p),bd(e,t,p)}catch(D){Vc(e,t,D)}finally{j.T=o}}else try{o=n(i,l),bd(e,t,o)}catch(D){Vc(e,t,D)}}function bd(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Sd(e,t,l)},function(l){return Vc(e,t,l)}):Sd(e,t,n)}function Sd(e,t,n){t.status="fulfilled",t.value=n,wd(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,xd(e,n)))}function Vc(e,t,n){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=n,wd(t),t=t.next;while(t!==l)}e.action=null}function wd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Ed(e,t){return t}function Nd(e,t){if(_e){var n=ke.formState;if(n!==null){e:{var l=xe;if(_e){if(Ge){t:{for(var i=Ge,o=Ft;i.nodeType!==8;){if(!o){i=null;break t}if(i=Xt(i.nextSibling),i===null){i=null;break t}}o=i.data,i=o==="F!"||o==="F"?i:null}if(i){Ge=Xt(i.nextSibling),l=i.data==="F!";break e}}yl(l)}l=!1}l&&(t=n[0])}}return n=xt(),n.memoizedState=n.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ed,lastRenderedState:t},n.queue=l,n=Gd.bind(null,xe,l),l.dispatch=n,l=qc(!1),o=Zc.bind(null,xe,!1,l.queue),l=xt(),i={state:t,dispatch:null,action:e,pending:null},l.queue=i,n=Ly.bind(null,xe,i,o,n),i.dispatch=n,l.memoizedState=e,[t,n,!1]}function Ad(e){var t=Pe();return Td(t,ze,e)}function Td(e,t,n){if(t=Lc(e,t,Ed)[0],e=wo(vn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=ri(t)}catch(d){throw d===ti?go:d}else l=t;t=Pe();var i=t.queue,o=i.dispatch;return n!==t.memoizedState&&(xe.flags|=2048,na(9,Eo(),ky.bind(null,i,n),null)),[l,o,e]}function ky(e,t){e.action=t}function Cd(e){var t=Pe(),n=ze;if(n!==null)return Td(t,n,e);Pe(),t=t.memoizedState,n=Pe();var l=n.queue.dispatch;return n.memoizedState=e,[t,l,!1]}function na(e,t,n,l){return e={tag:e,create:n,deps:l,inst:t,next:null},t=xe.updateQueue,t===null&&(t=Hc(),xe.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(l=n.next,n.next=e,e.next=l,t.lastEffect=e),e}function Eo(){return{destroy:void 0,resource:void 0}}function jd(){return Pe().memoizedState}function No(e,t,n,l){var i=xt();l=l===void 0?null:l,xe.flags|=e,i.memoizedState=na(1|t,Eo(),n,l)}function ci(e,t,n,l){var i=Pe();l=l===void 0?null:l;var o=i.memoizedState.inst;ze!==null&&l!==null&&Oc(l,ze.memoizedState.deps)?i.memoizedState=na(t,o,n,l):(xe.flags|=e,i.memoizedState=na(1|t,o,n,l))}function _d(e,t){No(8390656,8,e,t)}function Rd(e,t){ci(2048,8,e,t)}function Od(e,t){return ci(4,2,e,t)}function Md(e,t){return ci(4,4,e,t)}function Dd(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function zd(e,t,n){n=n!=null?n.concat([e]):null,ci(4,4,Dd.bind(null,t,e),n)}function Yc(){}function Ud(e,t){var n=Pe();t=t===void 0?null:t;var l=n.memoizedState;return t!==null&&Oc(t,l[1])?l[0]:(n.memoizedState=[e,t],e)}function Hd(e,t){var n=Pe();t=t===void 0?null:t;var l=n.memoizedState;if(t!==null&&Oc(t,l[1]))return l[0];if(l=e(),wl){jn(!0);try{e()}finally{jn(!1)}}return n.memoizedState=[l,t],l}function Gc(e,t,n){return n===void 0||(Un&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=km(),xe.lanes|=e,Yn|=e,n)}function Bd(e,t,n,l){return Nt(n,t)?n:Il.current!==null?(e=Gc(e,n,l),Nt(e,t)||(lt=!0),e):(Un&42)===0?(lt=!0,e.memoizedState=n):(e=km(),xe.lanes|=e,Yn|=e,t)}function Ld(e,t,n,l,i){var o=Z.p;Z.p=o!==0&&8>o?o:8;var d=j.T,p={};j.T=p,Zc(e,!1,t,n);try{var S=i(),D=j.S;if(D!==null&&D(p,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var q=Uy(S,l);si(e,t,q,_t(e))}else si(e,t,l,_t(e))}catch(Q){si(e,t,{then:function(){},status:"rejected",reason:Q},_t())}finally{Z.p=o,j.T=d}}function qy(){}function Xc(e,t,n,l){if(e.tag!==5)throw Error(c(476));var i=kd(e).queue;Ld(e,i,t,L,n===null?qy:function(){return qd(e),n(l)})}function kd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:L,baseState:L,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:vn,lastRenderedState:L},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:vn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function qd(e){var t=kd(e).next.queue;si(e,t,{},_t())}function Qc(){return dt(Ci)}function Vd(){return Pe().memoizedState}function Yd(){return Pe().memoizedState}function Vy(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=_t();e=Dn(n);var l=zn(t,e,n);l!==null&&(Rt(l,t,n),li(l,t,n)),t={cache:Sc()},e.payload=t;return}t=t.return}}function Yy(e,t,n){var l=_t();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ao(e)?Xd(t,n):(n=fc(e,t,n,l),n!==null&&(Rt(n,e,l),Qd(n,t,l)))}function Gd(e,t,n){var l=_t();si(e,t,n,l)}function si(e,t,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ao(e))Xd(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var d=t.lastRenderedState,p=o(d,n);if(i.hasEagerState=!0,i.eagerState=p,Nt(p,d))return ro(e,t,i,0),ke===null&&oo(),!1}catch{}finally{}if(n=fc(e,t,i,l),n!==null)return Rt(n,e,l),Qd(n,t,l),!0}return!1}function Zc(e,t,n,l){if(l={lane:2,revertLane:Ns(),action:l,hasEagerState:!1,eagerState:null,next:null},Ao(e)){if(t)throw Error(c(479))}else t=fc(e,n,l,2),t!==null&&Rt(t,e,2)}function Ao(e){var t=e.alternate;return e===xe||t!==null&&t===xe}function Xd(e,t){ea=xo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Qd(e,t,n){if((n&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,Fu(e,n)}}var To={readContext:dt,use:So,useCallback:Ke,useContext:Ke,useEffect:Ke,useImperativeHandle:Ke,useLayoutEffect:Ke,useInsertionEffect:Ke,useMemo:Ke,useReducer:Ke,useRef:Ke,useState:Ke,useDebugValue:Ke,useDeferredValue:Ke,useTransition:Ke,useSyncExternalStore:Ke,useId:Ke,useHostTransitionStatus:Ke,useFormState:Ke,useActionState:Ke,useOptimistic:Ke,useMemoCache:Ke,useCacheRefresh:Ke},Zd={readContext:dt,use:So,useCallback:function(e,t){return xt().memoizedState=[e,t===void 0?null:t],e},useContext:dt,useEffect:_d,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,No(4194308,4,Dd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return No(4194308,4,e,t)},useInsertionEffect:function(e,t){No(4,2,e,t)},useMemo:function(e,t){var n=xt();t=t===void 0?null:t;var l=e();if(wl){jn(!0);try{e()}finally{jn(!1)}}return n.memoizedState=[l,t],l},useReducer:function(e,t,n){var l=xt();if(n!==void 0){var i=n(t);if(wl){jn(!0);try{n(t)}finally{jn(!1)}}}else i=t;return l.memoizedState=l.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},l.queue=e,e=e.dispatch=Yy.bind(null,xe,e),[l.memoizedState,e]},useRef:function(e){var t=xt();return e={current:e},t.memoizedState=e},useState:function(e){e=qc(e);var t=e.queue,n=Gd.bind(null,xe,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Yc,useDeferredValue:function(e,t){var n=xt();return Gc(n,e,t)},useTransition:function(){var e=qc(!1);return e=Ld.bind(null,xe,e.queue,!0,!1),xt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var l=xe,i=xt();if(_e){if(n===void 0)throw Error(c(407));n=n()}else{if(n=t(),ke===null)throw Error(c(349));(Ce&124)!==0||md(l,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,_d(vd.bind(null,l,o,e),[e]),l.flags|=2048,na(9,Eo(),hd.bind(null,l,o,n,t),null),n},useId:function(){var e=xt(),t=ke.identifierPrefix;if(_e){var n=dn,l=fn;n=(l&~(1<<32-Et(l)-1)).toString(32)+n,t="«"+t+"R"+n,n=bo++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=Hy++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Qc,useFormState:Nd,useActionState:Nd,useOptimistic:function(e){var t=xt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Zc.bind(null,xe,!0,n),n.dispatch=t,[e,t]},useMemoCache:Bc,useCacheRefresh:function(){return xt().memoizedState=Vy.bind(null,xe)}},Kd={readContext:dt,use:So,useCallback:Ud,useContext:dt,useEffect:Rd,useImperativeHandle:zd,useInsertionEffect:Od,useLayoutEffect:Md,useMemo:Hd,useReducer:wo,useRef:jd,useState:function(){return wo(vn)},useDebugValue:Yc,useDeferredValue:function(e,t){var n=Pe();return Bd(n,ze.memoizedState,e,t)},useTransition:function(){var e=wo(vn)[0],t=Pe().memoizedState;return[typeof e=="boolean"?e:ri(e),t]},useSyncExternalStore:dd,useId:Vd,useHostTransitionStatus:Qc,useFormState:Ad,useActionState:Ad,useOptimistic:function(e,t){var n=Pe();return yd(n,ze,e,t)},useMemoCache:Bc,useCacheRefresh:Yd},Gy={readContext:dt,use:So,useCallback:Ud,useContext:dt,useEffect:Rd,useImperativeHandle:zd,useInsertionEffect:Od,useLayoutEffect:Md,useMemo:Hd,useReducer:kc,useRef:jd,useState:function(){return kc(vn)},useDebugValue:Yc,useDeferredValue:function(e,t){var n=Pe();return ze===null?Gc(n,e,t):Bd(n,ze.memoizedState,e,t)},useTransition:function(){var e=kc(vn)[0],t=Pe().memoizedState;return[typeof e=="boolean"?e:ri(e),t]},useSyncExternalStore:dd,useId:Vd,useHostTransitionStatus:Qc,useFormState:Cd,useActionState:Cd,useOptimistic:function(e,t){var n=Pe();return ze!==null?yd(n,ze,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Bc,useCacheRefresh:Yd},la=null,ui=0;function Co(e){var t=ui;return ui+=1,la===null&&(la=[]),ad(la,e,t)}function fi(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function jo(e,t){throw t.$$typeof===N?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Jd(e){var t=e._init;return t(e._payload)}function $d(e){function t(T,A){if(e){var O=T.deletions;O===null?(T.deletions=[A],T.flags|=16):O.push(A)}}function n(T,A){if(!e)return null;for(;A!==null;)t(T,A),A=A.sibling;return null}function l(T){for(var A=new Map;T!==null;)T.key!==null?A.set(T.key,T):A.set(T.index,T),T=T.sibling;return A}function i(T,A){return T=un(T,A),T.index=0,T.sibling=null,T}function o(T,A,O){return T.index=O,e?(O=T.alternate,O!==null?(O=O.index,O<A?(T.flags|=67108866,A):O):(T.flags|=67108866,A)):(T.flags|=1048576,A)}function d(T){return e&&T.alternate===null&&(T.flags|=67108866),T}function p(T,A,O,X){return A===null||A.tag!==6?(A=mc(O,T.mode,X),A.return=T,A):(A=i(A,O),A.return=T,A)}function S(T,A,O,X){var ne=O.type;return ne===M?q(T,A,O.props.children,X,O.key):A!==null&&(A.elementType===ne||typeof ne=="object"&&ne!==null&&ne.$$typeof===$&&Jd(ne)===A.type)?(A=i(A,O.props),fi(A,O),A.return=T,A):(A=so(O.type,O.key,O.props,null,T.mode,X),fi(A,O),A.return=T,A)}function D(T,A,O,X){return A===null||A.tag!==4||A.stateNode.containerInfo!==O.containerInfo||A.stateNode.implementation!==O.implementation?(A=hc(O,T.mode,X),A.return=T,A):(A=i(A,O.children||[]),A.return=T,A)}function q(T,A,O,X,ne){return A===null||A.tag!==7?(A=hl(O,T.mode,X,ne),A.return=T,A):(A=i(A,O),A.return=T,A)}function Q(T,A,O){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return A=mc(""+A,T.mode,O),A.return=T,A;if(typeof A=="object"&&A!==null){switch(A.$$typeof){case C:return O=so(A.type,A.key,A.props,null,T.mode,O),fi(O,A),O.return=T,O;case R:return A=hc(A,T.mode,O),A.return=T,A;case $:var X=A._init;return A=X(A._payload),Q(T,A,O)}if(Y(A)||re(A))return A=hl(A,T.mode,O,null),A.return=T,A;if(typeof A.then=="function")return Q(T,Co(A),O);if(A.$$typeof===B)return Q(T,ho(T,A),O);jo(T,A)}return null}function z(T,A,O,X){var ne=A!==null?A.key:null;if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return ne!==null?null:p(T,A,""+O,X);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case C:return O.key===ne?S(T,A,O,X):null;case R:return O.key===ne?D(T,A,O,X):null;case $:return ne=O._init,O=ne(O._payload),z(T,A,O,X)}if(Y(O)||re(O))return ne!==null?null:q(T,A,O,X,null);if(typeof O.then=="function")return z(T,A,Co(O),X);if(O.$$typeof===B)return z(T,A,ho(T,O),X);jo(T,O)}return null}function H(T,A,O,X,ne){if(typeof X=="string"&&X!==""||typeof X=="number"||typeof X=="bigint")return T=T.get(O)||null,p(A,T,""+X,ne);if(typeof X=="object"&&X!==null){switch(X.$$typeof){case C:return T=T.get(X.key===null?O:X.key)||null,S(A,T,X,ne);case R:return T=T.get(X.key===null?O:X.key)||null,D(A,T,X,ne);case $:var Se=X._init;return X=Se(X._payload),H(T,A,O,X,ne)}if(Y(X)||re(X))return T=T.get(O)||null,q(A,T,X,ne,null);if(typeof X.then=="function")return H(T,A,O,Co(X),ne);if(X.$$typeof===B)return H(T,A,O,ho(A,X),ne);jo(A,X)}return null}function he(T,A,O,X){for(var ne=null,Se=null,oe=A,me=A=0,it=null;oe!==null&&me<O.length;me++){oe.index>me?(it=oe,oe=null):it=oe.sibling;var je=z(T,oe,O[me],X);if(je===null){oe===null&&(oe=it);break}e&&oe&&je.alternate===null&&t(T,oe),A=o(je,A,me),Se===null?ne=je:Se.sibling=je,Se=je,oe=it}if(me===O.length)return n(T,oe),_e&&gl(T,me),ne;if(oe===null){for(;me<O.length;me++)oe=Q(T,O[me],X),oe!==null&&(A=o(oe,A,me),Se===null?ne=oe:Se.sibling=oe,Se=oe);return _e&&gl(T,me),ne}for(oe=l(oe);me<O.length;me++)it=H(oe,T,me,O[me],X),it!==null&&(e&&it.alternate!==null&&oe.delete(it.key===null?me:it.key),A=o(it,A,me),Se===null?ne=it:Se.sibling=it,Se=it);return e&&oe.forEach(function(Pn){return t(T,Pn)}),_e&&gl(T,me),ne}function de(T,A,O,X){if(O==null)throw Error(c(151));for(var ne=null,Se=null,oe=A,me=A=0,it=null,je=O.next();oe!==null&&!je.done;me++,je=O.next()){oe.index>me?(it=oe,oe=null):it=oe.sibling;var Pn=z(T,oe,je.value,X);if(Pn===null){oe===null&&(oe=it);break}e&&oe&&Pn.alternate===null&&t(T,oe),A=o(Pn,A,me),Se===null?ne=Pn:Se.sibling=Pn,Se=Pn,oe=it}if(je.done)return n(T,oe),_e&&gl(T,me),ne;if(oe===null){for(;!je.done;me++,je=O.next())je=Q(T,je.value,X),je!==null&&(A=o(je,A,me),Se===null?ne=je:Se.sibling=je,Se=je);return _e&&gl(T,me),ne}for(oe=l(oe);!je.done;me++,je=O.next())je=H(oe,T,me,je.value,X),je!==null&&(e&&je.alternate!==null&&oe.delete(je.key===null?me:je.key),A=o(je,A,me),Se===null?ne=je:Se.sibling=je,Se=je);return e&&oe.forEach(function(X0){return t(T,X0)}),_e&&gl(T,me),ne}function He(T,A,O,X){if(typeof O=="object"&&O!==null&&O.type===M&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case C:e:{for(var ne=O.key;A!==null;){if(A.key===ne){if(ne=O.type,ne===M){if(A.tag===7){n(T,A.sibling),X=i(A,O.props.children),X.return=T,T=X;break e}}else if(A.elementType===ne||typeof ne=="object"&&ne!==null&&ne.$$typeof===$&&Jd(ne)===A.type){n(T,A.sibling),X=i(A,O.props),fi(X,O),X.return=T,T=X;break e}n(T,A);break}else t(T,A);A=A.sibling}O.type===M?(X=hl(O.props.children,T.mode,X,O.key),X.return=T,T=X):(X=so(O.type,O.key,O.props,null,T.mode,X),fi(X,O),X.return=T,T=X)}return d(T);case R:e:{for(ne=O.key;A!==null;){if(A.key===ne)if(A.tag===4&&A.stateNode.containerInfo===O.containerInfo&&A.stateNode.implementation===O.implementation){n(T,A.sibling),X=i(A,O.children||[]),X.return=T,T=X;break e}else{n(T,A);break}else t(T,A);A=A.sibling}X=hc(O,T.mode,X),X.return=T,T=X}return d(T);case $:return ne=O._init,O=ne(O._payload),He(T,A,O,X)}if(Y(O))return he(T,A,O,X);if(re(O)){if(ne=re(O),typeof ne!="function")throw Error(c(150));return O=ne.call(O),de(T,A,O,X)}if(typeof O.then=="function")return He(T,A,Co(O),X);if(O.$$typeof===B)return He(T,A,ho(T,O),X);jo(T,O)}return typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint"?(O=""+O,A!==null&&A.tag===6?(n(T,A.sibling),X=i(A,O),X.return=T,T=X):(n(T,A),X=mc(O,T.mode,X),X.return=T,T=X),d(T)):n(T,A)}return function(T,A,O,X){try{ui=0;var ne=He(T,A,O,X);return la=null,ne}catch(oe){if(oe===ti||oe===go)throw oe;var Se=At(29,oe,null,T.mode);return Se.lanes=X,Se.return=T,Se}finally{}}}var aa=$d(!0),Wd=$d(!1),kt=G(null),It=null;function Hn(e){var t=e.alternate;W(et,et.current&1),W(kt,e),It===null&&(t===null||Il.current!==null||t.memoizedState!==null)&&(It=e)}function Pd(e){if(e.tag===22){if(W(et,et.current),W(kt,e),It===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(It=e)}}else Bn()}function Bn(){W(et,et.current),W(kt,kt.current)}function gn(e){F(kt),It===e&&(It=null),F(et)}var et=G(0);function _o(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Hs(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Kc(e,t,n,l){t=e.memoizedState,n=n(l,t),n=n==null?t:b({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Jc={enqueueSetState:function(e,t,n){e=e._reactInternals;var l=_t(),i=Dn(l);i.payload=t,n!=null&&(i.callback=n),t=zn(e,i,l),t!==null&&(Rt(t,e,l),li(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var l=_t(),i=Dn(l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=zn(e,i,l),t!==null&&(Rt(t,e,l),li(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=_t(),l=Dn(n);l.tag=2,t!=null&&(l.callback=t),t=zn(e,l,n),t!==null&&(Rt(t,e,n),li(t,e,n))}};function Fd(e,t,n,l,i,o,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,o,d):t.prototype&&t.prototype.isPureReactComponent?!Ka(n,l)||!Ka(i,o):!0}function Id(e,t,n,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,l),t.state!==e&&Jc.enqueueReplaceState(t,t.state,null)}function El(e,t){var n=t;if("ref"in t){n={};for(var l in t)l!=="ref"&&(n[l]=t[l])}if(e=e.defaultProps){n===t&&(n=b({},n));for(var i in e)n[i]===void 0&&(n[i]=e[i])}return n}var Ro=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function em(e){Ro(e)}function tm(e){console.error(e)}function nm(e){Ro(e)}function Oo(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function lm(e,t,n){try{var l=e.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function $c(e,t,n){return n=Dn(n),n.tag=3,n.payload={element:null},n.callback=function(){Oo(e,t)},n}function am(e){return e=Dn(e),e.tag=3,e}function im(e,t,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var o=l.value;e.payload=function(){return i(o)},e.callback=function(){lm(t,n,l)}}var d=n.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(e.callback=function(){lm(t,n,l),typeof i!="function"&&(Gn===null?Gn=new Set([this]):Gn.add(this));var p=l.stack;this.componentDidCatch(l.value,{componentStack:p!==null?p:""})})}function Xy(e,t,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=n.alternate,t!==null&&Fa(t,n,i,!0),n=kt.current,n!==null){switch(n.tag){case 13:return It===null?xs():n.alternate===null&&Xe===0&&(Xe=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===Nc?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([l]):t.add(l),Ss(e,l,i)),!1;case 22:return n.flags|=65536,l===Nc?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([l]):n.add(l)),Ss(e,l,i)),!1}throw Error(c(435,n.tag))}return Ss(e,l,i),xs(),!1}if(_e)return t=kt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,l!==pc&&(e=Error(c(422),{cause:l}),Pa(Ut(e,n)))):(l!==pc&&(t=Error(c(423),{cause:l}),Pa(Ut(t,n))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,l=Ut(l,n),i=$c(e.stateNode,l,i),Cc(e,i),Xe!==4&&(Xe=2)),!1;var o=Error(c(520),{cause:l});if(o=Ut(o,n),yi===null?yi=[o]:yi.push(o),Xe!==4&&(Xe=2),t===null)return!0;l=Ut(l,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,e=$c(n.stateNode,l,e),Cc(n,e),!1;case 1:if(t=n.type,o=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(Gn===null||!Gn.has(o))))return n.flags|=65536,i&=-i,n.lanes|=i,i=am(i),im(i,e,n,l),Cc(n,i),!1}n=n.return}while(n!==null);return!1}var om=Error(c(461)),lt=!1;function rt(e,t,n,l){t.child=e===null?Wd(t,null,n,l):aa(t,e.child,n,l)}function rm(e,t,n,l,i){n=n.render;var o=t.ref;if("ref"in l){var d={};for(var p in l)p!=="ref"&&(d[p]=l[p])}else d=l;return bl(t),l=Mc(e,t,n,d,o,i),p=Dc(),e!==null&&!lt?(zc(e,t,i),pn(e,t,i)):(_e&&p&&vc(t),t.flags|=1,rt(e,t,l,i),t.child)}function cm(e,t,n,l,i){if(e===null){var o=n.type;return typeof o=="function"&&!dc(o)&&o.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=o,sm(e,t,o,l,i)):(e=so(n.type,null,l,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!ls(e,i)){var d=o.memoizedProps;if(n=n.compare,n=n!==null?n:Ka,n(d,l)&&e.ref===t.ref)return pn(e,t,i)}return t.flags|=1,e=un(o,l),e.ref=t.ref,e.return=t,t.child=e}function sm(e,t,n,l,i){if(e!==null){var o=e.memoizedProps;if(Ka(o,l)&&e.ref===t.ref)if(lt=!1,t.pendingProps=l=o,ls(e,i))(e.flags&131072)!==0&&(lt=!0);else return t.lanes=e.lanes,pn(e,t,i)}return Wc(e,t,n,l,i)}function um(e,t,n){var l=t.pendingProps,i=l.children,o=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=o!==null?o.baseLanes|n:n,e!==null){for(i=t.child=e.child,o=0;i!==null;)o=o|i.lanes|i.childLanes,i=i.sibling;t.childLanes=o&~l}else t.childLanes=0,t.child=null;return fm(e,t,l,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&vo(t,o!==null?o.cachePool:null),o!==null?sd(t,o):_c(),Pd(t);else return t.lanes=t.childLanes=536870912,fm(e,t,o!==null?o.baseLanes|n:n,n)}else o!==null?(vo(t,o.cachePool),sd(t,o),Bn(),t.memoizedState=null):(e!==null&&vo(t,null),_c(),Bn());return rt(e,t,i,n),t.child}function fm(e,t,n,l){var i=Ec();return i=i===null?null:{parent:Ie._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},e!==null&&vo(t,null),_c(),Pd(t),e!==null&&Fa(e,t,l,!0),null}function Mo(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(c(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function Wc(e,t,n,l,i){return bl(t),n=Mc(e,t,n,l,void 0,i),l=Dc(),e!==null&&!lt?(zc(e,t,i),pn(e,t,i)):(_e&&l&&vc(t),t.flags|=1,rt(e,t,n,i),t.child)}function dm(e,t,n,l,i,o){return bl(t),t.updateQueue=null,n=fd(t,l,n,i),ud(e),l=Dc(),e!==null&&!lt?(zc(e,t,o),pn(e,t,o)):(_e&&l&&vc(t),t.flags|=1,rt(e,t,n,o),t.child)}function mm(e,t,n,l,i){if(bl(t),t.stateNode===null){var o=Jl,d=n.contextType;typeof d=="object"&&d!==null&&(o=dt(d)),o=new n(l,o),t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=Jc,t.stateNode=o,o._reactInternals=t,o=t.stateNode,o.props=l,o.state=t.memoizedState,o.refs={},Ac(t),d=n.contextType,o.context=typeof d=="object"&&d!==null?dt(d):Jl,o.state=t.memoizedState,d=n.getDerivedStateFromProps,typeof d=="function"&&(Kc(t,n,d,l),o.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(d=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),d!==o.state&&Jc.enqueueReplaceState(o,o.state,null),ii(t,l,o,i),ai(),o.state=t.memoizedState),typeof o.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){o=t.stateNode;var p=t.memoizedProps,S=El(n,p);o.props=S;var D=o.context,q=n.contextType;d=Jl,typeof q=="object"&&q!==null&&(d=dt(q));var Q=n.getDerivedStateFromProps;q=typeof Q=="function"||typeof o.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,q||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(p||D!==d)&&Id(t,o,l,d),Mn=!1;var z=t.memoizedState;o.state=z,ii(t,l,o,i),ai(),D=t.memoizedState,p||z!==D||Mn?(typeof Q=="function"&&(Kc(t,n,Q,l),D=t.memoizedState),(S=Mn||Fd(t,n,S,l,z,D,d))?(q||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=D),o.props=l,o.state=D,o.context=d,l=S):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{o=t.stateNode,Tc(e,t),d=t.memoizedProps,q=El(n,d),o.props=q,Q=t.pendingProps,z=o.context,D=n.contextType,S=Jl,typeof D=="object"&&D!==null&&(S=dt(D)),p=n.getDerivedStateFromProps,(D=typeof p=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(d!==Q||z!==S)&&Id(t,o,l,S),Mn=!1,z=t.memoizedState,o.state=z,ii(t,l,o,i),ai();var H=t.memoizedState;d!==Q||z!==H||Mn||e!==null&&e.dependencies!==null&&mo(e.dependencies)?(typeof p=="function"&&(Kc(t,n,p,l),H=t.memoizedState),(q=Mn||Fd(t,n,q,l,z,H,S)||e!==null&&e.dependencies!==null&&mo(e.dependencies))?(D||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(l,H,S),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(l,H,S)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||d===e.memoizedProps&&z===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&z===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=H),o.props=l,o.state=H,o.context=S,l=q):(typeof o.componentDidUpdate!="function"||d===e.memoizedProps&&z===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&z===e.memoizedState||(t.flags|=1024),l=!1)}return o=l,Mo(e,t),l=(t.flags&128)!==0,o||l?(o=t.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:o.render(),t.flags|=1,e!==null&&l?(t.child=aa(t,e.child,null,i),t.child=aa(t,null,n,i)):rt(e,t,n,i),t.memoizedState=o.state,e=t.child):e=pn(e,t,i),e}function hm(e,t,n,l){return Wa(),t.flags|=256,rt(e,t,n,l),t.child}var Pc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Fc(e){return{baseLanes:e,cachePool:td()}}function Ic(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=qt),e}function vm(e,t,n){var l=t.pendingProps,i=!1,o=(t.flags&128)!==0,d;if((d=o)||(d=e!==null&&e.memoizedState===null?!1:(et.current&2)!==0),d&&(i=!0,t.flags&=-129),d=(t.flags&32)!==0,t.flags&=-33,e===null){if(_e){if(i?Hn(t):Bn(),_e){var p=Ge,S;if(S=p){e:{for(S=p,p=Ft;S.nodeType!==8;){if(!p){p=null;break e}if(S=Xt(S.nextSibling),S===null){p=null;break e}}p=S}p!==null?(t.memoizedState={dehydrated:p,treeContext:vl!==null?{id:fn,overflow:dn}:null,retryLane:536870912,hydrationErrors:null},S=At(18,null,null,0),S.stateNode=p,S.return=t,t.child=S,vt=t,Ge=null,S=!0):S=!1}S||yl(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return Hs(p)?t.lanes=32:t.lanes=536870912,null;gn(t)}return p=l.children,l=l.fallback,i?(Bn(),i=t.mode,p=Do({mode:"hidden",children:p},i),l=hl(l,i,n,null),p.return=t,l.return=t,p.sibling=l,t.child=p,i=t.child,i.memoizedState=Fc(n),i.childLanes=Ic(e,d,n),t.memoizedState=Pc,l):(Hn(t),es(t,p))}if(S=e.memoizedState,S!==null&&(p=S.dehydrated,p!==null)){if(o)t.flags&256?(Hn(t),t.flags&=-257,t=ts(e,t,n)):t.memoizedState!==null?(Bn(),t.child=e.child,t.flags|=128,t=null):(Bn(),i=l.fallback,p=t.mode,l=Do({mode:"visible",children:l.children},p),i=hl(i,p,n,null),i.flags|=2,l.return=t,i.return=t,l.sibling=i,t.child=l,aa(t,e.child,null,n),l=t.child,l.memoizedState=Fc(n),l.childLanes=Ic(e,d,n),t.memoizedState=Pc,t=i);else if(Hn(t),Hs(p)){if(d=p.nextSibling&&p.nextSibling.dataset,d)var D=d.dgst;d=D,l=Error(c(419)),l.stack="",l.digest=d,Pa({value:l,source:null,stack:null}),t=ts(e,t,n)}else if(lt||Fa(e,t,n,!1),d=(n&e.childLanes)!==0,lt||d){if(d=ke,d!==null&&(l=n&-n,l=(l&42)!==0?1:Br(l),l=(l&(d.suspendedLanes|n))!==0?0:l,l!==0&&l!==S.retryLane))throw S.retryLane=l,Kl(e,l),Rt(d,e,l),om;p.data==="$?"||xs(),t=ts(e,t,n)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=S.treeContext,Ge=Xt(p.nextSibling),vt=t,_e=!0,pl=null,Ft=!1,e!==null&&(Bt[Lt++]=fn,Bt[Lt++]=dn,Bt[Lt++]=vl,fn=e.id,dn=e.overflow,vl=t),t=es(t,l.children),t.flags|=4096);return t}return i?(Bn(),i=l.fallback,p=t.mode,S=e.child,D=S.sibling,l=un(S,{mode:"hidden",children:l.children}),l.subtreeFlags=S.subtreeFlags&65011712,D!==null?i=un(D,i):(i=hl(i,p,n,null),i.flags|=2),i.return=t,l.return=t,l.sibling=i,t.child=l,l=i,i=t.child,p=e.child.memoizedState,p===null?p=Fc(n):(S=p.cachePool,S!==null?(D=Ie._currentValue,S=S.parent!==D?{parent:D,pool:D}:S):S=td(),p={baseLanes:p.baseLanes|n,cachePool:S}),i.memoizedState=p,i.childLanes=Ic(e,d,n),t.memoizedState=Pc,l):(Hn(t),n=e.child,e=n.sibling,n=un(n,{mode:"visible",children:l.children}),n.return=t,n.sibling=null,e!==null&&(d=t.deletions,d===null?(t.deletions=[e],t.flags|=16):d.push(e)),t.child=n,t.memoizedState=null,n)}function es(e,t){return t=Do({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Do(e,t){return e=At(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function ts(e,t,n){return aa(t,e.child,null,n),e=es(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function gm(e,t,n){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),xc(e.return,t,n)}function ns(e,t,n,l,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=l,o.tail=n,o.tailMode=i)}function pm(e,t,n){var l=t.pendingProps,i=l.revealOrder,o=l.tail;if(rt(e,t,l.children,n),l=et.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&gm(e,n,t);else if(e.tag===19)gm(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(W(et,l),i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&_o(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),ns(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&_o(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}ns(t,!0,n,null,o);break;case"together":ns(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function pn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Yn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Fa(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,n=un(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=un(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function ls(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&mo(e)))}function Qy(e,t,n){switch(t.tag){case 3:fe(t,t.stateNode.containerInfo),On(t,Ie,e.memoizedState.cache),Wa();break;case 27:case 5:Te(t);break;case 4:fe(t,t.stateNode.containerInfo);break;case 10:On(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Hn(t),t.flags|=128,null):(n&t.child.childLanes)!==0?vm(e,t,n):(Hn(t),e=pn(e,t,n),e!==null?e.sibling:null);Hn(t);break;case 19:var i=(e.flags&128)!==0;if(l=(n&t.childLanes)!==0,l||(Fa(e,t,n,!1),l=(n&t.childLanes)!==0),i){if(l)return pm(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),W(et,et.current),l)break;return null;case 22:case 23:return t.lanes=0,um(e,t,n);case 24:On(t,Ie,e.memoizedState.cache)}return pn(e,t,n)}function ym(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)lt=!0;else{if(!ls(e,n)&&(t.flags&128)===0)return lt=!1,Qy(e,t,n);lt=(e.flags&131072)!==0}else lt=!1,_e&&(t.flags&1048576)!==0&&Jf(t,fo,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,i=l._init;if(l=i(l._payload),t.type=l,typeof l=="function")dc(l)?(e=El(l,e),t.tag=1,t=mm(null,t,l,e,n)):(t.tag=0,t=Wc(null,t,l,e,n));else{if(l!=null){if(i=l.$$typeof,i===K){t.tag=11,t=rm(null,t,l,e,n);break e}else if(i===P){t.tag=14,t=cm(null,t,l,e,n);break e}}throw t=pe(l)||l,Error(c(306,t,""))}}return t;case 0:return Wc(e,t,t.type,t.pendingProps,n);case 1:return l=t.type,i=El(l,t.pendingProps),mm(e,t,l,i,n);case 3:e:{if(fe(t,t.stateNode.containerInfo),e===null)throw Error(c(387));l=t.pendingProps;var o=t.memoizedState;i=o.element,Tc(e,t),ii(t,l,null,n);var d=t.memoizedState;if(l=d.cache,On(t,Ie,l),l!==o.cache&&bc(t,[Ie],n,!0),ai(),l=d.element,o.isDehydrated)if(o={element:l,isDehydrated:!1,cache:d.cache},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){t=hm(e,t,l,n);break e}else if(l!==i){i=Ut(Error(c(424)),t),Pa(i),t=hm(e,t,l,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ge=Xt(e.firstChild),vt=t,_e=!0,pl=null,Ft=!0,n=Wd(t,null,l,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Wa(),l===i){t=pn(e,t,n);break e}rt(e,t,l,n)}t=t.child}return t;case 26:return Mo(e,t),e===null?(n=wh(t.type,null,t.pendingProps,null))?t.memoizedState=n:_e||(n=t.type,e=t.pendingProps,l=Ko(ce.current).createElement(n),l[ft]=t,l[pt]=e,st(l,n,e),nt(l),t.stateNode=l):t.memoizedState=wh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Te(t),e===null&&_e&&(l=t.stateNode=xh(t.type,t.pendingProps,ce.current),vt=t,Ft=!0,i=Ge,Zn(t.type)?(Bs=i,Ge=Xt(l.firstChild)):Ge=i),rt(e,t,t.pendingProps.children,n),Mo(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&_e&&((i=l=Ge)&&(l=x0(l,t.type,t.pendingProps,Ft),l!==null?(t.stateNode=l,vt=t,Ge=Xt(l.firstChild),Ft=!1,i=!0):i=!1),i||yl(t)),Te(t),i=t.type,o=t.pendingProps,d=e!==null?e.memoizedProps:null,l=o.children,Ds(i,o)?l=null:d!==null&&Ds(i,d)&&(t.flags|=32),t.memoizedState!==null&&(i=Mc(e,t,By,null,null,n),Ci._currentValue=i),Mo(e,t),rt(e,t,l,n),t.child;case 6:return e===null&&_e&&((e=n=Ge)&&(n=b0(n,t.pendingProps,Ft),n!==null?(t.stateNode=n,vt=t,Ge=null,e=!0):e=!1),e||yl(t)),null;case 13:return vm(e,t,n);case 4:return fe(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=aa(t,null,l,n):rt(e,t,l,n),t.child;case 11:return rm(e,t,t.type,t.pendingProps,n);case 7:return rt(e,t,t.pendingProps,n),t.child;case 8:return rt(e,t,t.pendingProps.children,n),t.child;case 12:return rt(e,t,t.pendingProps.children,n),t.child;case 10:return l=t.pendingProps,On(t,t.type,l.value),rt(e,t,l.children,n),t.child;case 9:return i=t.type._context,l=t.pendingProps.children,bl(t),i=dt(i),l=l(i),t.flags|=1,rt(e,t,l,n),t.child;case 14:return cm(e,t,t.type,t.pendingProps,n);case 15:return sm(e,t,t.type,t.pendingProps,n);case 19:return pm(e,t,n);case 31:return l=t.pendingProps,n=t.mode,l={mode:l.mode,children:l.children},e===null?(n=Do(l,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=un(e.child,l),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return um(e,t,n);case 24:return bl(t),l=dt(Ie),e===null?(i=Ec(),i===null&&(i=ke,o=Sc(),i.pooledCache=o,o.refCount++,o!==null&&(i.pooledCacheLanes|=n),i=o),t.memoizedState={parent:l,cache:i},Ac(t),On(t,Ie,i)):((e.lanes&n)!==0&&(Tc(e,t),ii(t,null,null,n),ai()),i=e.memoizedState,o=t.memoizedState,i.parent!==l?(i={parent:l,cache:l},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),On(t,Ie,l)):(l=o.cache,On(t,Ie,l),l!==i.cache&&bc(t,[Ie],n,!0))),rt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function yn(e){e.flags|=4}function xm(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Ch(t)){if(t=kt.current,t!==null&&((Ce&4194048)===Ce?It!==null:(Ce&62914560)!==Ce&&(Ce&536870912)===0||t!==It))throw ni=Nc,nd;e.flags|=8192}}function zo(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Wu():536870912,e.lanes|=t,ca|=t)}function di(e,t){if(!_e)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Ye(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,l=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=l,e.childLanes=n,t}function Zy(e,t,n){var l=t.pendingProps;switch(gc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ye(t),null;case 1:return Ye(t),null;case 3:return n=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),hn(Ie),Re(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&($a(t)?yn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Pf())),Ye(t),null;case 26:return n=t.memoizedState,e===null?(yn(t),n!==null?(Ye(t),xm(t,n)):(Ye(t),t.flags&=-16777217)):n?n!==e.memoizedState?(yn(t),Ye(t),xm(t,n)):(Ye(t),t.flags&=-16777217):(e.memoizedProps!==l&&yn(t),Ye(t),t.flags&=-16777217),null;case 27:we(t),n=ce.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&yn(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Ye(t),null}e=te.current,$a(t)?$f(t):(e=xh(i,l,n),t.stateNode=e,yn(t))}return Ye(t),null;case 5:if(we(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&yn(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Ye(t),null}if(e=te.current,$a(t))$f(t);else{switch(i=Ko(ce.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}e[ft]=t,e[pt]=l;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(st(e,n,l),n){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&yn(t)}}return Ye(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&yn(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(c(166));if(e=ce.current,$a(t)){if(e=t.stateNode,n=t.memoizedProps,l=null,i=vt,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}e[ft]=t,e=!!(e.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||dh(e.nodeValue,n)),e||yl(t)}else e=Ko(e).createTextNode(l),e[ft]=t,t.stateNode=e}return Ye(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=$a(t),l!==null&&l.dehydrated!==null){if(e===null){if(!i)throw Error(c(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(c(317));i[ft]=t}else Wa(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ye(t),i=!1}else i=Pf(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(gn(t),t):(gn(t),null)}if(gn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=l!==null,e=e!==null&&e.memoizedState!==null,n){l=t.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var o=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(o=l.memoizedState.cachePool.pool),o!==i&&(l.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),zo(t,t.updateQueue),Ye(t),null;case 4:return Re(),e===null&&js(t.stateNode.containerInfo),Ye(t),null;case 10:return hn(t.type),Ye(t),null;case 19:if(F(et),i=t.memoizedState,i===null)return Ye(t),null;if(l=(t.flags&128)!==0,o=i.rendering,o===null)if(l)di(i,!1);else{if(Xe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(o=_o(e),o!==null){for(t.flags|=128,di(i,!1),e=o.updateQueue,t.updateQueue=e,zo(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Kf(n,e),n=n.sibling;return W(et,et.current&1|2),t.child}e=e.sibling}i.tail!==null&&ut()>Bo&&(t.flags|=128,l=!0,di(i,!1),t.lanes=4194304)}else{if(!l)if(e=_o(o),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,zo(t,e),di(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!_e)return Ye(t),null}else 2*ut()-i.renderingStartTime>Bo&&n!==536870912&&(t.flags|=128,l=!0,di(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(e=i.last,e!==null?e.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ut(),t.sibling=null,e=et.current,W(et,l?e&1|2:e&1),t):(Ye(t),null);case 22:case 23:return gn(t),Rc(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(n&536870912)!==0&&(t.flags&128)===0&&(Ye(t),t.subtreeFlags&6&&(t.flags|=8192)):Ye(t),n=t.updateQueue,n!==null&&zo(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==n&&(t.flags|=2048),e!==null&&F(Sl),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),hn(Ie),Ye(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function Ky(e,t){switch(gc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return hn(Ie),Re(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return we(t),null;case 13:if(gn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));Wa()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return F(et),null;case 4:return Re(),null;case 10:return hn(t.type),null;case 22:case 23:return gn(t),Rc(),e!==null&&F(Sl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return hn(Ie),null;case 25:return null;default:return null}}function bm(e,t){switch(gc(t),t.tag){case 3:hn(Ie),Re();break;case 26:case 27:case 5:we(t);break;case 4:Re();break;case 13:gn(t);break;case 19:F(et);break;case 10:hn(t.type);break;case 22:case 23:gn(t),Rc(),e!==null&&F(Sl);break;case 24:hn(Ie)}}function mi(e,t){try{var n=t.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&e)===e){l=void 0;var o=n.create,d=n.inst;l=o(),d.destroy=l}n=n.next}while(n!==i)}}catch(p){Be(t,t.return,p)}}function Ln(e,t,n){try{var l=t.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var o=i.next;l=o;do{if((l.tag&e)===e){var d=l.inst,p=d.destroy;if(p!==void 0){d.destroy=void 0,i=t;var S=n,D=p;try{D()}catch(q){Be(i,S,q)}}}l=l.next}while(l!==o)}}catch(q){Be(t,t.return,q)}}function Sm(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{cd(t,n)}catch(l){Be(e,e.return,l)}}}function wm(e,t,n){n.props=El(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(l){Be(e,t,l)}}function hi(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof n=="function"?e.refCleanup=n(l):n.current=l}}catch(i){Be(e,t,i)}}function en(e,t){var n=e.ref,l=e.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){Be(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){Be(e,t,i)}else n.current=null}function Em(e){var t=e.type,n=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break e;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){Be(e,e.return,i)}}function as(e,t,n){try{var l=e.stateNode;h0(l,e.type,n,t),l[pt]=t}catch(i){Be(e,e.return,i)}}function Nm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Zn(e.type)||e.tag===4}function is(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Nm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Zn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function os(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Zo));else if(l!==4&&(l===27&&Zn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(os(e,t,n),e=e.sibling;e!==null;)os(e,t,n),e=e.sibling}function Uo(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(l!==4&&(l===27&&Zn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Uo(e,t,n),e=e.sibling;e!==null;)Uo(e,t,n),e=e.sibling}function Am(e){var t=e.stateNode,n=e.memoizedProps;try{for(var l=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);st(t,l,n),t[ft]=e,t[pt]=n}catch(o){Be(e,e.return,o)}}var xn=!1,Je=!1,rs=!1,Tm=typeof WeakSet=="function"?WeakSet:Set,at=null;function Jy(e,t){if(e=e.containerInfo,Os=Io,e=Bf(e),ic(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,o=l.focusNode;l=l.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var d=0,p=-1,S=-1,D=0,q=0,Q=e,z=null;t:for(;;){for(var H;Q!==n||i!==0&&Q.nodeType!==3||(p=d+i),Q!==o||l!==0&&Q.nodeType!==3||(S=d+l),Q.nodeType===3&&(d+=Q.nodeValue.length),(H=Q.firstChild)!==null;)z=Q,Q=H;for(;;){if(Q===e)break t;if(z===n&&++D===i&&(p=d),z===o&&++q===l&&(S=d),(H=Q.nextSibling)!==null)break;Q=z,z=Q.parentNode}Q=H}n=p===-1||S===-1?null:{start:p,end:S}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ms={focusedElem:e,selectionRange:n},Io=!1,at=t;at!==null;)if(t=at,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,at=e;else for(;at!==null;){switch(t=at,o=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&o!==null){e=void 0,n=t,i=o.memoizedProps,o=o.memoizedState,l=n.stateNode;try{var he=El(n.type,i,n.elementType===n.type);e=l.getSnapshotBeforeUpdate(he,o),l.__reactInternalSnapshotBeforeUpdate=e}catch(de){Be(n,n.return,de)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Us(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Us(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,at=e;break}at=t.return}}function Cm(e,t,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:kn(e,n),l&4&&mi(5,n);break;case 1:if(kn(e,n),l&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(d){Be(n,n.return,d)}else{var i=El(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(d){Be(n,n.return,d)}}l&64&&Sm(n),l&512&&hi(n,n.return);break;case 3:if(kn(e,n),l&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{cd(e,t)}catch(d){Be(n,n.return,d)}}break;case 27:t===null&&l&4&&Am(n);case 26:case 5:kn(e,n),t===null&&l&4&&Em(n),l&512&&hi(n,n.return);break;case 12:kn(e,n);break;case 13:kn(e,n),l&4&&Rm(e,n),l&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=l0.bind(null,n),S0(e,n))));break;case 22:if(l=n.memoizedState!==null||xn,!l){t=t!==null&&t.memoizedState!==null||Je,i=xn;var o=Je;xn=l,(Je=t)&&!o?qn(e,n,(n.subtreeFlags&8772)!==0):kn(e,n),xn=i,Je=o}break;case 30:break;default:kn(e,n)}}function jm(e){var t=e.alternate;t!==null&&(e.alternate=null,jm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&qr(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ve=null,bt=!1;function bn(e,t,n){for(n=n.child;n!==null;)_m(e,t,n),n=n.sibling}function _m(e,t,n){if(wt&&typeof wt.onCommitFiberUnmount=="function")try{wt.onCommitFiberUnmount(Ua,n)}catch{}switch(n.tag){case 26:Je||en(n,t),bn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Je||en(n,t);var l=Ve,i=bt;Zn(n.type)&&(Ve=n.stateNode,bt=!1),bn(e,t,n),Ei(n.stateNode),Ve=l,bt=i;break;case 5:Je||en(n,t);case 6:if(l=Ve,i=bt,Ve=null,bn(e,t,n),Ve=l,bt=i,Ve!==null)if(bt)try{(Ve.nodeType===9?Ve.body:Ve.nodeName==="HTML"?Ve.ownerDocument.body:Ve).removeChild(n.stateNode)}catch(o){Be(n,t,o)}else try{Ve.removeChild(n.stateNode)}catch(o){Be(n,t,o)}break;case 18:Ve!==null&&(bt?(e=Ve,ph(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Oi(e)):ph(Ve,n.stateNode));break;case 4:l=Ve,i=bt,Ve=n.stateNode.containerInfo,bt=!0,bn(e,t,n),Ve=l,bt=i;break;case 0:case 11:case 14:case 15:Je||Ln(2,n,t),Je||Ln(4,n,t),bn(e,t,n);break;case 1:Je||(en(n,t),l=n.stateNode,typeof l.componentWillUnmount=="function"&&wm(n,t,l)),bn(e,t,n);break;case 21:bn(e,t,n);break;case 22:Je=(l=Je)||n.memoizedState!==null,bn(e,t,n),Je=l;break;default:bn(e,t,n)}}function Rm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Oi(e)}catch(n){Be(t,t.return,n)}}function $y(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Tm),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Tm),t;default:throw Error(c(435,e.tag))}}function cs(e,t){var n=$y(e);t.forEach(function(l){var i=a0.bind(null,e,l);n.has(l)||(n.add(l),l.then(i,i))})}function Tt(e,t){var n=t.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],o=e,d=t,p=d;e:for(;p!==null;){switch(p.tag){case 27:if(Zn(p.type)){Ve=p.stateNode,bt=!1;break e}break;case 5:Ve=p.stateNode,bt=!1;break e;case 3:case 4:Ve=p.stateNode.containerInfo,bt=!0;break e}p=p.return}if(Ve===null)throw Error(c(160));_m(o,d,i),Ve=null,bt=!1,o=i.alternate,o!==null&&(o.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Om(t,e),t=t.sibling}var Gt=null;function Om(e,t){var n=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Tt(t,e),Ct(e),l&4&&(Ln(3,e,e.return),mi(3,e),Ln(5,e,e.return));break;case 1:Tt(t,e),Ct(e),l&512&&(Je||n===null||en(n,n.return)),l&64&&xn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=Gt;if(Tt(t,e),Ct(e),l&512&&(Je||n===null||en(n,n.return)),l&4){var o=n!==null?n.memoizedState:null;if(l=e.memoizedState,n===null)if(l===null)if(e.stateNode===null){e:{l=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(l){case"title":o=i.getElementsByTagName("title")[0],(!o||o[La]||o[ft]||o.namespaceURI==="http://www.w3.org/2000/svg"||o.hasAttribute("itemprop"))&&(o=i.createElement(l),i.head.insertBefore(o,i.querySelector("head > title"))),st(o,l,n),o[ft]=e,nt(o),l=o;break e;case"link":var d=Ah("link","href",i).get(l+(n.href||""));if(d){for(var p=0;p<d.length;p++)if(o=d[p],o.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&o.getAttribute("rel")===(n.rel==null?null:n.rel)&&o.getAttribute("title")===(n.title==null?null:n.title)&&o.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){d.splice(p,1);break t}}o=i.createElement(l),st(o,l,n),i.head.appendChild(o);break;case"meta":if(d=Ah("meta","content",i).get(l+(n.content||""))){for(p=0;p<d.length;p++)if(o=d[p],o.getAttribute("content")===(n.content==null?null:""+n.content)&&o.getAttribute("name")===(n.name==null?null:n.name)&&o.getAttribute("property")===(n.property==null?null:n.property)&&o.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&o.getAttribute("charset")===(n.charSet==null?null:n.charSet)){d.splice(p,1);break t}}o=i.createElement(l),st(o,l,n),i.head.appendChild(o);break;default:throw Error(c(468,l))}o[ft]=e,nt(o),l=o}e.stateNode=l}else Th(i,e.type,e.stateNode);else e.stateNode=Nh(i,l,e.memoizedProps);else o!==l?(o===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):o.count--,l===null?Th(i,e.type,e.stateNode):Nh(i,l,e.memoizedProps)):l===null&&e.stateNode!==null&&as(e,e.memoizedProps,n.memoizedProps)}break;case 27:Tt(t,e),Ct(e),l&512&&(Je||n===null||en(n,n.return)),n!==null&&l&4&&as(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Tt(t,e),Ct(e),l&512&&(Je||n===null||en(n,n.return)),e.flags&32){i=e.stateNode;try{ql(i,"")}catch(H){Be(e,e.return,H)}}l&4&&e.stateNode!=null&&(i=e.memoizedProps,as(e,i,n!==null?n.memoizedProps:i)),l&1024&&(rs=!0);break;case 6:if(Tt(t,e),Ct(e),l&4){if(e.stateNode===null)throw Error(c(162));l=e.memoizedProps,n=e.stateNode;try{n.nodeValue=l}catch(H){Be(e,e.return,H)}}break;case 3:if(Wo=null,i=Gt,Gt=Jo(t.containerInfo),Tt(t,e),Gt=i,Ct(e),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Oi(t.containerInfo)}catch(H){Be(e,e.return,H)}rs&&(rs=!1,Mm(e));break;case 4:l=Gt,Gt=Jo(e.stateNode.containerInfo),Tt(t,e),Ct(e),Gt=l;break;case 12:Tt(t,e),Ct(e);break;case 13:Tt(t,e),Ct(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(hs=ut()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,cs(e,l)));break;case 22:i=e.memoizedState!==null;var S=n!==null&&n.memoizedState!==null,D=xn,q=Je;if(xn=D||i,Je=q||S,Tt(t,e),Je=q,xn=D,Ct(e),l&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(n===null||S||xn||Je||Nl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){S=n=t;try{if(o=S.stateNode,i)d=o.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{p=S.stateNode;var Q=S.memoizedProps.style,z=Q!=null&&Q.hasOwnProperty("display")?Q.display:null;p.style.display=z==null||typeof z=="boolean"?"":(""+z).trim()}}catch(H){Be(S,S.return,H)}}}else if(t.tag===6){if(n===null){S=t;try{S.stateNode.nodeValue=i?"":S.memoizedProps}catch(H){Be(S,S.return,H)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,cs(e,n))));break;case 19:Tt(t,e),Ct(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,cs(e,l)));break;case 30:break;case 21:break;default:Tt(t,e),Ct(e)}}function Ct(e){var t=e.flags;if(t&2){try{for(var n,l=e.return;l!==null;){if(Nm(l)){n=l;break}l=l.return}if(n==null)throw Error(c(160));switch(n.tag){case 27:var i=n.stateNode,o=is(e);Uo(e,o,i);break;case 5:var d=n.stateNode;n.flags&32&&(ql(d,""),n.flags&=-33);var p=is(e);Uo(e,p,d);break;case 3:case 4:var S=n.stateNode.containerInfo,D=is(e);os(e,D,S);break;default:throw Error(c(161))}}catch(q){Be(e,e.return,q)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Mm(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Mm(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function kn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Cm(e,t.alternate,t),t=t.sibling}function Nl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ln(4,t,t.return),Nl(t);break;case 1:en(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&wm(t,t.return,n),Nl(t);break;case 27:Ei(t.stateNode);case 26:case 5:en(t,t.return),Nl(t);break;case 22:t.memoizedState===null&&Nl(t);break;case 30:Nl(t);break;default:Nl(t)}e=e.sibling}}function qn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,i=e,o=t,d=o.flags;switch(o.tag){case 0:case 11:case 15:qn(i,o,n),mi(4,o);break;case 1:if(qn(i,o,n),l=o,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(D){Be(l,l.return,D)}if(l=o,i=l.updateQueue,i!==null){var p=l.stateNode;try{var S=i.shared.hiddenCallbacks;if(S!==null)for(i.shared.hiddenCallbacks=null,i=0;i<S.length;i++)rd(S[i],p)}catch(D){Be(l,l.return,D)}}n&&d&64&&Sm(o),hi(o,o.return);break;case 27:Am(o);case 26:case 5:qn(i,o,n),n&&l===null&&d&4&&Em(o),hi(o,o.return);break;case 12:qn(i,o,n);break;case 13:qn(i,o,n),n&&d&4&&Rm(i,o);break;case 22:o.memoizedState===null&&qn(i,o,n),hi(o,o.return);break;case 30:break;default:qn(i,o,n)}t=t.sibling}}function ss(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Ia(n))}function us(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ia(e))}function tn(e,t,n,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Dm(e,t,n,l),t=t.sibling}function Dm(e,t,n,l){var i=t.flags;switch(t.tag){case 0:case 11:case 15:tn(e,t,n,l),i&2048&&mi(9,t);break;case 1:tn(e,t,n,l);break;case 3:tn(e,t,n,l),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ia(e)));break;case 12:if(i&2048){tn(e,t,n,l),e=t.stateNode;try{var o=t.memoizedProps,d=o.id,p=o.onPostCommit;typeof p=="function"&&p(d,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(S){Be(t,t.return,S)}}else tn(e,t,n,l);break;case 13:tn(e,t,n,l);break;case 23:break;case 22:o=t.stateNode,d=t.alternate,t.memoizedState!==null?o._visibility&2?tn(e,t,n,l):vi(e,t):o._visibility&2?tn(e,t,n,l):(o._visibility|=2,ia(e,t,n,l,(t.subtreeFlags&10256)!==0)),i&2048&&ss(d,t);break;case 24:tn(e,t,n,l),i&2048&&us(t.alternate,t);break;default:tn(e,t,n,l)}}function ia(e,t,n,l,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var o=e,d=t,p=n,S=l,D=d.flags;switch(d.tag){case 0:case 11:case 15:ia(o,d,p,S,i),mi(8,d);break;case 23:break;case 22:var q=d.stateNode;d.memoizedState!==null?q._visibility&2?ia(o,d,p,S,i):vi(o,d):(q._visibility|=2,ia(o,d,p,S,i)),i&&D&2048&&ss(d.alternate,d);break;case 24:ia(o,d,p,S,i),i&&D&2048&&us(d.alternate,d);break;default:ia(o,d,p,S,i)}t=t.sibling}}function vi(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,l=t,i=l.flags;switch(l.tag){case 22:vi(n,l),i&2048&&ss(l.alternate,l);break;case 24:vi(n,l),i&2048&&us(l.alternate,l);break;default:vi(n,l)}t=t.sibling}}var gi=8192;function oa(e){if(e.subtreeFlags&gi)for(e=e.child;e!==null;)zm(e),e=e.sibling}function zm(e){switch(e.tag){case 26:oa(e),e.flags&gi&&e.memoizedState!==null&&z0(Gt,e.memoizedState,e.memoizedProps);break;case 5:oa(e);break;case 3:case 4:var t=Gt;Gt=Jo(e.stateNode.containerInfo),oa(e),Gt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=gi,gi=16777216,oa(e),gi=t):oa(e));break;default:oa(e)}}function Um(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function pi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];at=l,Bm(l,e)}Um(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Hm(e),e=e.sibling}function Hm(e){switch(e.tag){case 0:case 11:case 15:pi(e),e.flags&2048&&Ln(9,e,e.return);break;case 3:pi(e);break;case 12:pi(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Ho(e)):pi(e);break;default:pi(e)}}function Ho(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];at=l,Bm(l,e)}Um(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Ln(8,t,t.return),Ho(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Ho(t));break;default:Ho(t)}e=e.sibling}}function Bm(e,t){for(;at!==null;){var n=at;switch(n.tag){case 0:case 11:case 15:Ln(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Ia(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,at=l;else e:for(n=e;at!==null;){l=at;var i=l.sibling,o=l.return;if(jm(l),l===n){at=null;break e}if(i!==null){i.return=o,at=i;break e}at=o}}}var Wy={getCacheForType:function(e){var t=dt(Ie),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Py=typeof WeakMap=="function"?WeakMap:Map,Oe=0,ke=null,Ne=null,Ce=0,Me=0,jt=null,Vn=!1,ra=!1,fs=!1,Sn=0,Xe=0,Yn=0,Al=0,ds=0,qt=0,ca=0,yi=null,St=null,ms=!1,hs=0,Bo=1/0,Lo=null,Gn=null,ct=0,Xn=null,sa=null,ua=0,vs=0,gs=null,Lm=null,xi=0,ps=null;function _t(){if((Oe&2)!==0&&Ce!==0)return Ce&-Ce;if(j.T!==null){var e=Pl;return e!==0?e:Ns()}return Iu()}function km(){qt===0&&(qt=(Ce&536870912)===0||_e?$u():536870912);var e=kt.current;return e!==null&&(e.flags|=32),qt}function Rt(e,t,n){(e===ke&&(Me===2||Me===9)||e.cancelPendingCommit!==null)&&(fa(e,0),Qn(e,Ce,qt,!1)),Ba(e,n),((Oe&2)===0||e!==ke)&&(e===ke&&((Oe&2)===0&&(Al|=n),Xe===4&&Qn(e,Ce,qt,!1)),nn(e))}function qm(e,t,n){if((Oe&6)!==0)throw Error(c(327));var l=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Ha(e,t),i=l?e0(e,t):bs(e,t,!0),o=l;do{if(i===0){ra&&!l&&Qn(e,t,0,!1);break}else{if(n=e.current.alternate,o&&!Fy(n)){i=bs(e,t,!1),o=!1;continue}if(i===2){if(o=t,e.errorRecoveryDisabledLanes&o)var d=0;else d=e.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){t=d;e:{var p=e;i=yi;var S=p.current.memoizedState.isDehydrated;if(S&&(fa(p,d).flags|=256),d=bs(p,d,!1),d!==2){if(fs&&!S){p.errorRecoveryDisabledLanes|=o,Al|=o,i=4;break e}o=St,St=i,o!==null&&(St===null?St=o:St.push.apply(St,o))}i=d}if(o=!1,i!==2)continue}}if(i===1){fa(e,0),Qn(e,t,0,!0);break}e:{switch(l=e,o=i,o){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Qn(l,t,qt,!Vn);break e;case 2:St=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(i=hs+300-ut(),10<i)){if(Qn(l,t,qt,!Vn),Ji(l,0,!0)!==0)break e;l.timeoutHandle=vh(Vm.bind(null,l,n,St,Lo,ms,t,qt,Al,ca,Vn,o,2,-0,0),i);break e}Vm(l,n,St,Lo,ms,t,qt,Al,ca,Vn,o,0,-0,0)}}break}while(!0);nn(e)}function Vm(e,t,n,l,i,o,d,p,S,D,q,Q,z,H){if(e.timeoutHandle=-1,Q=t.subtreeFlags,(Q&8192||(Q&16785408)===16785408)&&(Ti={stylesheets:null,count:0,unsuspend:D0},zm(t),Q=U0(),Q!==null)){e.cancelPendingCommit=Q(Jm.bind(null,e,t,o,n,l,i,d,p,S,q,1,z,H)),Qn(e,o,d,!D);return}Jm(e,t,o,n,l,i,d,p,S)}function Fy(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],o=i.getSnapshot;i=i.value;try{if(!Nt(o(),i))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Qn(e,t,n,l){t&=~ds,t&=~Al,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var i=t;0<i;){var o=31-Et(i),d=1<<o;l[o]=-1,i&=~d}n!==0&&Pu(e,n,t)}function ko(){return(Oe&6)===0?(bi(0),!1):!0}function ys(){if(Ne!==null){if(Me===0)var e=Ne.return;else e=Ne,mn=xl=null,Uc(e),la=null,ui=0,e=Ne;for(;e!==null;)bm(e.alternate,e),e=e.return;Ne=null}}function fa(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,g0(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),ys(),ke=e,Ne=n=un(e.current,null),Ce=t,Me=0,jt=null,Vn=!1,ra=Ha(e,t),fs=!1,ca=qt=ds=Al=Yn=Xe=0,St=yi=null,ms=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var i=31-Et(l),o=1<<i;t|=e[i],l&=~o}return Sn=t,oo(),n}function Ym(e,t){xe=null,j.H=To,t===ti||t===go?(t=id(),Me=3):t===nd?(t=id(),Me=4):Me=t===om?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,jt=t,Ne===null&&(Xe=1,Oo(e,Ut(t,e.current)))}function Gm(){var e=j.H;return j.H=To,e===null?To:e}function Xm(){var e=j.A;return j.A=Wy,e}function xs(){Xe=4,Vn||(Ce&4194048)!==Ce&&kt.current!==null||(ra=!0),(Yn&134217727)===0&&(Al&134217727)===0||ke===null||Qn(ke,Ce,qt,!1)}function bs(e,t,n){var l=Oe;Oe|=2;var i=Gm(),o=Xm();(ke!==e||Ce!==t)&&(Lo=null,fa(e,t)),t=!1;var d=Xe;e:do try{if(Me!==0&&Ne!==null){var p=Ne,S=jt;switch(Me){case 8:ys(),d=6;break e;case 3:case 2:case 9:case 6:kt.current===null&&(t=!0);var D=Me;if(Me=0,jt=null,da(e,p,S,D),n&&ra){d=0;break e}break;default:D=Me,Me=0,jt=null,da(e,p,S,D)}}Iy(),d=Xe;break}catch(q){Ym(e,q)}while(!0);return t&&e.shellSuspendCounter++,mn=xl=null,Oe=l,j.H=i,j.A=o,Ne===null&&(ke=null,Ce=0,oo()),d}function Iy(){for(;Ne!==null;)Qm(Ne)}function e0(e,t){var n=Oe;Oe|=2;var l=Gm(),i=Xm();ke!==e||Ce!==t?(Lo=null,Bo=ut()+500,fa(e,t)):ra=Ha(e,t);e:do try{if(Me!==0&&Ne!==null){t=Ne;var o=jt;t:switch(Me){case 1:Me=0,jt=null,da(e,t,o,1);break;case 2:case 9:if(ld(o)){Me=0,jt=null,Zm(t);break}t=function(){Me!==2&&Me!==9||ke!==e||(Me=7),nn(e)},o.then(t,t);break e;case 3:Me=7;break e;case 4:Me=5;break e;case 7:ld(o)?(Me=0,jt=null,Zm(t)):(Me=0,jt=null,da(e,t,o,7));break;case 5:var d=null;switch(Ne.tag){case 26:d=Ne.memoizedState;case 5:case 27:var p=Ne;if(!d||Ch(d)){Me=0,jt=null;var S=p.sibling;if(S!==null)Ne=S;else{var D=p.return;D!==null?(Ne=D,qo(D)):Ne=null}break t}}Me=0,jt=null,da(e,t,o,5);break;case 6:Me=0,jt=null,da(e,t,o,6);break;case 8:ys(),Xe=6;break e;default:throw Error(c(462))}}t0();break}catch(q){Ym(e,q)}while(!0);return mn=xl=null,j.H=l,j.A=i,Oe=n,Ne!==null?0:(ke=null,Ce=0,oo(),Xe)}function t0(){for(;Ne!==null&&!ol();)Qm(Ne)}function Qm(e){var t=ym(e.alternate,e,Sn);e.memoizedProps=e.pendingProps,t===null?qo(e):Ne=t}function Zm(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=dm(n,t,t.pendingProps,t.type,void 0,Ce);break;case 11:t=dm(n,t,t.pendingProps,t.type.render,t.ref,Ce);break;case 5:Uc(t);default:bm(n,t),t=Ne=Kf(t,Sn),t=ym(n,t,Sn)}e.memoizedProps=e.pendingProps,t===null?qo(e):Ne=t}function da(e,t,n,l){mn=xl=null,Uc(t),la=null,ui=0;var i=t.return;try{if(Xy(e,i,t,n,Ce)){Xe=1,Oo(e,Ut(n,e.current)),Ne=null;return}}catch(o){if(i!==null)throw Ne=i,o;Xe=1,Oo(e,Ut(n,e.current)),Ne=null;return}t.flags&32768?(_e||l===1?e=!0:ra||(Ce&536870912)!==0?e=!1:(Vn=e=!0,(l===2||l===9||l===3||l===6)&&(l=kt.current,l!==null&&l.tag===13&&(l.flags|=16384))),Km(t,e)):qo(t)}function qo(e){var t=e;do{if((t.flags&32768)!==0){Km(t,Vn);return}e=t.return;var n=Zy(t.alternate,t,Sn);if(n!==null){Ne=n;return}if(t=t.sibling,t!==null){Ne=t;return}Ne=t=e}while(t!==null);Xe===0&&(Xe=5)}function Km(e,t){do{var n=Ky(e.alternate,e);if(n!==null){n.flags&=32767,Ne=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){Ne=e;return}Ne=e=n}while(e!==null);Xe=6,Ne=null}function Jm(e,t,n,l,i,o,d,p,S){e.cancelPendingCommit=null;do Vo();while(ct!==0);if((Oe&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(o=t.lanes|t.childLanes,o|=uc,Dp(e,n,o,d,p,S),e===ke&&(Ne=ke=null,Ce=0),sa=t,Xn=e,ua=n,vs=o,gs=i,Lm=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,i0(Qi,function(){return Im(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=j.T,j.T=null,i=Z.p,Z.p=2,d=Oe,Oe|=4;try{Jy(e,t,n)}finally{Oe=d,Z.p=i,j.T=l}}ct=1,$m(),Wm(),Pm()}}function $m(){if(ct===1){ct=0;var e=Xn,t=sa,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=j.T,j.T=null;var l=Z.p;Z.p=2;var i=Oe;Oe|=4;try{Om(t,e);var o=Ms,d=Bf(e.containerInfo),p=o.focusedElem,S=o.selectionRange;if(d!==p&&p&&p.ownerDocument&&Hf(p.ownerDocument.documentElement,p)){if(S!==null&&ic(p)){var D=S.start,q=S.end;if(q===void 0&&(q=D),"selectionStart"in p)p.selectionStart=D,p.selectionEnd=Math.min(q,p.value.length);else{var Q=p.ownerDocument||document,z=Q&&Q.defaultView||window;if(z.getSelection){var H=z.getSelection(),he=p.textContent.length,de=Math.min(S.start,he),He=S.end===void 0?de:Math.min(S.end,he);!H.extend&&de>He&&(d=He,He=de,de=d);var T=Uf(p,de),A=Uf(p,He);if(T&&A&&(H.rangeCount!==1||H.anchorNode!==T.node||H.anchorOffset!==T.offset||H.focusNode!==A.node||H.focusOffset!==A.offset)){var O=Q.createRange();O.setStart(T.node,T.offset),H.removeAllRanges(),de>He?(H.addRange(O),H.extend(A.node,A.offset)):(O.setEnd(A.node,A.offset),H.addRange(O))}}}}for(Q=[],H=p;H=H.parentNode;)H.nodeType===1&&Q.push({element:H,left:H.scrollLeft,top:H.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<Q.length;p++){var X=Q[p];X.element.scrollLeft=X.left,X.element.scrollTop=X.top}}Io=!!Os,Ms=Os=null}finally{Oe=i,Z.p=l,j.T=n}}e.current=t,ct=2}}function Wm(){if(ct===2){ct=0;var e=Xn,t=sa,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=j.T,j.T=null;var l=Z.p;Z.p=2;var i=Oe;Oe|=4;try{Cm(e,t.alternate,t)}finally{Oe=i,Z.p=l,j.T=n}}ct=3}}function Pm(){if(ct===4||ct===3){ct=0,rl();var e=Xn,t=sa,n=ua,l=Lm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ct=5:(ct=0,sa=Xn=null,Fm(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(Gn=null),Lr(n),t=t.stateNode,wt&&typeof wt.onCommitFiberRoot=="function")try{wt.onCommitFiberRoot(Ua,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=j.T,i=Z.p,Z.p=2,j.T=null;try{for(var o=e.onRecoverableError,d=0;d<l.length;d++){var p=l[d];o(p.value,{componentStack:p.stack})}}finally{j.T=t,Z.p=i}}(ua&3)!==0&&Vo(),nn(e),i=e.pendingLanes,(n&4194090)!==0&&(i&42)!==0?e===ps?xi++:(xi=0,ps=e):xi=0,bi(0)}}function Fm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Ia(t)))}function Vo(e){return $m(),Wm(),Pm(),Im()}function Im(){if(ct!==5)return!1;var e=Xn,t=vs;vs=0;var n=Lr(ua),l=j.T,i=Z.p;try{Z.p=32>n?32:n,j.T=null,n=gs,gs=null;var o=Xn,d=ua;if(ct=0,sa=Xn=null,ua=0,(Oe&6)!==0)throw Error(c(331));var p=Oe;if(Oe|=4,Hm(o.current),Dm(o,o.current,d,n),Oe=p,bi(0,!1),wt&&typeof wt.onPostCommitFiberRoot=="function")try{wt.onPostCommitFiberRoot(Ua,o)}catch{}return!0}finally{Z.p=i,j.T=l,Fm(e,t)}}function eh(e,t,n){t=Ut(n,t),t=$c(e.stateNode,t,2),e=zn(e,t,2),e!==null&&(Ba(e,2),nn(e))}function Be(e,t,n){if(e.tag===3)eh(e,e,n);else for(;t!==null;){if(t.tag===3){eh(t,e,n);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Gn===null||!Gn.has(l))){e=Ut(n,e),n=am(2),l=zn(t,n,2),l!==null&&(im(n,l,t,e),Ba(l,2),nn(l));break}}t=t.return}}function Ss(e,t,n){var l=e.pingCache;if(l===null){l=e.pingCache=new Py;var i=new Set;l.set(t,i)}else i=l.get(t),i===void 0&&(i=new Set,l.set(t,i));i.has(n)||(fs=!0,i.add(n),e=n0.bind(null,e,t,n),t.then(e,e))}function n0(e,t,n){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ke===e&&(Ce&n)===n&&(Xe===4||Xe===3&&(Ce&62914560)===Ce&&300>ut()-hs?(Oe&2)===0&&fa(e,0):ds|=n,ca===Ce&&(ca=0)),nn(e)}function th(e,t){t===0&&(t=Wu()),e=Kl(e,t),e!==null&&(Ba(e,t),nn(e))}function l0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),th(e,n)}function a0(e,t){var n=0;switch(e.tag){case 13:var l=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(c(314))}l!==null&&l.delete(t),th(e,n)}function i0(e,t){return ot(e,t)}var Yo=null,ma=null,ws=!1,Go=!1,Es=!1,Tl=0;function nn(e){e!==ma&&e.next===null&&(ma===null?Yo=ma=e:ma=ma.next=e),Go=!0,ws||(ws=!0,r0())}function bi(e,t){if(!Es&&Go){Es=!0;do for(var n=!1,l=Yo;l!==null;){if(e!==0){var i=l.pendingLanes;if(i===0)var o=0;else{var d=l.suspendedLanes,p=l.pingedLanes;o=(1<<31-Et(42|e)+1)-1,o&=i&~(d&~p),o=o&201326741?o&201326741|1:o?o|2:0}o!==0&&(n=!0,ih(l,o))}else o=Ce,o=Ji(l,l===ke?o:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(o&3)===0||Ha(l,o)||(n=!0,ih(l,o));l=l.next}while(n);Es=!1}}function o0(){nh()}function nh(){Go=ws=!1;var e=0;Tl!==0&&(v0()&&(e=Tl),Tl=0);for(var t=ut(),n=null,l=Yo;l!==null;){var i=l.next,o=lh(l,t);o===0?(l.next=null,n===null?Yo=i:n.next=i,i===null&&(ma=n)):(n=l,(e!==0||(o&3)!==0)&&(Go=!0)),l=i}bi(e)}function lh(e,t){for(var n=e.suspendedLanes,l=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes&-62914561;0<o;){var d=31-Et(o),p=1<<d,S=i[d];S===-1?((p&n)===0||(p&l)!==0)&&(i[d]=Mp(p,t)):S<=t&&(e.expiredLanes|=p),o&=~p}if(t=ke,n=Ce,n=Ji(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,n===0||e===t&&(Me===2||Me===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&gt(l),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Ha(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(l!==null&&gt(l),Lr(n)){case 2:case 8:n=Ku;break;case 32:n=Qi;break;case 268435456:n=Ju;break;default:n=Qi}return l=ah.bind(null,e),n=ot(n,l),e.callbackPriority=t,e.callbackNode=n,t}return l!==null&&l!==null&&gt(l),e.callbackPriority=2,e.callbackNode=null,2}function ah(e,t){if(ct!==0&&ct!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Vo()&&e.callbackNode!==n)return null;var l=Ce;return l=Ji(e,e===ke?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(qm(e,l,t),lh(e,ut()),e.callbackNode!=null&&e.callbackNode===n?ah.bind(null,e):null)}function ih(e,t){if(Vo())return null;qm(e,t,!0)}function r0(){p0(function(){(Oe&6)!==0?ot(cl,o0):nh()})}function Ns(){return Tl===0&&(Tl=$u()),Tl}function oh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Ii(""+e)}function rh(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function c0(e,t,n,l,i){if(t==="submit"&&n&&n.stateNode===i){var o=oh((i[pt]||null).action),d=l.submitter;d&&(t=(t=d[pt]||null)?oh(t.formAction):d.getAttribute("formAction"),t!==null&&(o=t,d=null));var p=new lo("action","action",null,l,i);e.push({event:p,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Tl!==0){var S=d?rh(i,d):new FormData(i);Xc(n,{pending:!0,data:S,method:i.method,action:o},null,S)}}else typeof o=="function"&&(p.preventDefault(),S=d?rh(i,d):new FormData(i),Xc(n,{pending:!0,data:S,method:i.method,action:o},o,S))},currentTarget:i}]})}}for(var As=0;As<sc.length;As++){var Ts=sc[As],s0=Ts.toLowerCase(),u0=Ts[0].toUpperCase()+Ts.slice(1);Yt(s0,"on"+u0)}Yt(qf,"onAnimationEnd"),Yt(Vf,"onAnimationIteration"),Yt(Yf,"onAnimationStart"),Yt("dblclick","onDoubleClick"),Yt("focusin","onFocus"),Yt("focusout","onBlur"),Yt(Cy,"onTransitionRun"),Yt(jy,"onTransitionStart"),Yt(_y,"onTransitionCancel"),Yt(Gf,"onTransitionEnd"),Bl("onMouseEnter",["mouseout","mouseover"]),Bl("onMouseLeave",["mouseout","mouseover"]),Bl("onPointerEnter",["pointerout","pointerover"]),Bl("onPointerLeave",["pointerout","pointerover"]),ul("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),ul("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),ul("onBeforeInput",["compositionend","keypress","textInput","paste"]),ul("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),ul("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),ul("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Si="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),f0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Si));function ch(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var l=e[n],i=l.event;l=l.listeners;e:{var o=void 0;if(t)for(var d=l.length-1;0<=d;d--){var p=l[d],S=p.instance,D=p.currentTarget;if(p=p.listener,S!==o&&i.isPropagationStopped())break e;o=p,i.currentTarget=D;try{o(i)}catch(q){Ro(q)}i.currentTarget=null,o=S}else for(d=0;d<l.length;d++){if(p=l[d],S=p.instance,D=p.currentTarget,p=p.listener,S!==o&&i.isPropagationStopped())break e;o=p,i.currentTarget=D;try{o(i)}catch(q){Ro(q)}i.currentTarget=null,o=S}}}}function Ae(e,t){var n=t[kr];n===void 0&&(n=t[kr]=new Set);var l=e+"__bubble";n.has(l)||(sh(t,e,2,!1),n.add(l))}function Cs(e,t,n){var l=0;t&&(l|=4),sh(n,e,l,t)}var Xo="_reactListening"+Math.random().toString(36).slice(2);function js(e){if(!e[Xo]){e[Xo]=!0,tf.forEach(function(n){n!=="selectionchange"&&(f0.has(n)||Cs(n,!1,e),Cs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Xo]||(t[Xo]=!0,Cs("selectionchange",!1,t))}}function sh(e,t,n,l){switch(Dh(t)){case 2:var i=L0;break;case 8:i=k0;break;default:i=Ys}n=i.bind(null,t,n,e),i=void 0,!Wr||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),l?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function _s(e,t,n,l,i){var o=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var d=l.tag;if(d===3||d===4){var p=l.stateNode.containerInfo;if(p===i)break;if(d===4)for(d=l.return;d!==null;){var S=d.tag;if((S===3||S===4)&&d.stateNode.containerInfo===i)return;d=d.return}for(;p!==null;){if(d=zl(p),d===null)return;if(S=d.tag,S===5||S===6||S===26||S===27){l=o=d;continue e}p=p.parentNode}}l=l.return}gf(function(){var D=o,q=Jr(n),Q=[];e:{var z=Xf.get(e);if(z!==void 0){var H=lo,he=e;switch(e){case"keypress":if(to(n)===0)break e;case"keydown":case"keyup":H=iy;break;case"focusin":he="focus",H=ec;break;case"focusout":he="blur",H=ec;break;case"beforeblur":case"afterblur":H=ec;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":H=xf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":H=Kp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":H=cy;break;case qf:case Vf:case Yf:H=Wp;break;case Gf:H=uy;break;case"scroll":case"scrollend":H=Qp;break;case"wheel":H=dy;break;case"copy":case"cut":case"paste":H=Fp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":H=Sf;break;case"toggle":case"beforetoggle":H=hy}var de=(t&4)!==0,He=!de&&(e==="scroll"||e==="scrollend"),T=de?z!==null?z+"Capture":null:z;de=[];for(var A=D,O;A!==null;){var X=A;if(O=X.stateNode,X=X.tag,X!==5&&X!==26&&X!==27||O===null||T===null||(X=qa(A,T),X!=null&&de.push(wi(A,X,O))),He)break;A=A.return}0<de.length&&(z=new H(z,he,null,n,q),Q.push({event:z,listeners:de}))}}if((t&7)===0){e:{if(z=e==="mouseover"||e==="pointerover",H=e==="mouseout"||e==="pointerout",z&&n!==Kr&&(he=n.relatedTarget||n.fromElement)&&(zl(he)||he[Dl]))break e;if((H||z)&&(z=q.window===q?q:(z=q.ownerDocument)?z.defaultView||z.parentWindow:window,H?(he=n.relatedTarget||n.toElement,H=D,he=he?zl(he):null,he!==null&&(He=m(he),de=he.tag,he!==He||de!==5&&de!==27&&de!==6)&&(he=null)):(H=null,he=D),H!==he)){if(de=xf,X="onMouseLeave",T="onMouseEnter",A="mouse",(e==="pointerout"||e==="pointerover")&&(de=Sf,X="onPointerLeave",T="onPointerEnter",A="pointer"),He=H==null?z:ka(H),O=he==null?z:ka(he),z=new de(X,A+"leave",H,n,q),z.target=He,z.relatedTarget=O,X=null,zl(q)===D&&(de=new de(T,A+"enter",he,n,q),de.target=O,de.relatedTarget=He,X=de),He=X,H&&he)t:{for(de=H,T=he,A=0,O=de;O;O=ha(O))A++;for(O=0,X=T;X;X=ha(X))O++;for(;0<A-O;)de=ha(de),A--;for(;0<O-A;)T=ha(T),O--;for(;A--;){if(de===T||T!==null&&de===T.alternate)break t;de=ha(de),T=ha(T)}de=null}else de=null;H!==null&&uh(Q,z,H,de,!1),he!==null&&He!==null&&uh(Q,He,he,de,!0)}}e:{if(z=D?ka(D):window,H=z.nodeName&&z.nodeName.toLowerCase(),H==="select"||H==="input"&&z.type==="file")var ne=_f;else if(Cf(z))if(Rf)ne=Ny;else{ne=wy;var Se=Sy}else H=z.nodeName,!H||H.toLowerCase()!=="input"||z.type!=="checkbox"&&z.type!=="radio"?D&&Zr(D.elementType)&&(ne=_f):ne=Ey;if(ne&&(ne=ne(e,D))){jf(Q,ne,n,q);break e}Se&&Se(e,z,D),e==="focusout"&&D&&z.type==="number"&&D.memoizedProps.value!=null&&Qr(z,"number",z.value)}switch(Se=D?ka(D):window,e){case"focusin":(Cf(Se)||Se.contentEditable==="true")&&(Xl=Se,oc=D,Ja=null);break;case"focusout":Ja=oc=Xl=null;break;case"mousedown":rc=!0;break;case"contextmenu":case"mouseup":case"dragend":rc=!1,Lf(Q,n,q);break;case"selectionchange":if(Ty)break;case"keydown":case"keyup":Lf(Q,n,q)}var oe;if(nc)e:{switch(e){case"compositionstart":var me="onCompositionStart";break e;case"compositionend":me="onCompositionEnd";break e;case"compositionupdate":me="onCompositionUpdate";break e}me=void 0}else Gl?Af(e,n)&&(me="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(me="onCompositionStart");me&&(wf&&n.locale!=="ko"&&(Gl||me!=="onCompositionStart"?me==="onCompositionEnd"&&Gl&&(oe=pf()):(Rn=q,Pr="value"in Rn?Rn.value:Rn.textContent,Gl=!0)),Se=Qo(D,me),0<Se.length&&(me=new bf(me,e,null,n,q),Q.push({event:me,listeners:Se}),oe?me.data=oe:(oe=Tf(n),oe!==null&&(me.data=oe)))),(oe=gy?py(e,n):yy(e,n))&&(me=Qo(D,"onBeforeInput"),0<me.length&&(Se=new bf("onBeforeInput","beforeinput",null,n,q),Q.push({event:Se,listeners:me}),Se.data=oe)),c0(Q,e,D,n,q)}ch(Q,t)})}function wi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qo(e,t){for(var n=t+"Capture",l=[];e!==null;){var i=e,o=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||o===null||(i=qa(e,n),i!=null&&l.unshift(wi(e,i,o)),i=qa(e,t),i!=null&&l.push(wi(e,i,o))),e.tag===3)return l;e=e.return}return[]}function ha(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function uh(e,t,n,l,i){for(var o=t._reactName,d=[];n!==null&&n!==l;){var p=n,S=p.alternate,D=p.stateNode;if(p=p.tag,S!==null&&S===l)break;p!==5&&p!==26&&p!==27||D===null||(S=D,i?(D=qa(n,o),D!=null&&d.unshift(wi(n,D,S))):i||(D=qa(n,o),D!=null&&d.push(wi(n,D,S)))),n=n.return}d.length!==0&&e.push({event:t,listeners:d})}var d0=/\r\n?/g,m0=/\u0000|\uFFFD/g;function fh(e){return(typeof e=="string"?e:""+e).replace(d0,`
`).replace(m0,"")}function dh(e,t){return t=fh(t),fh(e)===t}function Zo(){}function Ue(e,t,n,l,i,o){switch(n){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||ql(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&ql(e,""+l);break;case"className":Wi(e,"class",l);break;case"tabIndex":Wi(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Wi(e,n,l);break;case"style":hf(e,l,o);break;case"data":if(t!=="object"){Wi(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=Ii(""+l),e.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(n==="formAction"?(t!=="input"&&Ue(e,t,"name",i.name,i,null),Ue(e,t,"formEncType",i.formEncType,i,null),Ue(e,t,"formMethod",i.formMethod,i,null),Ue(e,t,"formTarget",i.formTarget,i,null)):(Ue(e,t,"encType",i.encType,i,null),Ue(e,t,"method",i.method,i,null),Ue(e,t,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=Ii(""+l),e.setAttribute(n,l);break;case"onClick":l!=null&&(e.onclick=Zo);break;case"onScroll":l!=null&&Ae("scroll",e);break;case"onScrollEnd":l!=null&&Ae("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=n}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}n=Ii(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""+l):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":l===!0?e.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,l):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(n,l):e.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(n):e.setAttribute(n,l);break;case"popover":Ae("beforetoggle",e),Ae("toggle",e),$i(e,"popover",l);break;case"xlinkActuate":cn(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":cn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":cn(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":cn(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":cn(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":cn(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":cn(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":cn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":cn(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":$i(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Gp.get(n)||n,$i(e,n,l))}}function Rs(e,t,n,l,i,o){switch(n){case"style":hf(e,l,o);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=n}}break;case"children":typeof l=="string"?ql(e,l):(typeof l=="number"||typeof l=="bigint")&&ql(e,""+l);break;case"onScroll":l!=null&&Ae("scroll",e);break;case"onScrollEnd":l!=null&&Ae("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Zo);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!nf.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),o=e[pt]||null,o=o!=null?o[n]:null,typeof o=="function"&&e.removeEventListener(t,o,i),typeof l=="function")){typeof o!="function"&&o!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,l,i);break e}n in e?e[n]=l:l===!0?e.setAttribute(n,""):$i(e,n,l)}}}function st(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ae("error",e),Ae("load",e);var l=!1,i=!1,o;for(o in n)if(n.hasOwnProperty(o)){var d=n[o];if(d!=null)switch(o){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Ue(e,t,o,d,n,null)}}i&&Ue(e,t,"srcSet",n.srcSet,n,null),l&&Ue(e,t,"src",n.src,n,null);return;case"input":Ae("invalid",e);var p=o=d=i=null,S=null,D=null;for(l in n)if(n.hasOwnProperty(l)){var q=n[l];if(q!=null)switch(l){case"name":i=q;break;case"type":d=q;break;case"checked":S=q;break;case"defaultChecked":D=q;break;case"value":o=q;break;case"defaultValue":p=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(c(137,t));break;default:Ue(e,t,l,q,n,null)}}uf(e,o,p,S,D,d,i,!1),Pi(e);return;case"select":Ae("invalid",e),l=d=o=null;for(i in n)if(n.hasOwnProperty(i)&&(p=n[i],p!=null))switch(i){case"value":o=p;break;case"defaultValue":d=p;break;case"multiple":l=p;default:Ue(e,t,i,p,n,null)}t=o,n=d,e.multiple=!!l,t!=null?kl(e,!!l,t,!1):n!=null&&kl(e,!!l,n,!0);return;case"textarea":Ae("invalid",e),o=i=l=null;for(d in n)if(n.hasOwnProperty(d)&&(p=n[d],p!=null))switch(d){case"value":l=p;break;case"defaultValue":i=p;break;case"children":o=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(c(91));break;default:Ue(e,t,d,p,n,null)}df(e,l,i,o),Pi(e);return;case"option":for(S in n)if(n.hasOwnProperty(S)&&(l=n[S],l!=null))switch(S){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ue(e,t,S,l,n,null)}return;case"dialog":Ae("beforetoggle",e),Ae("toggle",e),Ae("cancel",e),Ae("close",e);break;case"iframe":case"object":Ae("load",e);break;case"video":case"audio":for(l=0;l<Si.length;l++)Ae(Si[l],e);break;case"image":Ae("error",e),Ae("load",e);break;case"details":Ae("toggle",e);break;case"embed":case"source":case"link":Ae("error",e),Ae("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in n)if(n.hasOwnProperty(D)&&(l=n[D],l!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Ue(e,t,D,l,n,null)}return;default:if(Zr(t)){for(q in n)n.hasOwnProperty(q)&&(l=n[q],l!==void 0&&Rs(e,t,q,l,n,void 0));return}}for(p in n)n.hasOwnProperty(p)&&(l=n[p],l!=null&&Ue(e,t,p,l,n,null))}function h0(e,t,n,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,o=null,d=null,p=null,S=null,D=null,q=null;for(H in n){var Q=n[H];if(n.hasOwnProperty(H)&&Q!=null)switch(H){case"checked":break;case"value":break;case"defaultValue":S=Q;default:l.hasOwnProperty(H)||Ue(e,t,H,null,l,Q)}}for(var z in l){var H=l[z];if(Q=n[z],l.hasOwnProperty(z)&&(H!=null||Q!=null))switch(z){case"type":o=H;break;case"name":i=H;break;case"checked":D=H;break;case"defaultChecked":q=H;break;case"value":d=H;break;case"defaultValue":p=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(c(137,t));break;default:H!==Q&&Ue(e,t,z,H,l,Q)}}Xr(e,d,p,S,D,q,o,i);return;case"select":H=d=p=z=null;for(o in n)if(S=n[o],n.hasOwnProperty(o)&&S!=null)switch(o){case"value":break;case"multiple":H=S;default:l.hasOwnProperty(o)||Ue(e,t,o,null,l,S)}for(i in l)if(o=l[i],S=n[i],l.hasOwnProperty(i)&&(o!=null||S!=null))switch(i){case"value":z=o;break;case"defaultValue":p=o;break;case"multiple":d=o;default:o!==S&&Ue(e,t,i,o,l,S)}t=p,n=d,l=H,z!=null?kl(e,!!n,z,!1):!!l!=!!n&&(t!=null?kl(e,!!n,t,!0):kl(e,!!n,n?[]:"",!1));return;case"textarea":H=z=null;for(p in n)if(i=n[p],n.hasOwnProperty(p)&&i!=null&&!l.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:Ue(e,t,p,null,l,i)}for(d in l)if(i=l[d],o=n[d],l.hasOwnProperty(d)&&(i!=null||o!=null))switch(d){case"value":z=i;break;case"defaultValue":H=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(c(91));break;default:i!==o&&Ue(e,t,d,i,l,o)}ff(e,z,H);return;case"option":for(var he in n)if(z=n[he],n.hasOwnProperty(he)&&z!=null&&!l.hasOwnProperty(he))switch(he){case"selected":e.selected=!1;break;default:Ue(e,t,he,null,l,z)}for(S in l)if(z=l[S],H=n[S],l.hasOwnProperty(S)&&z!==H&&(z!=null||H!=null))switch(S){case"selected":e.selected=z&&typeof z!="function"&&typeof z!="symbol";break;default:Ue(e,t,S,z,l,H)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var de in n)z=n[de],n.hasOwnProperty(de)&&z!=null&&!l.hasOwnProperty(de)&&Ue(e,t,de,null,l,z);for(D in l)if(z=l[D],H=n[D],l.hasOwnProperty(D)&&z!==H&&(z!=null||H!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(c(137,t));break;default:Ue(e,t,D,z,l,H)}return;default:if(Zr(t)){for(var He in n)z=n[He],n.hasOwnProperty(He)&&z!==void 0&&!l.hasOwnProperty(He)&&Rs(e,t,He,void 0,l,z);for(q in l)z=l[q],H=n[q],!l.hasOwnProperty(q)||z===H||z===void 0&&H===void 0||Rs(e,t,q,z,l,H);return}}for(var T in n)z=n[T],n.hasOwnProperty(T)&&z!=null&&!l.hasOwnProperty(T)&&Ue(e,t,T,null,l,z);for(Q in l)z=l[Q],H=n[Q],!l.hasOwnProperty(Q)||z===H||z==null&&H==null||Ue(e,t,Q,z,l,H)}var Os=null,Ms=null;function Ko(e){return e.nodeType===9?e:e.ownerDocument}function mh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function hh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Ds(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var zs=null;function v0(){var e=window.event;return e&&e.type==="popstate"?e===zs?!1:(zs=e,!0):(zs=null,!1)}var vh=typeof setTimeout=="function"?setTimeout:void 0,g0=typeof clearTimeout=="function"?clearTimeout:void 0,gh=typeof Promise=="function"?Promise:void 0,p0=typeof queueMicrotask=="function"?queueMicrotask:typeof gh<"u"?function(e){return gh.resolve(null).then(e).catch(y0)}:vh;function y0(e){setTimeout(function(){throw e})}function Zn(e){return e==="head"}function ph(e,t){var n=t,l=0,i=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(0<l&&8>l){n=l;var d=e.ownerDocument;if(n&1&&Ei(d.documentElement),n&2&&Ei(d.body),n&4)for(n=d.head,Ei(n),d=n.firstChild;d;){var p=d.nextSibling,S=d.nodeName;d[La]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&d.rel.toLowerCase()==="stylesheet"||n.removeChild(d),d=p}}if(i===0){e.removeChild(o),Oi(t);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=o}while(n);Oi(t)}function Us(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Us(n),qr(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function x0(e,t,n,l){for(;e.nodeType===1;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[La])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(o=e.getAttribute("rel"),o==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(o!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(o=e.getAttribute("src"),(o!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var o=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===o)return e}else return e;if(e=Xt(e.nextSibling),e===null)break}return null}function b0(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Xt(e.nextSibling),e===null))return null;return e}function Hs(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function S0(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var l=function(){t(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Xt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Bs=null;function yh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function xh(e,t,n){switch(t=Ko(n),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function Ei(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);qr(e)}var Vt=new Map,bh=new Set;function Jo(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var wn=Z.d;Z.d={f:w0,r:E0,D:N0,C:A0,L:T0,m:C0,X:_0,S:j0,M:R0};function w0(){var e=wn.f(),t=ko();return e||t}function E0(e){var t=Ul(e);t!==null&&t.tag===5&&t.type==="form"?qd(t):wn.r(e)}var va=typeof document>"u"?null:document;function Sh(e,t,n){var l=va;if(l&&typeof t=="string"&&t){var i=zt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),bh.has(i)||(bh.add(i),e={rel:e,crossOrigin:n,href:t},l.querySelector(i)===null&&(t=l.createElement("link"),st(t,"link",e),nt(t),l.head.appendChild(t)))}}function N0(e){wn.D(e),Sh("dns-prefetch",e,null)}function A0(e,t){wn.C(e,t),Sh("preconnect",e,t)}function T0(e,t,n){wn.L(e,t,n);var l=va;if(l&&e&&t){var i='link[rel="preload"][as="'+zt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+zt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+zt(n.imageSizes)+'"]')):i+='[href="'+zt(e)+'"]';var o=i;switch(t){case"style":o=ga(e);break;case"script":o=pa(e)}Vt.has(o)||(e=b({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Vt.set(o,e),l.querySelector(i)!==null||t==="style"&&l.querySelector(Ni(o))||t==="script"&&l.querySelector(Ai(o))||(t=l.createElement("link"),st(t,"link",e),nt(t),l.head.appendChild(t)))}}function C0(e,t){wn.m(e,t);var n=va;if(n&&e){var l=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+zt(l)+'"][href="'+zt(e)+'"]',o=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=pa(e)}if(!Vt.has(o)&&(e=b({rel:"modulepreload",href:e},t),Vt.set(o,e),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ai(o)))return}l=n.createElement("link"),st(l,"link",e),nt(l),n.head.appendChild(l)}}}function j0(e,t,n){wn.S(e,t,n);var l=va;if(l&&e){var i=Hl(l).hoistableStyles,o=ga(e);t=t||"default";var d=i.get(o);if(!d){var p={loading:0,preload:null};if(d=l.querySelector(Ni(o)))p.loading=5;else{e=b({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Vt.get(o))&&Ls(e,n);var S=d=l.createElement("link");nt(S),st(S,"link",e),S._p=new Promise(function(D,q){S.onload=D,S.onerror=q}),S.addEventListener("load",function(){p.loading|=1}),S.addEventListener("error",function(){p.loading|=2}),p.loading|=4,$o(d,t,l)}d={type:"stylesheet",instance:d,count:1,state:p},i.set(o,d)}}}function _0(e,t){wn.X(e,t);var n=va;if(n&&e){var l=Hl(n).hoistableScripts,i=pa(e),o=l.get(i);o||(o=n.querySelector(Ai(i)),o||(e=b({src:e,async:!0},t),(t=Vt.get(i))&&ks(e,t),o=n.createElement("script"),nt(o),st(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},l.set(i,o))}}function R0(e,t){wn.M(e,t);var n=va;if(n&&e){var l=Hl(n).hoistableScripts,i=pa(e),o=l.get(i);o||(o=n.querySelector(Ai(i)),o||(e=b({src:e,async:!0,type:"module"},t),(t=Vt.get(i))&&ks(e,t),o=n.createElement("script"),nt(o),st(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},l.set(i,o))}}function wh(e,t,n,l){var i=(i=ce.current)?Jo(i):null;if(!i)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=ga(n.href),n=Hl(i).hoistableStyles,l=n.get(t),l||(l={type:"style",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=ga(n.href);var o=Hl(i).hoistableStyles,d=o.get(e);if(d||(i=i.ownerDocument||i,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},o.set(e,d),(o=i.querySelector(Ni(e)))&&!o._p&&(d.instance=o,d.state.loading=5),Vt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Vt.set(e,n),o||O0(i,e,n,d.state))),t&&l===null)throw Error(c(528,""));return d}if(t&&l!==null)throw Error(c(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=pa(n),n=Hl(i).hoistableScripts,l=n.get(t),l||(l={type:"script",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function ga(e){return'href="'+zt(e)+'"'}function Ni(e){return'link[rel="stylesheet"]['+e+"]"}function Eh(e){return b({},e,{"data-precedence":e.precedence,precedence:null})}function O0(e,t,n,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),st(t,"link",n),nt(t),e.head.appendChild(t))}function pa(e){return'[src="'+zt(e)+'"]'}function Ai(e){return"script[async]"+e}function Nh(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+zt(n.href)+'"]');if(l)return t.instance=l,nt(l),l;var i=b({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),nt(l),st(l,"style",i),$o(l,n.precedence,e),t.instance=l;case"stylesheet":i=ga(n.href);var o=e.querySelector(Ni(i));if(o)return t.state.loading|=4,t.instance=o,nt(o),o;l=Eh(n),(i=Vt.get(i))&&Ls(l,i),o=(e.ownerDocument||e).createElement("link"),nt(o);var d=o;return d._p=new Promise(function(p,S){d.onload=p,d.onerror=S}),st(o,"link",l),t.state.loading|=4,$o(o,n.precedence,e),t.instance=o;case"script":return o=pa(n.src),(i=e.querySelector(Ai(o)))?(t.instance=i,nt(i),i):(l=n,(i=Vt.get(o))&&(l=b({},n),ks(l,i)),e=e.ownerDocument||e,i=e.createElement("script"),nt(i),st(i,"link",l),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,$o(l,n.precedence,e));return t.instance}function $o(e,t,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,o=i,d=0;d<l.length;d++){var p=l[d];if(p.dataset.precedence===t)o=p;else if(o!==i)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Ls(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function ks(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Wo=null;function Ah(e,t,n){if(Wo===null){var l=new Map,i=Wo=new Map;i.set(n,l)}else i=Wo,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(e))return l;for(l.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var o=n[i];if(!(o[La]||o[ft]||e==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!=="http://www.w3.org/2000/svg"){var d=o.getAttribute(t)||"";d=e+d;var p=l.get(d);p?p.push(o):l.set(d,[o])}}return l}function Th(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function M0(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Ch(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Ti=null;function D0(){}function z0(e,t,n){if(Ti===null)throw Error(c(475));var l=Ti;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=ga(n.href),o=e.querySelector(Ni(i));if(o){e=o._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Po.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=o,nt(o);return}o=e.ownerDocument||e,n=Eh(n),(i=Vt.get(i))&&Ls(n,i),o=o.createElement("link"),nt(o);var d=o;d._p=new Promise(function(p,S){d.onload=p,d.onerror=S}),st(o,"link",n),t.instance=o}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Po.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function U0(){if(Ti===null)throw Error(c(475));var e=Ti;return e.stylesheets&&e.count===0&&qs(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&qs(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Po(){if(this.count--,this.count===0){if(this.stylesheets)qs(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Fo=null;function qs(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Fo=new Map,t.forEach(H0,e),Fo=null,Po.call(e))}function H0(e,t){if(!(t.state.loading&4)){var n=Fo.get(e);if(n)var l=n.get(null);else{n=new Map,Fo.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<i.length;o++){var d=i[o];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(n.set(d.dataset.precedence,d),l=d)}l&&n.set(null,l)}i=t.instance,d=i.getAttribute("data-precedence"),o=n.get(d)||l,o===l&&n.set(null,i),n.set(d,i),this.count++,l=Po.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),o?o.parentNode.insertBefore(i,o.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Ci={$$typeof:B,Provider:null,Consumer:null,_currentValue:L,_currentValue2:L,_threadCount:0};function B0(e,t,n,l,i,o,d,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Hr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Hr(0),this.hiddenUpdates=Hr(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=o,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function jh(e,t,n,l,i,o,d,p,S,D,q,Q){return e=new B0(e,t,n,d,p,S,D,Q),t=1,o===!0&&(t|=24),o=At(3,null,null,t),e.current=o,o.stateNode=e,t=Sc(),t.refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:l,isDehydrated:n,cache:t},Ac(o),e}function _h(e){return e?(e=Jl,e):Jl}function Rh(e,t,n,l,i,o){i=_h(i),l.context===null?l.context=i:l.pendingContext=i,l=Dn(t),l.payload={element:n},o=o===void 0?null:o,o!==null&&(l.callback=o),n=zn(e,l,t),n!==null&&(Rt(n,e,t),li(n,e,t))}function Oh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Vs(e,t){Oh(e,t),(e=e.alternate)&&Oh(e,t)}function Mh(e){if(e.tag===13){var t=Kl(e,67108864);t!==null&&Rt(t,e,67108864),Vs(e,67108864)}}var Io=!0;function L0(e,t,n,l){var i=j.T;j.T=null;var o=Z.p;try{Z.p=2,Ys(e,t,n,l)}finally{Z.p=o,j.T=i}}function k0(e,t,n,l){var i=j.T;j.T=null;var o=Z.p;try{Z.p=8,Ys(e,t,n,l)}finally{Z.p=o,j.T=i}}function Ys(e,t,n,l){if(Io){var i=Gs(l);if(i===null)_s(e,t,l,er,n),zh(e,l);else if(V0(i,e,t,n,l))l.stopPropagation();else if(zh(e,l),t&4&&-1<q0.indexOf(e)){for(;i!==null;){var o=Ul(i);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var d=sl(o.pendingLanes);if(d!==0){var p=o;for(p.pendingLanes|=2,p.entangledLanes|=2;d;){var S=1<<31-Et(d);p.entanglements[1]|=S,d&=~S}nn(o),(Oe&6)===0&&(Bo=ut()+500,bi(0))}}break;case 13:p=Kl(o,2),p!==null&&Rt(p,o,2),ko(),Vs(o,2)}if(o=Gs(l),o===null&&_s(e,t,l,er,n),o===i)break;i=o}i!==null&&l.stopPropagation()}else _s(e,t,l,null,n)}}function Gs(e){return e=Jr(e),Xs(e)}var er=null;function Xs(e){if(er=null,e=zl(e),e!==null){var t=m(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=h(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return er=e,null}function Dh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ur()){case cl:return 2;case Ku:return 8;case Qi:case Tp:return 32;case Ju:return 268435456;default:return 32}default:return 32}}var Qs=!1,Kn=null,Jn=null,$n=null,ji=new Map,_i=new Map,Wn=[],q0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function zh(e,t){switch(e){case"focusin":case"focusout":Kn=null;break;case"dragenter":case"dragleave":Jn=null;break;case"mouseover":case"mouseout":$n=null;break;case"pointerover":case"pointerout":ji.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":_i.delete(t.pointerId)}}function Ri(e,t,n,l,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:l,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Ul(t),t!==null&&Mh(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function V0(e,t,n,l,i){switch(t){case"focusin":return Kn=Ri(Kn,e,t,n,l,i),!0;case"dragenter":return Jn=Ri(Jn,e,t,n,l,i),!0;case"mouseover":return $n=Ri($n,e,t,n,l,i),!0;case"pointerover":var o=i.pointerId;return ji.set(o,Ri(ji.get(o)||null,e,t,n,l,i)),!0;case"gotpointercapture":return o=i.pointerId,_i.set(o,Ri(_i.get(o)||null,e,t,n,l,i)),!0}return!1}function Uh(e){var t=zl(e.target);if(t!==null){var n=m(t);if(n!==null){if(t=n.tag,t===13){if(t=h(n),t!==null){e.blockedOn=t,zp(e.priority,function(){if(n.tag===13){var l=_t();l=Br(l);var i=Kl(n,l);i!==null&&Rt(i,n,l),Vs(n,l)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function tr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gs(e.nativeEvent);if(n===null){n=e.nativeEvent;var l=new n.constructor(n.type,n);Kr=l,n.target.dispatchEvent(l),Kr=null}else return t=Ul(n),t!==null&&Mh(t),e.blockedOn=n,!1;t.shift()}return!0}function Hh(e,t,n){tr(e)&&n.delete(t)}function Y0(){Qs=!1,Kn!==null&&tr(Kn)&&(Kn=null),Jn!==null&&tr(Jn)&&(Jn=null),$n!==null&&tr($n)&&($n=null),ji.forEach(Hh),_i.forEach(Hh)}function nr(e,t){e.blockedOn===t&&(e.blockedOn=null,Qs||(Qs=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Y0)))}var lr=null;function Bh(e){lr!==e&&(lr=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){lr===e&&(lr=null);for(var t=0;t<e.length;t+=3){var n=e[t],l=e[t+1],i=e[t+2];if(typeof l!="function"){if(Xs(l||n)===null)continue;break}var o=Ul(n);o!==null&&(e.splice(t,3),t-=3,Xc(o,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function Oi(e){function t(S){return nr(S,e)}Kn!==null&&nr(Kn,e),Jn!==null&&nr(Jn,e),$n!==null&&nr($n,e),ji.forEach(t),_i.forEach(t);for(var n=0;n<Wn.length;n++){var l=Wn[n];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Wn.length&&(n=Wn[0],n.blockedOn===null);)Uh(n),n.blockedOn===null&&Wn.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],o=n[l+1],d=i[pt]||null;if(typeof o=="function")d||Bh(n);else if(d){var p=null;if(o&&o.hasAttribute("formAction")){if(i=o,d=o[pt]||null)p=d.formAction;else if(Xs(i)!==null)continue}else p=d.action;typeof p=="function"?n[l+1]=p:(n.splice(l,3),l-=3),Bh(n)}}}function Zs(e){this._internalRoot=e}ar.prototype.render=Zs.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var n=t.current,l=_t();Rh(n,l,e,t,null,null)},ar.prototype.unmount=Zs.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Rh(e.current,2,null,e,null,null),ko(),t[Dl]=null}};function ar(e){this._internalRoot=e}ar.prototype.unstable_scheduleHydration=function(e){if(e){var t=Iu();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Wn.length&&t!==0&&t<Wn[n].priority;n++);Wn.splice(n,0,e),n===0&&Uh(e)}};var Lh=r.version;if(Lh!=="19.1.0")throw Error(c(527,Lh,"19.1.0"));Z.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=y(t),e=e!==null?v(e):null,e=e===null?null:e.stateNode,e};var G0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:j,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ir=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ir.isDisabled&&ir.supportsFiber)try{Ua=ir.inject(G0),wt=ir}catch{}}return Di.createRoot=function(e,t){if(!u(e))throw Error(c(299));var n=!1,l="",i=em,o=tm,d=nm,p=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(o=t.onCaughtError),t.onRecoverableError!==void 0&&(d=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=jh(e,1,!1,null,null,n,l,i,o,d,p,null),e[Dl]=t.current,js(e),new Zs(t)},Di.hydrateRoot=function(e,t,n){if(!u(e))throw Error(c(299));var l=!1,i="",o=em,d=tm,p=nm,S=null,D=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(o=n.onUncaughtError),n.onCaughtError!==void 0&&(d=n.onCaughtError),n.onRecoverableError!==void 0&&(p=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(S=n.unstable_transitionCallbacks),n.formState!==void 0&&(D=n.formState)),t=jh(e,1,!0,t,n??null,l,i,o,d,p,S,D),t.context=_h(null),n=t.current,l=_t(),l=Br(l),i=Dn(l),i.callback=null,zn(n,i,l),n=l,t.current.lanes=n,Ba(t,n),nn(t),e[Dl]=t.current,js(e),new ar(t)},Di.version="19.1.0",Di}var Jh;function I0(){if(Jh)return $s.exports;Jh=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),$s.exports=F0(),$s.exports}var ex=I0();/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tx=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),nx=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,s,c)=>c?c.toUpperCase():s.toLowerCase()),$h=a=>{const r=nx(a);return r.charAt(0).toUpperCase()+r.slice(1)},_v=(...a)=>a.filter((r,s,c)=>!!r&&r.trim()!==""&&c.indexOf(r)===s).join(" ").trim(),lx=a=>{for(const r in a)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var ax={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ix=x.forwardRef(({color:a="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:c,className:u="",children:m,iconNode:h,...g},y)=>x.createElement("svg",{ref:y,...ax,width:r,height:r,stroke:a,strokeWidth:c?Number(s)*24/Number(r):s,className:_v("lucide",u),...!m&&!lx(g)&&{"aria-hidden":"true"},...g},[...h.map(([v,b])=>x.createElement(v,b)),...Array.isArray(m)?m:[m]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=(a,r)=>{const s=x.forwardRef(({className:c,...u},m)=>x.createElement(ix,{ref:m,iconNode:r,className:_v(`lucide-${tx($h(a))}`,`lucide-${a}`,c),...u}));return s.displayName=$h(a),s};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ox=[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]],rx=De("arrow-down-right",ox);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cx=[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]],sx=De("arrow-up-right",cx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ux=[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]],uu=De("bot",ux);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fx=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],dx=De("chart-column",fx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mx=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],hx=De("check",mx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vx=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Rv=De("chevron-down",vx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gx=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],px=De("chevron-up",gx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yx=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],xx=De("clock",yx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bx=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],Sx=De("copy",bx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wx=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],Ex=De("dollar-sign",wx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nx=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]],Ax=De("download",Nx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tx=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],hr=De("file-text",Tx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cx=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],jx=De("image",Cx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _x=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Rx=De("mail",_x);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ox=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Mx=De("menu",Ox);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dx=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],wa=De("message-square",Dx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zx=[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]],Ux=De("mic",zx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hx=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],Ov=De("phone",Hx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bx=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],yr=De("plus",Bx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lx=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],fu=De("search",Lx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kx=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],qx=De("settings",kx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vx=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],Mv=De("square-pen",Vx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yx=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],Gx=De("tag",Yx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xx=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Dv=De("trash-2",Xx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qx=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]],Wh=De("trending-up",Qx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zx=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],Na=De("users",Zx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kx=[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]],Jx=De("video",Kx);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $x=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],zv=De("x",$x);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wx=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],du=De("zap",Wx);function Ph(a,r){if(typeof a=="function")return a(r);a!=null&&(a.current=r)}function Px(...a){return r=>{let s=!1;const c=a.map(u=>{const m=Ph(u,r);return!s&&typeof m=="function"&&(s=!0),m});if(s)return()=>{for(let u=0;u<c.length;u++){const m=c[u];typeof m=="function"?m():Ph(a[u],null)}}}}function Ze(...a){return x.useCallback(Px(...a),a)}function ja(a){const r=Fx(a),s=x.forwardRef((c,u)=>{const{children:m,...h}=c,g=x.Children.toArray(m),y=g.find(eb);if(y){const v=y.props.children,b=g.map(N=>N===y?x.Children.count(v)>1?x.Children.only(null):x.isValidElement(v)?v.props.children:null:N);return f.jsx(r,{...h,ref:u,children:x.isValidElement(v)?x.cloneElement(v,void 0,b):null})}return f.jsx(r,{...h,ref:u,children:m})});return s.displayName=`${a}.Slot`,s}var Uv=ja("Slot");function Fx(a){const r=x.forwardRef((s,c)=>{const{children:u,...m}=s,h=x.isValidElement(u)?nb(u):void 0,g=Ze(h,c);if(x.isValidElement(u)){const y=tb(m,u.props);return u.type!==x.Fragment&&(y.ref=g),x.cloneElement(u,y)}return x.Children.count(u)>1?x.Children.only(null):null});return r.displayName=`${a}.SlotClone`,r}var Ix=Symbol("radix.slottable");function eb(a){return x.isValidElement(a)&&typeof a.type=="function"&&"__radixId"in a.type&&a.type.__radixId===Ix}function tb(a,r){const s={...r};for(const c in r){const u=a[c],m=r[c];/^on[A-Z]/.test(c)?u&&m?s[c]=(...g)=>{const y=m(...g);return u(...g),y}:u&&(s[c]=u):c==="style"?s[c]={...u,...m}:c==="className"&&(s[c]=[u,m].filter(Boolean).join(" "))}return{...a,...s}}function nb(a){var c,u;let r=(c=Object.getOwnPropertyDescriptor(a.props,"ref"))==null?void 0:c.get,s=r&&"isReactWarning"in r&&r.isReactWarning;return s?a.ref:(r=(u=Object.getOwnPropertyDescriptor(a,"ref"))==null?void 0:u.get,s=r&&"isReactWarning"in r&&r.isReactWarning,s?a.props.ref:a.props.ref||a.ref)}function Hv(a){var r,s,c="";if(typeof a=="string"||typeof a=="number")c+=a;else if(typeof a=="object")if(Array.isArray(a)){var u=a.length;for(r=0;r<u;r++)a[r]&&(s=Hv(a[r]))&&(c&&(c+=" "),c+=s)}else for(s in a)a[s]&&(c&&(c+=" "),c+=s);return c}function Bv(){for(var a,r,s=0,c="",u=arguments.length;s<u;s++)(a=arguments[s])&&(r=Hv(a))&&(c&&(c+=" "),c+=r);return c}const Fh=a=>typeof a=="boolean"?`${a}`:a===0?"0":a,Ih=Bv,Lv=(a,r)=>s=>{var c;if((r==null?void 0:r.variants)==null)return Ih(a,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:u,defaultVariants:m}=r,h=Object.keys(u).map(v=>{const b=s==null?void 0:s[v],N=m==null?void 0:m[v];if(b===null)return null;const C=Fh(b)||Fh(N);return u[v][C]}),g=s&&Object.entries(s).reduce((v,b)=>{let[N,C]=b;return C===void 0||(v[N]=C),v},{}),y=r==null||(c=r.compoundVariants)===null||c===void 0?void 0:c.reduce((v,b)=>{let{class:N,className:C,...R}=b;return Object.entries(R).every(M=>{let[E,_]=M;return Array.isArray(_)?_.includes({...m,...g}[E]):{...m,...g}[E]===_})?[...v,N,C]:v},[]);return Ih(a,h,y,s==null?void 0:s.class,s==null?void 0:s.className)},_u="-",lb=a=>{const r=ib(a),{conflictingClassGroups:s,conflictingClassGroupModifiers:c}=a;return{getClassGroupId:h=>{const g=h.split(_u);return g[0]===""&&g.length!==1&&g.shift(),kv(g,r)||ab(h)},getConflictingClassGroupIds:(h,g)=>{const y=s[h]||[];return g&&c[h]?[...y,...c[h]]:y}}},kv=(a,r)=>{var h;if(a.length===0)return r.classGroupId;const s=a[0],c=r.nextPart.get(s),u=c?kv(a.slice(1),c):void 0;if(u)return u;if(r.validators.length===0)return;const m=a.join(_u);return(h=r.validators.find(({validator:g})=>g(m)))==null?void 0:h.classGroupId},ev=/^\[(.+)\]$/,ab=a=>{if(ev.test(a)){const r=ev.exec(a)[1],s=r==null?void 0:r.substring(0,r.indexOf(":"));if(s)return"arbitrary.."+s}},ib=a=>{const{theme:r,classGroups:s}=a,c={nextPart:new Map,validators:[]};for(const u in s)mu(s[u],c,u,r);return c},mu=(a,r,s,c)=>{a.forEach(u=>{if(typeof u=="string"){const m=u===""?r:tv(r,u);m.classGroupId=s;return}if(typeof u=="function"){if(ob(u)){mu(u(c),r,s,c);return}r.validators.push({validator:u,classGroupId:s});return}Object.entries(u).forEach(([m,h])=>{mu(h,tv(r,m),s,c)})})},tv=(a,r)=>{let s=a;return r.split(_u).forEach(c=>{s.nextPart.has(c)||s.nextPart.set(c,{nextPart:new Map,validators:[]}),s=s.nextPart.get(c)}),s},ob=a=>a.isThemeGetter,rb=a=>{if(a<1)return{get:()=>{},set:()=>{}};let r=0,s=new Map,c=new Map;const u=(m,h)=>{s.set(m,h),r++,r>a&&(r=0,c=s,s=new Map)};return{get(m){let h=s.get(m);if(h!==void 0)return h;if((h=c.get(m))!==void 0)return u(m,h),h},set(m,h){s.has(m)?s.set(m,h):u(m,h)}}},hu="!",vu=":",cb=vu.length,sb=a=>{const{prefix:r,experimentalParseClassName:s}=a;let c=u=>{const m=[];let h=0,g=0,y=0,v;for(let M=0;M<u.length;M++){let E=u[M];if(h===0&&g===0){if(E===vu){m.push(u.slice(y,M)),y=M+cb;continue}if(E==="/"){v=M;continue}}E==="["?h++:E==="]"?h--:E==="("?g++:E===")"&&g--}const b=m.length===0?u:u.substring(y),N=ub(b),C=N!==b,R=v&&v>y?v-y:void 0;return{modifiers:m,hasImportantModifier:C,baseClassName:N,maybePostfixModifierPosition:R}};if(r){const u=r+vu,m=c;c=h=>h.startsWith(u)?m(h.substring(u.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:h,maybePostfixModifierPosition:void 0}}if(s){const u=c;c=m=>s({className:m,parseClassName:u})}return c},ub=a=>a.endsWith(hu)?a.substring(0,a.length-1):a.startsWith(hu)?a.substring(1):a,fb=a=>{const r=Object.fromEntries(a.orderSensitiveModifiers.map(c=>[c,!0]));return c=>{if(c.length<=1)return c;const u=[];let m=[];return c.forEach(h=>{h[0]==="["||r[h]?(u.push(...m.sort(),h),m=[]):m.push(h)}),u.push(...m.sort()),u}},db=a=>({cache:rb(a.cacheSize),parseClassName:sb(a),sortModifiers:fb(a),...lb(a)}),mb=/\s+/,hb=(a,r)=>{const{parseClassName:s,getClassGroupId:c,getConflictingClassGroupIds:u,sortModifiers:m}=r,h=[],g=a.trim().split(mb);let y="";for(let v=g.length-1;v>=0;v-=1){const b=g[v],{isExternal:N,modifiers:C,hasImportantModifier:R,baseClassName:M,maybePostfixModifierPosition:E}=s(b);if(N){y=b+(y.length>0?" "+y:y);continue}let _=!!E,k=c(_?M.substring(0,E):M);if(!k){if(!_){y=b+(y.length>0?" "+y:y);continue}if(k=c(M),!k){y=b+(y.length>0?" "+y:y);continue}_=!1}const U=m(C).join(":"),B=R?U+hu:U,K=B+k;if(h.includes(K))continue;h.push(K);const V=u(k,_);for(let ee=0;ee<V.length;++ee){const P=V[ee];h.push(B+P)}y=b+(y.length>0?" "+y:y)}return y};function vb(){let a=0,r,s,c="";for(;a<arguments.length;)(r=arguments[a++])&&(s=qv(r))&&(c&&(c+=" "),c+=s);return c}const qv=a=>{if(typeof a=="string")return a;let r,s="";for(let c=0;c<a.length;c++)a[c]&&(r=qv(a[c]))&&(s&&(s+=" "),s+=r);return s};function gb(a,...r){let s,c,u,m=h;function h(y){const v=r.reduce((b,N)=>N(b),a());return s=db(v),c=s.cache.get,u=s.cache.set,m=g,g(y)}function g(y){const v=c(y);if(v)return v;const b=hb(y,s);return u(y,b),b}return function(){return m(vb.apply(null,arguments))}}const tt=a=>{const r=s=>s[a]||[];return r.isThemeGetter=!0,r},Vv=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Yv=/^\((?:(\w[\w-]*):)?(.+)\)$/i,pb=/^\d+\/\d+$/,yb=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,xb=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,bb=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Sb=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,wb=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ya=a=>pb.test(a),be=a=>!!a&&!Number.isNaN(Number(a)),Fn=a=>!!a&&Number.isInteger(Number(a)),Is=a=>a.endsWith("%")&&be(a.slice(0,-1)),En=a=>yb.test(a),Eb=()=>!0,Nb=a=>xb.test(a)&&!bb.test(a),Gv=()=>!1,Ab=a=>Sb.test(a),Tb=a=>wb.test(a),Cb=a=>!le(a)&&!ae(a),jb=a=>Ra(a,Zv,Gv),le=a=>Vv.test(a),Cl=a=>Ra(a,Kv,Nb),eu=a=>Ra(a,Db,be),nv=a=>Ra(a,Xv,Gv),_b=a=>Ra(a,Qv,Tb),or=a=>Ra(a,Jv,Ab),ae=a=>Yv.test(a),zi=a=>Oa(a,Kv),Rb=a=>Oa(a,zb),lv=a=>Oa(a,Xv),Ob=a=>Oa(a,Zv),Mb=a=>Oa(a,Qv),rr=a=>Oa(a,Jv,!0),Ra=(a,r,s)=>{const c=Vv.exec(a);return c?c[1]?r(c[1]):s(c[2]):!1},Oa=(a,r,s=!1)=>{const c=Yv.exec(a);return c?c[1]?r(c[1]):s:!1},Xv=a=>a==="position"||a==="percentage",Qv=a=>a==="image"||a==="url",Zv=a=>a==="length"||a==="size"||a==="bg-size",Kv=a=>a==="length",Db=a=>a==="number",zb=a=>a==="family-name",Jv=a=>a==="shadow",Ub=()=>{const a=tt("color"),r=tt("font"),s=tt("text"),c=tt("font-weight"),u=tt("tracking"),m=tt("leading"),h=tt("breakpoint"),g=tt("container"),y=tt("spacing"),v=tt("radius"),b=tt("shadow"),N=tt("inset-shadow"),C=tt("text-shadow"),R=tt("drop-shadow"),M=tt("blur"),E=tt("perspective"),_=tt("aspect"),k=tt("ease"),U=tt("animate"),B=()=>["auto","avoid","all","avoid-page","page","left","right","column"],K=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],V=()=>[...K(),ae,le],ee=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],$=()=>[ae,le,y],se=()=>[ya,"full","auto",...$()],J=()=>[Fn,"none","subgrid",ae,le],ue=()=>["auto",{span:["full",Fn,ae,le]},Fn,ae,le],re=()=>[Fn,"auto",ae,le],ve=()=>["auto","min","max","fr",ae,le],pe=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Y=()=>["start","end","center","stretch","center-safe","end-safe"],j=()=>["auto",...$()],Z=()=>[ya,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...$()],L=()=>[a,ae,le],ie=()=>[...K(),lv,nv,{position:[ae,le]}],w=()=>["no-repeat",{repeat:["","x","y","space","round"]}],G=()=>["auto","cover","contain",Ob,jb,{size:[ae,le]}],F=()=>[Is,zi,Cl],W=()=>["","none","full",v,ae,le],te=()=>["",be,zi,Cl],ge=()=>["solid","dashed","dotted","double"],ce=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],I=()=>[be,Is,lv,nv],fe=()=>["","none",M,ae,le],Re=()=>["none",be,ae,le],Te=()=>["none",be,ae,le],we=()=>[be,ae,le],Ee=()=>[ya,"full",...$()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[En],breakpoint:[En],color:[Eb],container:[En],"drop-shadow":[En],ease:["in","out","in-out"],font:[Cb],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[En],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[En],shadow:[En],spacing:["px",be],text:[En],"text-shadow":[En],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",ya,le,ae,_]}],container:["container"],columns:[{columns:[be,le,ae,g]}],"break-after":[{"break-after":B()}],"break-before":[{"break-before":B()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:V()}],overflow:[{overflow:ee()}],"overflow-x":[{"overflow-x":ee()}],"overflow-y":[{"overflow-y":ee()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:se()}],"inset-x":[{"inset-x":se()}],"inset-y":[{"inset-y":se()}],start:[{start:se()}],end:[{end:se()}],top:[{top:se()}],right:[{right:se()}],bottom:[{bottom:se()}],left:[{left:se()}],visibility:["visible","invisible","collapse"],z:[{z:[Fn,"auto",ae,le]}],basis:[{basis:[ya,"full","auto",g,...$()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[be,ya,"auto","initial","none",le]}],grow:[{grow:["",be,ae,le]}],shrink:[{shrink:["",be,ae,le]}],order:[{order:[Fn,"first","last","none",ae,le]}],"grid-cols":[{"grid-cols":J()}],"col-start-end":[{col:ue()}],"col-start":[{"col-start":re()}],"col-end":[{"col-end":re()}],"grid-rows":[{"grid-rows":J()}],"row-start-end":[{row:ue()}],"row-start":[{"row-start":re()}],"row-end":[{"row-end":re()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ve()}],"auto-rows":[{"auto-rows":ve()}],gap:[{gap:$()}],"gap-x":[{"gap-x":$()}],"gap-y":[{"gap-y":$()}],"justify-content":[{justify:[...pe(),"normal"]}],"justify-items":[{"justify-items":[...Y(),"normal"]}],"justify-self":[{"justify-self":["auto",...Y()]}],"align-content":[{content:["normal",...pe()]}],"align-items":[{items:[...Y(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Y(),{baseline:["","last"]}]}],"place-content":[{"place-content":pe()}],"place-items":[{"place-items":[...Y(),"baseline"]}],"place-self":[{"place-self":["auto",...Y()]}],p:[{p:$()}],px:[{px:$()}],py:[{py:$()}],ps:[{ps:$()}],pe:[{pe:$()}],pt:[{pt:$()}],pr:[{pr:$()}],pb:[{pb:$()}],pl:[{pl:$()}],m:[{m:j()}],mx:[{mx:j()}],my:[{my:j()}],ms:[{ms:j()}],me:[{me:j()}],mt:[{mt:j()}],mr:[{mr:j()}],mb:[{mb:j()}],ml:[{ml:j()}],"space-x":[{"space-x":$()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":$()}],"space-y-reverse":["space-y-reverse"],size:[{size:Z()}],w:[{w:[g,"screen",...Z()]}],"min-w":[{"min-w":[g,"screen","none",...Z()]}],"max-w":[{"max-w":[g,"screen","none","prose",{screen:[h]},...Z()]}],h:[{h:["screen","lh",...Z()]}],"min-h":[{"min-h":["screen","lh","none",...Z()]}],"max-h":[{"max-h":["screen","lh",...Z()]}],"font-size":[{text:["base",s,zi,Cl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[c,ae,eu]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Is,le]}],"font-family":[{font:[Rb,le,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[u,ae,le]}],"line-clamp":[{"line-clamp":[be,"none",ae,eu]}],leading:[{leading:[m,...$()]}],"list-image":[{"list-image":["none",ae,le]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ae,le]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:L()}],"text-color":[{text:L()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ge(),"wavy"]}],"text-decoration-thickness":[{decoration:[be,"from-font","auto",ae,Cl]}],"text-decoration-color":[{decoration:L()}],"underline-offset":[{"underline-offset":[be,"auto",ae,le]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:$()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ae,le]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ae,le]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ie()}],"bg-repeat":[{bg:w()}],"bg-size":[{bg:G()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Fn,ae,le],radial:["",ae,le],conic:[Fn,ae,le]},Mb,_b]}],"bg-color":[{bg:L()}],"gradient-from-pos":[{from:F()}],"gradient-via-pos":[{via:F()}],"gradient-to-pos":[{to:F()}],"gradient-from":[{from:L()}],"gradient-via":[{via:L()}],"gradient-to":[{to:L()}],rounded:[{rounded:W()}],"rounded-s":[{"rounded-s":W()}],"rounded-e":[{"rounded-e":W()}],"rounded-t":[{"rounded-t":W()}],"rounded-r":[{"rounded-r":W()}],"rounded-b":[{"rounded-b":W()}],"rounded-l":[{"rounded-l":W()}],"rounded-ss":[{"rounded-ss":W()}],"rounded-se":[{"rounded-se":W()}],"rounded-ee":[{"rounded-ee":W()}],"rounded-es":[{"rounded-es":W()}],"rounded-tl":[{"rounded-tl":W()}],"rounded-tr":[{"rounded-tr":W()}],"rounded-br":[{"rounded-br":W()}],"rounded-bl":[{"rounded-bl":W()}],"border-w":[{border:te()}],"border-w-x":[{"border-x":te()}],"border-w-y":[{"border-y":te()}],"border-w-s":[{"border-s":te()}],"border-w-e":[{"border-e":te()}],"border-w-t":[{"border-t":te()}],"border-w-r":[{"border-r":te()}],"border-w-b":[{"border-b":te()}],"border-w-l":[{"border-l":te()}],"divide-x":[{"divide-x":te()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":te()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ge(),"hidden","none"]}],"divide-style":[{divide:[...ge(),"hidden","none"]}],"border-color":[{border:L()}],"border-color-x":[{"border-x":L()}],"border-color-y":[{"border-y":L()}],"border-color-s":[{"border-s":L()}],"border-color-e":[{"border-e":L()}],"border-color-t":[{"border-t":L()}],"border-color-r":[{"border-r":L()}],"border-color-b":[{"border-b":L()}],"border-color-l":[{"border-l":L()}],"divide-color":[{divide:L()}],"outline-style":[{outline:[...ge(),"none","hidden"]}],"outline-offset":[{"outline-offset":[be,ae,le]}],"outline-w":[{outline:["",be,zi,Cl]}],"outline-color":[{outline:L()}],shadow:[{shadow:["","none",b,rr,or]}],"shadow-color":[{shadow:L()}],"inset-shadow":[{"inset-shadow":["none",N,rr,or]}],"inset-shadow-color":[{"inset-shadow":L()}],"ring-w":[{ring:te()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:L()}],"ring-offset-w":[{"ring-offset":[be,Cl]}],"ring-offset-color":[{"ring-offset":L()}],"inset-ring-w":[{"inset-ring":te()}],"inset-ring-color":[{"inset-ring":L()}],"text-shadow":[{"text-shadow":["none",C,rr,or]}],"text-shadow-color":[{"text-shadow":L()}],opacity:[{opacity:[be,ae,le]}],"mix-blend":[{"mix-blend":[...ce(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ce()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[be]}],"mask-image-linear-from-pos":[{"mask-linear-from":I()}],"mask-image-linear-to-pos":[{"mask-linear-to":I()}],"mask-image-linear-from-color":[{"mask-linear-from":L()}],"mask-image-linear-to-color":[{"mask-linear-to":L()}],"mask-image-t-from-pos":[{"mask-t-from":I()}],"mask-image-t-to-pos":[{"mask-t-to":I()}],"mask-image-t-from-color":[{"mask-t-from":L()}],"mask-image-t-to-color":[{"mask-t-to":L()}],"mask-image-r-from-pos":[{"mask-r-from":I()}],"mask-image-r-to-pos":[{"mask-r-to":I()}],"mask-image-r-from-color":[{"mask-r-from":L()}],"mask-image-r-to-color":[{"mask-r-to":L()}],"mask-image-b-from-pos":[{"mask-b-from":I()}],"mask-image-b-to-pos":[{"mask-b-to":I()}],"mask-image-b-from-color":[{"mask-b-from":L()}],"mask-image-b-to-color":[{"mask-b-to":L()}],"mask-image-l-from-pos":[{"mask-l-from":I()}],"mask-image-l-to-pos":[{"mask-l-to":I()}],"mask-image-l-from-color":[{"mask-l-from":L()}],"mask-image-l-to-color":[{"mask-l-to":L()}],"mask-image-x-from-pos":[{"mask-x-from":I()}],"mask-image-x-to-pos":[{"mask-x-to":I()}],"mask-image-x-from-color":[{"mask-x-from":L()}],"mask-image-x-to-color":[{"mask-x-to":L()}],"mask-image-y-from-pos":[{"mask-y-from":I()}],"mask-image-y-to-pos":[{"mask-y-to":I()}],"mask-image-y-from-color":[{"mask-y-from":L()}],"mask-image-y-to-color":[{"mask-y-to":L()}],"mask-image-radial":[{"mask-radial":[ae,le]}],"mask-image-radial-from-pos":[{"mask-radial-from":I()}],"mask-image-radial-to-pos":[{"mask-radial-to":I()}],"mask-image-radial-from-color":[{"mask-radial-from":L()}],"mask-image-radial-to-color":[{"mask-radial-to":L()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":K()}],"mask-image-conic-pos":[{"mask-conic":[be]}],"mask-image-conic-from-pos":[{"mask-conic-from":I()}],"mask-image-conic-to-pos":[{"mask-conic-to":I()}],"mask-image-conic-from-color":[{"mask-conic-from":L()}],"mask-image-conic-to-color":[{"mask-conic-to":L()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ie()}],"mask-repeat":[{mask:w()}],"mask-size":[{mask:G()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ae,le]}],filter:[{filter:["","none",ae,le]}],blur:[{blur:fe()}],brightness:[{brightness:[be,ae,le]}],contrast:[{contrast:[be,ae,le]}],"drop-shadow":[{"drop-shadow":["","none",R,rr,or]}],"drop-shadow-color":[{"drop-shadow":L()}],grayscale:[{grayscale:["",be,ae,le]}],"hue-rotate":[{"hue-rotate":[be,ae,le]}],invert:[{invert:["",be,ae,le]}],saturate:[{saturate:[be,ae,le]}],sepia:[{sepia:["",be,ae,le]}],"backdrop-filter":[{"backdrop-filter":["","none",ae,le]}],"backdrop-blur":[{"backdrop-blur":fe()}],"backdrop-brightness":[{"backdrop-brightness":[be,ae,le]}],"backdrop-contrast":[{"backdrop-contrast":[be,ae,le]}],"backdrop-grayscale":[{"backdrop-grayscale":["",be,ae,le]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[be,ae,le]}],"backdrop-invert":[{"backdrop-invert":["",be,ae,le]}],"backdrop-opacity":[{"backdrop-opacity":[be,ae,le]}],"backdrop-saturate":[{"backdrop-saturate":[be,ae,le]}],"backdrop-sepia":[{"backdrop-sepia":["",be,ae,le]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":$()}],"border-spacing-x":[{"border-spacing-x":$()}],"border-spacing-y":[{"border-spacing-y":$()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ae,le]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[be,"initial",ae,le]}],ease:[{ease:["linear","initial",k,ae,le]}],delay:[{delay:[be,ae,le]}],animate:[{animate:["none",U,ae,le]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[E,ae,le]}],"perspective-origin":[{"perspective-origin":V()}],rotate:[{rotate:Re()}],"rotate-x":[{"rotate-x":Re()}],"rotate-y":[{"rotate-y":Re()}],"rotate-z":[{"rotate-z":Re()}],scale:[{scale:Te()}],"scale-x":[{"scale-x":Te()}],"scale-y":[{"scale-y":Te()}],"scale-z":[{"scale-z":Te()}],"scale-3d":["scale-3d"],skew:[{skew:we()}],"skew-x":[{"skew-x":we()}],"skew-y":[{"skew-y":we()}],transform:[{transform:[ae,le,"","none","gpu","cpu"]}],"transform-origin":[{origin:V()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ee()}],"translate-x":[{"translate-x":Ee()}],"translate-y":[{"translate-y":Ee()}],"translate-z":[{"translate-z":Ee()}],"translate-none":["translate-none"],accent:[{accent:L()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:L()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ae,le]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":$()}],"scroll-mx":[{"scroll-mx":$()}],"scroll-my":[{"scroll-my":$()}],"scroll-ms":[{"scroll-ms":$()}],"scroll-me":[{"scroll-me":$()}],"scroll-mt":[{"scroll-mt":$()}],"scroll-mr":[{"scroll-mr":$()}],"scroll-mb":[{"scroll-mb":$()}],"scroll-ml":[{"scroll-ml":$()}],"scroll-p":[{"scroll-p":$()}],"scroll-px":[{"scroll-px":$()}],"scroll-py":[{"scroll-py":$()}],"scroll-ps":[{"scroll-ps":$()}],"scroll-pe":[{"scroll-pe":$()}],"scroll-pt":[{"scroll-pt":$()}],"scroll-pr":[{"scroll-pr":$()}],"scroll-pb":[{"scroll-pb":$()}],"scroll-pl":[{"scroll-pl":$()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ae,le]}],fill:[{fill:["none",...L()]}],"stroke-w":[{stroke:[be,zi,Cl,eu]}],stroke:[{stroke:["none",...L()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Hb=gb(Ub);function $e(...a){return Hb(Bv(a))}const Bb=Lv("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Qe({className:a,variant:r,size:s,asChild:c=!1,...u}){const m=c?Uv:"button";return f.jsx(m,{"data-slot":"button",className:$e(Bb({variant:r,size:s,className:a})),...u})}const Lb=({children:a,currentPage:r,onPageChange:s})=>{const[c,u]=x.useState(!1),m=[{id:"dashboard",label:"Dashboard",icon:dx},{id:"conversations",label:"Conversas",icon:wa},{id:"contacts",label:"Contatos",icon:Na},{id:"templates",label:"Templates",icon:hr},{id:"automation",label:"Automação",icon:uu},{id:"whatsapp",label:"WhatsApp",icon:Ov},{id:"ai-analysis",label:"Análise IA",icon:du},{id:"settings",label:"Configurações",icon:qx}];return f.jsxs("div",{className:"min-h-screen bg-gray-50",children:[c&&f.jsx("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>u(!1)}),f.jsxs("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${c?"translate-x-0":"-translate-x-full"}`,children:[f.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b",children:[f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx("div",{className:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center",children:f.jsx(wa,{className:"w-5 h-5 text-white"})}),f.jsx("span",{className:"text-xl font-bold text-gray-900",children:"WhatsApp AI"})]}),f.jsx(Qe,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>u(!1),children:f.jsx(zv,{className:"w-5 h-5"})})]}),f.jsx("nav",{className:"mt-6",children:m.map(h=>{const g=h.icon;return f.jsxs("button",{onClick:()=>{s(h.id),u(!1)},className:`w-full flex items-center px-6 py-3 text-left transition-colors duration-200 ${r===h.id?"bg-green-50 text-green-700 border-r-2 border-green-500":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[f.jsx(g,{className:"w-5 h-5 mr-3"}),h.label]},h.id)})}),f.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t",children:f.jsxs("div",{className:"flex items-center space-x-3",children:[f.jsx("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:f.jsx("span",{className:"text-sm font-medium text-gray-600",children:"U"})}),f.jsxs("div",{children:[f.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Usuário"}),f.jsx("p",{className:"text-xs text-gray-500",children:"Administrador"})]})]})})]}),f.jsxs("div",{className:"lg:pl-64",children:[f.jsxs("div",{className:"sticky top-0 z-30 flex items-center justify-between h-16 px-6 bg-white border-b",children:[f.jsx(Qe,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>u(!0),children:f.jsx(Mx,{className:"w-5 h-5"})}),f.jsx("div",{className:"flex items-center space-x-4",children:f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),f.jsx("span",{className:"text-sm text-gray-600",children:"Sistema Online"})]})})]}),f.jsx("main",{className:"p-6",children:a})]})]})};function Kt({className:a,...r}){return f.jsx("div",{"data-slot":"card",className:$e("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...r})}function tl({className:a,...r}){return f.jsx("div",{"data-slot":"card-header",className:$e("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...r})}function Ea({className:a,...r}){return f.jsx("div",{"data-slot":"card-title",className:$e("leading-none font-semibold",a),...r})}function gu({className:a,...r}){return f.jsx("div",{"data-slot":"card-description",className:$e("text-muted-foreground text-sm",a),...r})}function Jt({className:a,...r}){return f.jsx("div",{"data-slot":"card-content",className:$e("px-6",a),...r})}const kb=Lv("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function qi({className:a,variant:r,asChild:s=!1,...c}){const u=s?Uv:"span";return f.jsx(u,{"data-slot":"badge",className:$e(kb({variant:r}),a),...c})}const av=()=>{var y;const[a,r]=x.useState({totalContacts:0,newContacts:0,qualifiedContacts:0,convertedContacts:0,totalDealValue:0}),[s,c]=x.useState([]),[u,m]=x.useState(!0);x.useEffect(()=>{h()},[]);const h=async()=>{try{const v=await fetch("/api/contacts/stats");if(v.ok){const N=await v.json();r(N)}const b=await fetch("/api/whatsapp/messages?per_page=5");if(b.ok){const N=await b.json();c(N.messages||[])}}catch(v){console.error("Erro ao buscar dados do dashboard:",v)}finally{m(!1)}},g=[{title:"Total de Contatos",value:a.totalContacts,change:"+12%",changeType:"positive",icon:Na,description:"Contatos cadastrados"},{title:"Novos Contatos",value:a.newContacts,change:"+8%",changeType:"positive",icon:Wh,description:"Últimos 30 dias"},{title:"Leads Qualificados",value:a.qualifiedContacts,change:"+15%",changeType:"positive",icon:du,description:"Prontos para conversão"},{title:"Valor Total",value:`R$ ${((y=a.totalDealValue)==null?void 0:y.toLocaleString("pt-BR"))||"0"}`,change:"+23%",changeType:"positive",icon:Wh,description:"Em negociações"}];return u?f.jsx("div",{className:"space-y-6",children:f.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[1,2,3,4].map(v=>f.jsxs(Kt,{className:"animate-pulse",children:[f.jsx(tl,{className:"pb-2",children:f.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"})}),f.jsxs(Jt,{children:[f.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/2 mb-2"}),f.jsx("div",{className:"h-3 bg-gray-200 rounded w-full"})]})]},v))})}):f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{children:[f.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),f.jsx("p",{className:"text-gray-600",children:"Visão geral do seu WhatsApp AI Agent"})]}),f.jsx(Qe,{onClick:h,children:"Atualizar Dados"})]}),f.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:g.map((v,b)=>{const N=v.icon;return f.jsxs(Kt,{className:"hover:shadow-lg transition-shadow duration-200",children:[f.jsxs(tl,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[f.jsx(Ea,{className:"text-sm font-medium text-gray-600",children:v.title}),f.jsx(N,{className:"h-4 w-4 text-gray-400"})]}),f.jsxs(Jt,{children:[f.jsx("div",{className:"text-2xl font-bold text-gray-900",children:v.value}),f.jsxs("div",{className:"flex items-center space-x-2 text-xs",children:[f.jsxs(qi,{variant:v.changeType==="positive"?"default":"destructive",className:"flex items-center space-x-1",children:[v.changeType==="positive"?f.jsx(sx,{className:"h-3 w-3"}):f.jsx(rx,{className:"h-3 w-3"}),f.jsx("span",{children:v.change})]}),f.jsx("span",{className:"text-gray-500",children:v.description})]})]})]},b)})}),f.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[f.jsxs(Kt,{children:[f.jsxs(tl,{children:[f.jsxs(Ea,{className:"flex items-center space-x-2",children:[f.jsx(wa,{className:"h-5 w-5"}),f.jsx("span",{children:"Mensagens Recentes"})]}),f.jsx(gu,{children:"Últimas mensagens recebidas"})]}),f.jsx(Jt,{children:f.jsx("div",{className:"space-y-4",children:s.length>0?s.map((v,b)=>f.jsxs("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[f.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:f.jsx(wa,{className:"w-4 h-4 text-green-600"})}),f.jsxs("div",{className:"flex-1 min-w-0",children:[f.jsxs("p",{className:"text-sm font-medium text-gray-900",children:["Contato #",v.contact_id]}),f.jsx("p",{className:"text-sm text-gray-600 truncate",children:v.content}),f.jsx("p",{className:"text-xs text-gray-400",children:new Date(v.timestamp).toLocaleString("pt-BR")})]}),f.jsx(qi,{variant:v.direction==="inbound"?"default":"secondary",children:v.direction==="inbound"?"Recebida":"Enviada"})]},b)):f.jsxs("div",{className:"text-center py-8 text-gray-500",children:[f.jsx(wa,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),f.jsx("p",{children:"Nenhuma mensagem recente"})]})})})]}),f.jsxs(Kt,{children:[f.jsxs(tl,{children:[f.jsxs(Ea,{className:"flex items-center space-x-2",children:[f.jsx(uu,{className:"h-5 w-5"}),f.jsx("span",{children:"Ações Rápidas"})]}),f.jsx(gu,{children:"Ferramentas e configurações"})]}),f.jsx(Jt,{children:f.jsxs("div",{className:"space-y-3",children:[f.jsxs(Qe,{className:"w-full justify-start",variant:"outline",children:[f.jsx(Na,{className:"h-4 w-4 mr-2"}),"Gerenciar Contatos"]}),f.jsxs(Qe,{className:"w-full justify-start",variant:"outline",children:[f.jsx(wa,{className:"h-4 w-4 mr-2"}),"Criar Template"]}),f.jsxs(Qe,{className:"w-full justify-start",variant:"outline",children:[f.jsx(uu,{className:"h-4 w-4 mr-2"}),"Configurar Automação"]}),f.jsxs(Qe,{className:"w-full justify-start",variant:"outline",children:[f.jsx(du,{className:"h-4 w-4 mr-2"}),"Análise de IA"]})]})})]})]}),f.jsxs(Kt,{children:[f.jsx(tl,{children:f.jsxs(Ea,{className:"flex items-center space-x-2",children:[f.jsx(xx,{className:"h-5 w-5"}),f.jsx("span",{children:"Status do Sistema"})]})}),f.jsx(Jt,{children:f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[f.jsxs("div",{className:"flex items-center space-x-3",children:[f.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),f.jsxs("div",{children:[f.jsx("p",{className:"text-sm font-medium",children:"WhatsApp API"}),f.jsx("p",{className:"text-xs text-gray-500",children:"Conectado"})]})]}),f.jsxs("div",{className:"flex items-center space-x-3",children:[f.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),f.jsxs("div",{children:[f.jsx("p",{className:"text-sm font-medium",children:"Automação"}),f.jsx("p",{className:"text-xs text-gray-500",children:"Ativa"})]})]}),f.jsxs("div",{className:"flex items-center space-x-3",children:[f.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),f.jsxs("div",{children:[f.jsx("p",{className:"text-sm font-medium",children:"Análise IA"}),f.jsx("p",{className:"text-xs text-gray-500",children:"Funcionando"})]})]})]})})]})]})};function Nn({className:a,type:r,...s}){return f.jsx("input",{type:r,"data-slot":"input",className:$e("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...s})}function qe(a,r,{checkForDefaultPrevented:s=!0}={}){return function(u){if(a==null||a(u),s===!1||!u.defaultPrevented)return r==null?void 0:r(u)}}function qb(a,r){const s=x.createContext(r),c=m=>{const{children:h,...g}=m,y=x.useMemo(()=>g,Object.values(g));return f.jsx(s.Provider,{value:y,children:h})};c.displayName=a+"Provider";function u(m){const h=x.useContext(s);if(h)return h;if(r!==void 0)return r;throw new Error(`\`${m}\` must be used within \`${a}\``)}return[c,u]}function Ar(a,r=[]){let s=[];function c(m,h){const g=x.createContext(h),y=s.length;s=[...s,h];const v=N=>{var k;const{scope:C,children:R,...M}=N,E=((k=C==null?void 0:C[a])==null?void 0:k[y])||g,_=x.useMemo(()=>M,Object.values(M));return f.jsx(E.Provider,{value:_,children:R})};v.displayName=m+"Provider";function b(N,C){var E;const R=((E=C==null?void 0:C[a])==null?void 0:E[y])||g,M=x.useContext(R);if(M)return M;if(h!==void 0)return h;throw new Error(`\`${N}\` must be used within \`${m}\``)}return[v,b]}const u=()=>{const m=s.map(h=>x.createContext(h));return function(g){const y=(g==null?void 0:g[a])||m;return x.useMemo(()=>({[`__scope${a}`]:{...g,[a]:y}}),[g,y])}};return u.scopeName=a,[c,Vb(u,...r)]}function Vb(...a){const r=a[0];if(a.length===1)return r;const s=()=>{const c=a.map(u=>({useScope:u(),scopeName:u.scopeName}));return function(m){const h=c.reduce((g,{useScope:y,scopeName:v})=>{const N=y(m)[`__scope${v}`];return{...g,...N}},{});return x.useMemo(()=>({[`__scope${r.scopeName}`]:h}),[h])}};return s.scopeName=r.scopeName,s}var ht=globalThis!=null&&globalThis.document?x.useLayoutEffect:()=>{},Yb=Cv[" useId ".trim().toString()]||(()=>{}),Gb=0;function Aa(a){const[r,s]=x.useState(Yb());return ht(()=>{s(c=>c??String(Gb++))},[a]),a||(r?`radix-${r}`:"")}var Xb=Cv[" useInsertionEffect ".trim().toString()]||ht;function pu({prop:a,defaultProp:r,onChange:s=()=>{},caller:c}){const[u,m,h]=Qb({defaultProp:r,onChange:s}),g=a!==void 0,y=g?a:u;{const b=x.useRef(a!==void 0);x.useEffect(()=>{const N=b.current;N!==g&&console.warn(`${c} is changing from ${N?"controlled":"uncontrolled"} to ${g?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),b.current=g},[g,c])}const v=x.useCallback(b=>{var N;if(g){const C=Zb(b)?b(a):b;C!==a&&((N=h.current)==null||N.call(h,C))}else m(b)},[g,a,m,h]);return[y,v]}function Qb({defaultProp:a,onChange:r}){const[s,c]=x.useState(a),u=x.useRef(s),m=x.useRef(r);return Xb(()=>{m.current=r},[r]),x.useEffect(()=>{var h;u.current!==s&&((h=m.current)==null||h.call(m,s),u.current=s)},[s,u]),[s,c,m]}function Zb(a){return typeof a=="function"}var Gi=jv();const Kb=Tv(Gi);var Jb=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Le=Jb.reduce((a,r)=>{const s=ja(`Primitive.${r}`),c=x.forwardRef((u,m)=>{const{asChild:h,...g}=u,y=h?s:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(y,{...g,ref:m})});return c.displayName=`Primitive.${r}`,{...a,[r]:c}},{});function $b(a,r){a&&Gi.flushSync(()=>a.dispatchEvent(r))}function jl(a){const r=x.useRef(a);return x.useEffect(()=>{r.current=a}),x.useMemo(()=>(...s)=>{var c;return(c=r.current)==null?void 0:c.call(r,...s)},[])}function Wb(a,r=globalThis==null?void 0:globalThis.document){const s=jl(a);x.useEffect(()=>{const c=u=>{u.key==="Escape"&&s(u)};return r.addEventListener("keydown",c,{capture:!0}),()=>r.removeEventListener("keydown",c,{capture:!0})},[s,r])}var Pb="DismissableLayer",yu="dismissableLayer.update",Fb="dismissableLayer.pointerDownOutside",Ib="dismissableLayer.focusOutside",iv,$v=x.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ru=x.forwardRef((a,r)=>{const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:c,onPointerDownOutside:u,onFocusOutside:m,onInteractOutside:h,onDismiss:g,...y}=a,v=x.useContext($v),[b,N]=x.useState(null),C=(b==null?void 0:b.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,R]=x.useState({}),M=Ze(r,P=>N(P)),E=Array.from(v.layers),[_]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),k=E.indexOf(_),U=b?E.indexOf(b):-1,B=v.layersWithOutsidePointerEventsDisabled.size>0,K=U>=k,V=n1(P=>{const $=P.target,se=[...v.branches].some(J=>J.contains($));!K||se||(u==null||u(P),h==null||h(P),P.defaultPrevented||g==null||g())},C),ee=l1(P=>{const $=P.target;[...v.branches].some(J=>J.contains($))||(m==null||m(P),h==null||h(P),P.defaultPrevented||g==null||g())},C);return Wb(P=>{U===v.layers.size-1&&(c==null||c(P),!P.defaultPrevented&&g&&(P.preventDefault(),g()))},C),x.useEffect(()=>{if(b)return s&&(v.layersWithOutsidePointerEventsDisabled.size===0&&(iv=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(b)),v.layers.add(b),ov(),()=>{s&&v.layersWithOutsidePointerEventsDisabled.size===1&&(C.body.style.pointerEvents=iv)}},[b,C,s,v]),x.useEffect(()=>()=>{b&&(v.layers.delete(b),v.layersWithOutsidePointerEventsDisabled.delete(b),ov())},[b,v]),x.useEffect(()=>{const P=()=>R({});return document.addEventListener(yu,P),()=>document.removeEventListener(yu,P)},[]),f.jsx(Le.div,{...y,ref:M,style:{pointerEvents:B?K?"auto":"none":void 0,...a.style},onFocusCapture:qe(a.onFocusCapture,ee.onFocusCapture),onBlurCapture:qe(a.onBlurCapture,ee.onBlurCapture),onPointerDownCapture:qe(a.onPointerDownCapture,V.onPointerDownCapture)})});Ru.displayName=Pb;var e1="DismissableLayerBranch",t1=x.forwardRef((a,r)=>{const s=x.useContext($v),c=x.useRef(null),u=Ze(r,c);return x.useEffect(()=>{const m=c.current;if(m)return s.branches.add(m),()=>{s.branches.delete(m)}},[s.branches]),f.jsx(Le.div,{...a,ref:u})});t1.displayName=e1;function n1(a,r=globalThis==null?void 0:globalThis.document){const s=jl(a),c=x.useRef(!1),u=x.useRef(()=>{});return x.useEffect(()=>{const m=g=>{if(g.target&&!c.current){let y=function(){Wv(Fb,s,v,{discrete:!0})};const v={originalEvent:g};g.pointerType==="touch"?(r.removeEventListener("click",u.current),u.current=y,r.addEventListener("click",u.current,{once:!0})):y()}else r.removeEventListener("click",u.current);c.current=!1},h=window.setTimeout(()=>{r.addEventListener("pointerdown",m)},0);return()=>{window.clearTimeout(h),r.removeEventListener("pointerdown",m),r.removeEventListener("click",u.current)}},[r,s]),{onPointerDownCapture:()=>c.current=!0}}function l1(a,r=globalThis==null?void 0:globalThis.document){const s=jl(a),c=x.useRef(!1);return x.useEffect(()=>{const u=m=>{m.target&&!c.current&&Wv(Ib,s,{originalEvent:m},{discrete:!1})};return r.addEventListener("focusin",u),()=>r.removeEventListener("focusin",u)},[r,s]),{onFocusCapture:()=>c.current=!0,onBlurCapture:()=>c.current=!1}}function ov(){const a=new CustomEvent(yu);document.dispatchEvent(a)}function Wv(a,r,s,{discrete:c}){const u=s.originalEvent.target,m=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:s});r&&u.addEventListener(a,r,{once:!0}),c?$b(u,m):u.dispatchEvent(m)}var tu="focusScope.autoFocusOnMount",nu="focusScope.autoFocusOnUnmount",rv={bubbles:!1,cancelable:!0},a1="FocusScope",Ou=x.forwardRef((a,r)=>{const{loop:s=!1,trapped:c=!1,onMountAutoFocus:u,onUnmountAutoFocus:m,...h}=a,[g,y]=x.useState(null),v=jl(u),b=jl(m),N=x.useRef(null),C=Ze(r,E=>y(E)),R=x.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;x.useEffect(()=>{if(c){let E=function(B){if(R.paused||!g)return;const K=B.target;g.contains(K)?N.current=K:el(N.current,{select:!0})},_=function(B){if(R.paused||!g)return;const K=B.relatedTarget;K!==null&&(g.contains(K)||el(N.current,{select:!0}))},k=function(B){if(document.activeElement===document.body)for(const V of B)V.removedNodes.length>0&&el(g)};document.addEventListener("focusin",E),document.addEventListener("focusout",_);const U=new MutationObserver(k);return g&&U.observe(g,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",E),document.removeEventListener("focusout",_),U.disconnect()}}},[c,g,R.paused]),x.useEffect(()=>{if(g){sv.add(R);const E=document.activeElement;if(!g.contains(E)){const k=new CustomEvent(tu,rv);g.addEventListener(tu,v),g.dispatchEvent(k),k.defaultPrevented||(i1(u1(Pv(g)),{select:!0}),document.activeElement===E&&el(g))}return()=>{g.removeEventListener(tu,v),setTimeout(()=>{const k=new CustomEvent(nu,rv);g.addEventListener(nu,b),g.dispatchEvent(k),k.defaultPrevented||el(E??document.body,{select:!0}),g.removeEventListener(nu,b),sv.remove(R)},0)}}},[g,v,b,R]);const M=x.useCallback(E=>{if(!s&&!c||R.paused)return;const _=E.key==="Tab"&&!E.altKey&&!E.ctrlKey&&!E.metaKey,k=document.activeElement;if(_&&k){const U=E.currentTarget,[B,K]=o1(U);B&&K?!E.shiftKey&&k===K?(E.preventDefault(),s&&el(B,{select:!0})):E.shiftKey&&k===B&&(E.preventDefault(),s&&el(K,{select:!0})):k===U&&E.preventDefault()}},[s,c,R.paused]);return f.jsx(Le.div,{tabIndex:-1,...h,ref:C,onKeyDown:M})});Ou.displayName=a1;function i1(a,{select:r=!1}={}){const s=document.activeElement;for(const c of a)if(el(c,{select:r}),document.activeElement!==s)return}function o1(a){const r=Pv(a),s=cv(r,a),c=cv(r.reverse(),a);return[s,c]}function Pv(a){const r=[],s=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:c=>{const u=c.tagName==="INPUT"&&c.type==="hidden";return c.disabled||c.hidden||u?NodeFilter.FILTER_SKIP:c.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)r.push(s.currentNode);return r}function cv(a,r){for(const s of a)if(!r1(s,{upTo:r}))return s}function r1(a,{upTo:r}){if(getComputedStyle(a).visibility==="hidden")return!0;for(;a;){if(r!==void 0&&a===r)return!1;if(getComputedStyle(a).display==="none")return!0;a=a.parentElement}return!1}function c1(a){return a instanceof HTMLInputElement&&"select"in a}function el(a,{select:r=!1}={}){if(a&&a.focus){const s=document.activeElement;a.focus({preventScroll:!0}),a!==s&&c1(a)&&r&&a.select()}}var sv=s1();function s1(){let a=[];return{add(r){const s=a[0];r!==s&&(s==null||s.pause()),a=uv(a,r),a.unshift(r)},remove(r){var s;a=uv(a,r),(s=a[0])==null||s.resume()}}}function uv(a,r){const s=[...a],c=s.indexOf(r);return c!==-1&&s.splice(c,1),s}function u1(a){return a.filter(r=>r.tagName!=="A")}var f1="Portal",Mu=x.forwardRef((a,r)=>{var g;const{container:s,...c}=a,[u,m]=x.useState(!1);ht(()=>m(!0),[]);const h=s||u&&((g=globalThis==null?void 0:globalThis.document)==null?void 0:g.body);return h?Kb.createPortal(f.jsx(Le.div,{...c,ref:r}),h):null});Mu.displayName=f1;function d1(a,r){return x.useReducer((s,c)=>r[s][c]??s,a)}var Tr=a=>{const{present:r,children:s}=a,c=m1(r),u=typeof s=="function"?s({present:c.isPresent}):x.Children.only(s),m=Ze(c.ref,h1(u));return typeof s=="function"||c.isPresent?x.cloneElement(u,{ref:m}):null};Tr.displayName="Presence";function m1(a){const[r,s]=x.useState(),c=x.useRef(null),u=x.useRef(a),m=x.useRef("none"),h=a?"mounted":"unmounted",[g,y]=d1(h,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return x.useEffect(()=>{const v=cr(c.current);m.current=g==="mounted"?v:"none"},[g]),ht(()=>{const v=c.current,b=u.current;if(b!==a){const C=m.current,R=cr(v);a?y("MOUNT"):R==="none"||(v==null?void 0:v.display)==="none"?y("UNMOUNT"):y(b&&C!==R?"ANIMATION_OUT":"UNMOUNT"),u.current=a}},[a,y]),ht(()=>{if(r){let v;const b=r.ownerDocument.defaultView??window,N=R=>{const E=cr(c.current).includes(R.animationName);if(R.target===r&&E&&(y("ANIMATION_END"),!u.current)){const _=r.style.animationFillMode;r.style.animationFillMode="forwards",v=b.setTimeout(()=>{r.style.animationFillMode==="forwards"&&(r.style.animationFillMode=_)})}},C=R=>{R.target===r&&(m.current=cr(c.current))};return r.addEventListener("animationstart",C),r.addEventListener("animationcancel",N),r.addEventListener("animationend",N),()=>{b.clearTimeout(v),r.removeEventListener("animationstart",C),r.removeEventListener("animationcancel",N),r.removeEventListener("animationend",N)}}else y("ANIMATION_END")},[r,y]),{isPresent:["mounted","unmountSuspended"].includes(g),ref:x.useCallback(v=>{c.current=v?getComputedStyle(v):null,s(v)},[])}}function cr(a){return(a==null?void 0:a.animationName)||"none"}function h1(a){var c,u;let r=(c=Object.getOwnPropertyDescriptor(a.props,"ref"))==null?void 0:c.get,s=r&&"isReactWarning"in r&&r.isReactWarning;return s?a.ref:(r=(u=Object.getOwnPropertyDescriptor(a,"ref"))==null?void 0:u.get,s=r&&"isReactWarning"in r&&r.isReactWarning,s?a.props.ref:a.props.ref||a.ref)}var lu=0;function Fv(){x.useEffect(()=>{const a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??fv()),document.body.insertAdjacentElement("beforeend",a[1]??fv()),lu++,()=>{lu===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),lu--}},[])}function fv(){const a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var ln=function(){return ln=Object.assign||function(r){for(var s,c=1,u=arguments.length;c<u;c++){s=arguments[c];for(var m in s)Object.prototype.hasOwnProperty.call(s,m)&&(r[m]=s[m])}return r},ln.apply(this,arguments)};function Iv(a,r){var s={};for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&r.indexOf(c)<0&&(s[c]=a[c]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var u=0,c=Object.getOwnPropertySymbols(a);u<c.length;u++)r.indexOf(c[u])<0&&Object.prototype.propertyIsEnumerable.call(a,c[u])&&(s[c[u]]=a[c[u]]);return s}function v1(a,r,s){if(s||arguments.length===2)for(var c=0,u=r.length,m;c<u;c++)(m||!(c in r))&&(m||(m=Array.prototype.slice.call(r,0,c)),m[c]=r[c]);return a.concat(m||Array.prototype.slice.call(r))}var vr="right-scroll-bar-position",gr="width-before-scroll-bar",g1="with-scroll-bars-hidden",p1="--removed-body-scroll-bar-size";function au(a,r){return typeof a=="function"?a(r):a&&(a.current=r),a}function y1(a,r){var s=x.useState(function(){return{value:a,callback:r,facade:{get current(){return s.value},set current(c){var u=s.value;u!==c&&(s.value=c,s.callback(c,u))}}}})[0];return s.callback=r,s.facade}var x1=typeof window<"u"?x.useLayoutEffect:x.useEffect,dv=new WeakMap;function b1(a,r){var s=y1(null,function(c){return a.forEach(function(u){return au(u,c)})});return x1(function(){var c=dv.get(s);if(c){var u=new Set(c),m=new Set(a),h=s.current;u.forEach(function(g){m.has(g)||au(g,null)}),m.forEach(function(g){u.has(g)||au(g,h)})}dv.set(s,a)},[a]),s}function S1(a){return a}function w1(a,r){r===void 0&&(r=S1);var s=[],c=!1,u={read:function(){if(c)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:a},useMedium:function(m){var h=r(m,c);return s.push(h),function(){s=s.filter(function(g){return g!==h})}},assignSyncMedium:function(m){for(c=!0;s.length;){var h=s;s=[],h.forEach(m)}s={push:function(g){return m(g)},filter:function(){return s}}},assignMedium:function(m){c=!0;var h=[];if(s.length){var g=s;s=[],g.forEach(m),h=s}var y=function(){var b=h;h=[],b.forEach(m)},v=function(){return Promise.resolve().then(y)};v(),s={push:function(b){h.push(b),v()},filter:function(b){return h=h.filter(b),s}}}};return u}function E1(a){a===void 0&&(a={});var r=w1(null);return r.options=ln({async:!0,ssr:!1},a),r}var eg=function(a){var r=a.sideCar,s=Iv(a,["sideCar"]);if(!r)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var c=r.read();if(!c)throw new Error("Sidecar medium not found");return x.createElement(c,ln({},s))};eg.isSideCarExport=!0;function N1(a,r){return a.useMedium(r),eg}var tg=E1(),iu=function(){},Cr=x.forwardRef(function(a,r){var s=x.useRef(null),c=x.useState({onScrollCapture:iu,onWheelCapture:iu,onTouchMoveCapture:iu}),u=c[0],m=c[1],h=a.forwardProps,g=a.children,y=a.className,v=a.removeScrollBar,b=a.enabled,N=a.shards,C=a.sideCar,R=a.noIsolation,M=a.inert,E=a.allowPinchZoom,_=a.as,k=_===void 0?"div":_,U=a.gapMode,B=Iv(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),K=C,V=b1([s,r]),ee=ln(ln({},B),u);return x.createElement(x.Fragment,null,b&&x.createElement(K,{sideCar:tg,removeScrollBar:v,shards:N,noIsolation:R,inert:M,setCallbacks:m,allowPinchZoom:!!E,lockRef:s,gapMode:U}),h?x.cloneElement(x.Children.only(g),ln(ln({},ee),{ref:V})):x.createElement(k,ln({},ee,{className:y,ref:V}),g))});Cr.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Cr.classNames={fullWidth:gr,zeroRight:vr};var A1=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function T1(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var r=A1();return r&&a.setAttribute("nonce",r),a}function C1(a,r){a.styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r))}function j1(a){var r=document.head||document.getElementsByTagName("head")[0];r.appendChild(a)}var _1=function(){var a=0,r=null;return{add:function(s){a==0&&(r=T1())&&(C1(r,s),j1(r)),a++},remove:function(){a--,!a&&r&&(r.parentNode&&r.parentNode.removeChild(r),r=null)}}},R1=function(){var a=_1();return function(r,s){x.useEffect(function(){return a.add(r),function(){a.remove()}},[r&&s])}},ng=function(){var a=R1(),r=function(s){var c=s.styles,u=s.dynamic;return a(c,u),null};return r},O1={left:0,top:0,right:0,gap:0},ou=function(a){return parseInt(a||"",10)||0},M1=function(a){var r=window.getComputedStyle(document.body),s=r[a==="padding"?"paddingLeft":"marginLeft"],c=r[a==="padding"?"paddingTop":"marginTop"],u=r[a==="padding"?"paddingRight":"marginRight"];return[ou(s),ou(c),ou(u)]},D1=function(a){if(a===void 0&&(a="margin"),typeof window>"u")return O1;var r=M1(a),s=document.documentElement.clientWidth,c=window.innerWidth;return{left:r[0],top:r[1],right:r[2],gap:Math.max(0,c-s+r[2]-r[0])}},z1=ng(),Ta="data-scroll-locked",U1=function(a,r,s,c){var u=a.left,m=a.top,h=a.right,g=a.gap;return s===void 0&&(s="margin"),`
  .`.concat(g1,` {
   overflow: hidden `).concat(c,`;
   padding-right: `).concat(g,"px ").concat(c,`;
  }
  body[`).concat(Ta,`] {
    overflow: hidden `).concat(c,`;
    overscroll-behavior: contain;
    `).concat([r&&"position: relative ".concat(c,";"),s==="margin"&&`
    padding-left: `.concat(u,`px;
    padding-top: `).concat(m,`px;
    padding-right: `).concat(h,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(g,"px ").concat(c,`;
    `),s==="padding"&&"padding-right: ".concat(g,"px ").concat(c,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(vr,` {
    right: `).concat(g,"px ").concat(c,`;
  }
  
  .`).concat(gr,` {
    margin-right: `).concat(g,"px ").concat(c,`;
  }
  
  .`).concat(vr," .").concat(vr,` {
    right: 0 `).concat(c,`;
  }
  
  .`).concat(gr," .").concat(gr,` {
    margin-right: 0 `).concat(c,`;
  }
  
  body[`).concat(Ta,`] {
    `).concat(p1,": ").concat(g,`px;
  }
`)},mv=function(){var a=parseInt(document.body.getAttribute(Ta)||"0",10);return isFinite(a)?a:0},H1=function(){x.useEffect(function(){return document.body.setAttribute(Ta,(mv()+1).toString()),function(){var a=mv()-1;a<=0?document.body.removeAttribute(Ta):document.body.setAttribute(Ta,a.toString())}},[])},B1=function(a){var r=a.noRelative,s=a.noImportant,c=a.gapMode,u=c===void 0?"margin":c;H1();var m=x.useMemo(function(){return D1(u)},[u]);return x.createElement(z1,{styles:U1(m,!r,u,s?"":"!important")})},xu=!1;if(typeof window<"u")try{var sr=Object.defineProperty({},"passive",{get:function(){return xu=!0,!0}});window.addEventListener("test",sr,sr),window.removeEventListener("test",sr,sr)}catch{xu=!1}var xa=xu?{passive:!1}:!1,L1=function(a){return a.tagName==="TEXTAREA"},lg=function(a,r){if(!(a instanceof Element))return!1;var s=window.getComputedStyle(a);return s[r]!=="hidden"&&!(s.overflowY===s.overflowX&&!L1(a)&&s[r]==="visible")},k1=function(a){return lg(a,"overflowY")},q1=function(a){return lg(a,"overflowX")},hv=function(a,r){var s=r.ownerDocument,c=r;do{typeof ShadowRoot<"u"&&c instanceof ShadowRoot&&(c=c.host);var u=ag(a,c);if(u){var m=ig(a,c),h=m[1],g=m[2];if(h>g)return!0}c=c.parentNode}while(c&&c!==s.body);return!1},V1=function(a){var r=a.scrollTop,s=a.scrollHeight,c=a.clientHeight;return[r,s,c]},Y1=function(a){var r=a.scrollLeft,s=a.scrollWidth,c=a.clientWidth;return[r,s,c]},ag=function(a,r){return a==="v"?k1(r):q1(r)},ig=function(a,r){return a==="v"?V1(r):Y1(r)},G1=function(a,r){return a==="h"&&r==="rtl"?-1:1},X1=function(a,r,s,c,u){var m=G1(a,window.getComputedStyle(r).direction),h=m*c,g=s.target,y=r.contains(g),v=!1,b=h>0,N=0,C=0;do{var R=ig(a,g),M=R[0],E=R[1],_=R[2],k=E-_-m*M;(M||k)&&ag(a,g)&&(N+=k,C+=M),g instanceof ShadowRoot?g=g.host:g=g.parentNode}while(!y&&g!==document.body||y&&(r.contains(g)||r===g));return(b&&Math.abs(N)<1||!b&&Math.abs(C)<1)&&(v=!0),v},ur=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},vv=function(a){return[a.deltaX,a.deltaY]},gv=function(a){return a&&"current"in a?a.current:a},Q1=function(a,r){return a[0]===r[0]&&a[1]===r[1]},Z1=function(a){return`
  .block-interactivity-`.concat(a,` {pointer-events: none;}
  .allow-interactivity-`).concat(a,` {pointer-events: all;}
`)},K1=0,ba=[];function J1(a){var r=x.useRef([]),s=x.useRef([0,0]),c=x.useRef(),u=x.useState(K1++)[0],m=x.useState(ng)[0],h=x.useRef(a);x.useEffect(function(){h.current=a},[a]),x.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(u));var E=v1([a.lockRef.current],(a.shards||[]).map(gv),!0).filter(Boolean);return E.forEach(function(_){return _.classList.add("allow-interactivity-".concat(u))}),function(){document.body.classList.remove("block-interactivity-".concat(u)),E.forEach(function(_){return _.classList.remove("allow-interactivity-".concat(u))})}}},[a.inert,a.lockRef.current,a.shards]);var g=x.useCallback(function(E,_){if("touches"in E&&E.touches.length===2||E.type==="wheel"&&E.ctrlKey)return!h.current.allowPinchZoom;var k=ur(E),U=s.current,B="deltaX"in E?E.deltaX:U[0]-k[0],K="deltaY"in E?E.deltaY:U[1]-k[1],V,ee=E.target,P=Math.abs(B)>Math.abs(K)?"h":"v";if("touches"in E&&P==="h"&&ee.type==="range")return!1;var $=hv(P,ee);if(!$)return!0;if($?V=P:(V=P==="v"?"h":"v",$=hv(P,ee)),!$)return!1;if(!c.current&&"changedTouches"in E&&(B||K)&&(c.current=V),!V)return!0;var se=c.current||V;return X1(se,_,E,se==="h"?B:K)},[]),y=x.useCallback(function(E){var _=E;if(!(!ba.length||ba[ba.length-1]!==m)){var k="deltaY"in _?vv(_):ur(_),U=r.current.filter(function(V){return V.name===_.type&&(V.target===_.target||_.target===V.shadowParent)&&Q1(V.delta,k)})[0];if(U&&U.should){_.cancelable&&_.preventDefault();return}if(!U){var B=(h.current.shards||[]).map(gv).filter(Boolean).filter(function(V){return V.contains(_.target)}),K=B.length>0?g(_,B[0]):!h.current.noIsolation;K&&_.cancelable&&_.preventDefault()}}},[]),v=x.useCallback(function(E,_,k,U){var B={name:E,delta:_,target:k,should:U,shadowParent:$1(k)};r.current.push(B),setTimeout(function(){r.current=r.current.filter(function(K){return K!==B})},1)},[]),b=x.useCallback(function(E){s.current=ur(E),c.current=void 0},[]),N=x.useCallback(function(E){v(E.type,vv(E),E.target,g(E,a.lockRef.current))},[]),C=x.useCallback(function(E){v(E.type,ur(E),E.target,g(E,a.lockRef.current))},[]);x.useEffect(function(){return ba.push(m),a.setCallbacks({onScrollCapture:N,onWheelCapture:N,onTouchMoveCapture:C}),document.addEventListener("wheel",y,xa),document.addEventListener("touchmove",y,xa),document.addEventListener("touchstart",b,xa),function(){ba=ba.filter(function(E){return E!==m}),document.removeEventListener("wheel",y,xa),document.removeEventListener("touchmove",y,xa),document.removeEventListener("touchstart",b,xa)}},[]);var R=a.removeScrollBar,M=a.inert;return x.createElement(x.Fragment,null,M?x.createElement(m,{styles:Z1(u)}):null,R?x.createElement(B1,{gapMode:a.gapMode}):null)}function $1(a){for(var r=null;a!==null;)a instanceof ShadowRoot&&(r=a.host,a=a.host),a=a.parentNode;return r}const W1=N1(tg,J1);var Du=x.forwardRef(function(a,r){return x.createElement(Cr,ln({},a,{ref:r,sideCar:W1}))});Du.classNames=Cr.classNames;var P1=function(a){if(typeof document>"u")return null;var r=Array.isArray(a)?a[0]:a;return r.ownerDocument.body},Sa=new WeakMap,fr=new WeakMap,dr={},ru=0,og=function(a){return a&&(a.host||og(a.parentNode))},F1=function(a,r){return r.map(function(s){if(a.contains(s))return s;var c=og(s);return c&&a.contains(c)?c:(console.error("aria-hidden",s,"in not contained inside",a,". Doing nothing"),null)}).filter(function(s){return!!s})},I1=function(a,r,s,c){var u=F1(r,Array.isArray(a)?a:[a]);dr[s]||(dr[s]=new WeakMap);var m=dr[s],h=[],g=new Set,y=new Set(u),v=function(N){!N||g.has(N)||(g.add(N),v(N.parentNode))};u.forEach(v);var b=function(N){!N||y.has(N)||Array.prototype.forEach.call(N.children,function(C){if(g.has(C))b(C);else try{var R=C.getAttribute(c),M=R!==null&&R!=="false",E=(Sa.get(C)||0)+1,_=(m.get(C)||0)+1;Sa.set(C,E),m.set(C,_),h.push(C),E===1&&M&&fr.set(C,!0),_===1&&C.setAttribute(s,"true"),M||C.setAttribute(c,"true")}catch(k){console.error("aria-hidden: cannot operate on ",C,k)}})};return b(r),g.clear(),ru++,function(){h.forEach(function(N){var C=Sa.get(N)-1,R=m.get(N)-1;Sa.set(N,C),m.set(N,R),C||(fr.has(N)||N.removeAttribute(c),fr.delete(N)),R||N.removeAttribute(s)}),ru--,ru||(Sa=new WeakMap,Sa=new WeakMap,fr=new WeakMap,dr={})}},rg=function(a,r,s){s===void 0&&(s="data-aria-hidden");var c=Array.from(Array.isArray(a)?a:[a]),u=P1(a);return u?(c.push.apply(c,Array.from(u.querySelectorAll("[aria-live]"))),I1(c,u,s,"aria-hidden")):function(){return null}},jr="Dialog",[cg,Ew]=Ar(jr),[eS,Pt]=cg(jr),sg=a=>{const{__scopeDialog:r,children:s,open:c,defaultOpen:u,onOpenChange:m,modal:h=!0}=a,g=x.useRef(null),y=x.useRef(null),[v,b]=pu({prop:c,defaultProp:u??!1,onChange:m,caller:jr});return f.jsx(eS,{scope:r,triggerRef:g,contentRef:y,contentId:Aa(),titleId:Aa(),descriptionId:Aa(),open:v,onOpenChange:b,onOpenToggle:x.useCallback(()=>b(N=>!N),[b]),modal:h,children:s})};sg.displayName=jr;var ug="DialogTrigger",fg=x.forwardRef((a,r)=>{const{__scopeDialog:s,...c}=a,u=Pt(ug,s),m=Ze(r,u.triggerRef);return f.jsx(Le.button,{type:"button","aria-haspopup":"dialog","aria-expanded":u.open,"aria-controls":u.contentId,"data-state":Hu(u.open),...c,ref:m,onClick:qe(a.onClick,u.onOpenToggle)})});fg.displayName=ug;var zu="DialogPortal",[tS,dg]=cg(zu,{forceMount:void 0}),mg=a=>{const{__scopeDialog:r,forceMount:s,children:c,container:u}=a,m=Pt(zu,r);return f.jsx(tS,{scope:r,forceMount:s,children:x.Children.map(c,h=>f.jsx(Tr,{present:s||m.open,children:f.jsx(Mu,{asChild:!0,container:u,children:h})}))})};mg.displayName=zu;var xr="DialogOverlay",hg=x.forwardRef((a,r)=>{const s=dg(xr,a.__scopeDialog),{forceMount:c=s.forceMount,...u}=a,m=Pt(xr,a.__scopeDialog);return m.modal?f.jsx(Tr,{present:c||m.open,children:f.jsx(lS,{...u,ref:r})}):null});hg.displayName=xr;var nS=ja("DialogOverlay.RemoveScroll"),lS=x.forwardRef((a,r)=>{const{__scopeDialog:s,...c}=a,u=Pt(xr,s);return f.jsx(Du,{as:nS,allowPinchZoom:!0,shards:[u.contentRef],children:f.jsx(Le.div,{"data-state":Hu(u.open),...c,ref:r,style:{pointerEvents:"auto",...c.style}})})}),_l="DialogContent",vg=x.forwardRef((a,r)=>{const s=dg(_l,a.__scopeDialog),{forceMount:c=s.forceMount,...u}=a,m=Pt(_l,a.__scopeDialog);return f.jsx(Tr,{present:c||m.open,children:m.modal?f.jsx(aS,{...u,ref:r}):f.jsx(iS,{...u,ref:r})})});vg.displayName=_l;var aS=x.forwardRef((a,r)=>{const s=Pt(_l,a.__scopeDialog),c=x.useRef(null),u=Ze(r,s.contentRef,c);return x.useEffect(()=>{const m=c.current;if(m)return rg(m)},[]),f.jsx(gg,{...a,ref:u,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:qe(a.onCloseAutoFocus,m=>{var h;m.preventDefault(),(h=s.triggerRef.current)==null||h.focus()}),onPointerDownOutside:qe(a.onPointerDownOutside,m=>{const h=m.detail.originalEvent,g=h.button===0&&h.ctrlKey===!0;(h.button===2||g)&&m.preventDefault()}),onFocusOutside:qe(a.onFocusOutside,m=>m.preventDefault())})}),iS=x.forwardRef((a,r)=>{const s=Pt(_l,a.__scopeDialog),c=x.useRef(!1),u=x.useRef(!1);return f.jsx(gg,{...a,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:m=>{var h,g;(h=a.onCloseAutoFocus)==null||h.call(a,m),m.defaultPrevented||(c.current||(g=s.triggerRef.current)==null||g.focus(),m.preventDefault()),c.current=!1,u.current=!1},onInteractOutside:m=>{var y,v;(y=a.onInteractOutside)==null||y.call(a,m),m.defaultPrevented||(c.current=!0,m.detail.originalEvent.type==="pointerdown"&&(u.current=!0));const h=m.target;((v=s.triggerRef.current)==null?void 0:v.contains(h))&&m.preventDefault(),m.detail.originalEvent.type==="focusin"&&u.current&&m.preventDefault()}})}),gg=x.forwardRef((a,r)=>{const{__scopeDialog:s,trapFocus:c,onOpenAutoFocus:u,onCloseAutoFocus:m,...h}=a,g=Pt(_l,s),y=x.useRef(null),v=Ze(r,y);return Fv(),f.jsxs(f.Fragment,{children:[f.jsx(Ou,{asChild:!0,loop:!0,trapped:c,onMountAutoFocus:u,onUnmountAutoFocus:m,children:f.jsx(Ru,{role:"dialog",id:g.contentId,"aria-describedby":g.descriptionId,"aria-labelledby":g.titleId,"data-state":Hu(g.open),...h,ref:v,onDismiss:()=>g.onOpenChange(!1)})}),f.jsxs(f.Fragment,{children:[f.jsx(oS,{titleId:g.titleId}),f.jsx(cS,{contentRef:y,descriptionId:g.descriptionId})]})]})}),Uu="DialogTitle",pg=x.forwardRef((a,r)=>{const{__scopeDialog:s,...c}=a,u=Pt(Uu,s);return f.jsx(Le.h2,{id:u.titleId,...c,ref:r})});pg.displayName=Uu;var yg="DialogDescription",xg=x.forwardRef((a,r)=>{const{__scopeDialog:s,...c}=a,u=Pt(yg,s);return f.jsx(Le.p,{id:u.descriptionId,...c,ref:r})});xg.displayName=yg;var bg="DialogClose",Sg=x.forwardRef((a,r)=>{const{__scopeDialog:s,...c}=a,u=Pt(bg,s);return f.jsx(Le.button,{type:"button",...c,ref:r,onClick:qe(a.onClick,()=>u.onOpenChange(!1))})});Sg.displayName=bg;function Hu(a){return a?"open":"closed"}var wg="DialogTitleWarning",[Nw,Eg]=qb(wg,{contentName:_l,titleName:Uu,docsSlug:"dialog"}),oS=({titleId:a})=>{const r=Eg(wg),s=`\`${r.contentName}\` requires a \`${r.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${r.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${r.docsSlug}`;return x.useEffect(()=>{a&&(document.getElementById(a)||console.error(s))},[s,a]),null},rS="DialogDescriptionWarning",cS=({contentRef:a,descriptionId:r})=>{const c=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Eg(rS).contentName}}.`;return x.useEffect(()=>{var m;const u=(m=a.current)==null?void 0:m.getAttribute("aria-describedby");r&&u&&(document.getElementById(r)||console.warn(c))},[c,a,r]),null},sS=sg,uS=fg,fS=mg,dS=hg,mS=vg,hS=pg,vS=xg,gS=Sg;function Ng({...a}){return f.jsx(sS,{"data-slot":"dialog",...a})}function Ag({...a}){return f.jsx(uS,{"data-slot":"dialog-trigger",...a})}function pS({...a}){return f.jsx(fS,{"data-slot":"dialog-portal",...a})}function yS({className:a,...r}){return f.jsx(dS,{"data-slot":"dialog-overlay",className:$e("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...r})}function Tg({className:a,children:r,...s}){return f.jsxs(pS,{"data-slot":"dialog-portal",children:[f.jsx(yS,{}),f.jsxs(mS,{"data-slot":"dialog-content",className:$e("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...s,children:[r,f.jsxs(gS,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[f.jsx(zv,{}),f.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Cg({className:a,...r}){return f.jsx("div",{"data-slot":"dialog-header",className:$e("flex flex-col gap-2 text-center sm:text-left",a),...r})}function jg({className:a,...r}){return f.jsx(hS,{"data-slot":"dialog-title",className:$e("text-lg leading-none font-semibold",a),...r})}function _g({className:a,...r}){return f.jsx(vS,{"data-slot":"dialog-description",className:$e("text-muted-foreground text-sm",a),...r})}var xS="Label",Rg=x.forwardRef((a,r)=>f.jsx(Le.label,{...a,ref:r,onMouseDown:s=>{var u;s.target.closest("button, input, select, textarea")||((u=a.onMouseDown)==null||u.call(a,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));Rg.displayName=xS;var bS=Rg;function Zt({className:a,...r}){return f.jsx(bS,{"data-slot":"label",className:$e("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...r})}function Og({className:a,...r}){return f.jsx("textarea",{"data-slot":"textarea",className:$e("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...r})}function pv(a,[r,s]){return Math.min(s,Math.max(r,a))}function SS(a){const r=a+"CollectionProvider",[s,c]=Ar(r),[u,m]=s(r,{collectionRef:{current:null},itemMap:new Map}),h=E=>{const{scope:_,children:k}=E,U=In.useRef(null),B=In.useRef(new Map).current;return f.jsx(u,{scope:_,itemMap:B,collectionRef:U,children:k})};h.displayName=r;const g=a+"CollectionSlot",y=ja(g),v=In.forwardRef((E,_)=>{const{scope:k,children:U}=E,B=m(g,k),K=Ze(_,B.collectionRef);return f.jsx(y,{ref:K,children:U})});v.displayName=g;const b=a+"CollectionItemSlot",N="data-radix-collection-item",C=ja(b),R=In.forwardRef((E,_)=>{const{scope:k,children:U,...B}=E,K=In.useRef(null),V=Ze(_,K),ee=m(b,k);return In.useEffect(()=>(ee.itemMap.set(K,{ref:K,...B}),()=>void ee.itemMap.delete(K))),f.jsx(C,{[N]:"",ref:V,children:U})});R.displayName=b;function M(E){const _=m(a+"CollectionConsumer",E);return In.useCallback(()=>{const U=_.collectionRef.current;if(!U)return[];const B=Array.from(U.querySelectorAll(`[${N}]`));return Array.from(_.itemMap.values()).sort((ee,P)=>B.indexOf(ee.ref.current)-B.indexOf(P.ref.current))},[_.collectionRef,_.itemMap])}return[{Provider:h,Slot:v,ItemSlot:R},M,c]}var wS=x.createContext(void 0);function ES(a){const r=x.useContext(wS);return a||r||"ltr"}const NS=["top","right","bottom","left"],nl=Math.min,Ot=Math.max,br=Math.round,mr=Math.floor,an=a=>({x:a,y:a}),AS={left:"right",right:"left",bottom:"top",top:"bottom"},TS={start:"end",end:"start"};function bu(a,r,s){return Ot(a,nl(r,s))}function Tn(a,r){return typeof a=="function"?a(r):a}function Cn(a){return a.split("-")[0]}function Ma(a){return a.split("-")[1]}function Bu(a){return a==="x"?"y":"x"}function Lu(a){return a==="y"?"height":"width"}function An(a){return["top","bottom"].includes(Cn(a))?"y":"x"}function ku(a){return Bu(An(a))}function CS(a,r,s){s===void 0&&(s=!1);const c=Ma(a),u=ku(a),m=Lu(u);let h=u==="x"?c===(s?"end":"start")?"right":"left":c==="start"?"bottom":"top";return r.reference[m]>r.floating[m]&&(h=Sr(h)),[h,Sr(h)]}function jS(a){const r=Sr(a);return[Su(a),r,Su(r)]}function Su(a){return a.replace(/start|end/g,r=>TS[r])}function _S(a,r,s){const c=["left","right"],u=["right","left"],m=["top","bottom"],h=["bottom","top"];switch(a){case"top":case"bottom":return s?r?u:c:r?c:u;case"left":case"right":return r?m:h;default:return[]}}function RS(a,r,s,c){const u=Ma(a);let m=_S(Cn(a),s==="start",c);return u&&(m=m.map(h=>h+"-"+u),r&&(m=m.concat(m.map(Su)))),m}function Sr(a){return a.replace(/left|right|bottom|top/g,r=>AS[r])}function OS(a){return{top:0,right:0,bottom:0,left:0,...a}}function Mg(a){return typeof a!="number"?OS(a):{top:a,right:a,bottom:a,left:a}}function wr(a){const{x:r,y:s,width:c,height:u}=a;return{width:c,height:u,top:s,left:r,right:r+c,bottom:s+u,x:r,y:s}}function yv(a,r,s){let{reference:c,floating:u}=a;const m=An(r),h=ku(r),g=Lu(h),y=Cn(r),v=m==="y",b=c.x+c.width/2-u.width/2,N=c.y+c.height/2-u.height/2,C=c[g]/2-u[g]/2;let R;switch(y){case"top":R={x:b,y:c.y-u.height};break;case"bottom":R={x:b,y:c.y+c.height};break;case"right":R={x:c.x+c.width,y:N};break;case"left":R={x:c.x-u.width,y:N};break;default:R={x:c.x,y:c.y}}switch(Ma(r)){case"start":R[h]-=C*(s&&v?-1:1);break;case"end":R[h]+=C*(s&&v?-1:1);break}return R}const MS=async(a,r,s)=>{const{placement:c="bottom",strategy:u="absolute",middleware:m=[],platform:h}=s,g=m.filter(Boolean),y=await(h.isRTL==null?void 0:h.isRTL(r));let v=await h.getElementRects({reference:a,floating:r,strategy:u}),{x:b,y:N}=yv(v,c,y),C=c,R={},M=0;for(let E=0;E<g.length;E++){const{name:_,fn:k}=g[E],{x:U,y:B,data:K,reset:V}=await k({x:b,y:N,initialPlacement:c,placement:C,strategy:u,middlewareData:R,rects:v,platform:h,elements:{reference:a,floating:r}});b=U??b,N=B??N,R={...R,[_]:{...R[_],...K}},V&&M<=50&&(M++,typeof V=="object"&&(V.placement&&(C=V.placement),V.rects&&(v=V.rects===!0?await h.getElementRects({reference:a,floating:r,strategy:u}):V.rects),{x:b,y:N}=yv(v,C,y)),E=-1)}return{x:b,y:N,placement:C,strategy:u,middlewareData:R}};async function Vi(a,r){var s;r===void 0&&(r={});const{x:c,y:u,platform:m,rects:h,elements:g,strategy:y}=a,{boundary:v="clippingAncestors",rootBoundary:b="viewport",elementContext:N="floating",altBoundary:C=!1,padding:R=0}=Tn(r,a),M=Mg(R),_=g[C?N==="floating"?"reference":"floating":N],k=wr(await m.getClippingRect({element:(s=await(m.isElement==null?void 0:m.isElement(_)))==null||s?_:_.contextElement||await(m.getDocumentElement==null?void 0:m.getDocumentElement(g.floating)),boundary:v,rootBoundary:b,strategy:y})),U=N==="floating"?{x:c,y:u,width:h.floating.width,height:h.floating.height}:h.reference,B=await(m.getOffsetParent==null?void 0:m.getOffsetParent(g.floating)),K=await(m.isElement==null?void 0:m.isElement(B))?await(m.getScale==null?void 0:m.getScale(B))||{x:1,y:1}:{x:1,y:1},V=wr(m.convertOffsetParentRelativeRectToViewportRelativeRect?await m.convertOffsetParentRelativeRectToViewportRelativeRect({elements:g,rect:U,offsetParent:B,strategy:y}):U);return{top:(k.top-V.top+M.top)/K.y,bottom:(V.bottom-k.bottom+M.bottom)/K.y,left:(k.left-V.left+M.left)/K.x,right:(V.right-k.right+M.right)/K.x}}const DS=a=>({name:"arrow",options:a,async fn(r){const{x:s,y:c,placement:u,rects:m,platform:h,elements:g,middlewareData:y}=r,{element:v,padding:b=0}=Tn(a,r)||{};if(v==null)return{};const N=Mg(b),C={x:s,y:c},R=ku(u),M=Lu(R),E=await h.getDimensions(v),_=R==="y",k=_?"top":"left",U=_?"bottom":"right",B=_?"clientHeight":"clientWidth",K=m.reference[M]+m.reference[R]-C[R]-m.floating[M],V=C[R]-m.reference[R],ee=await(h.getOffsetParent==null?void 0:h.getOffsetParent(v));let P=ee?ee[B]:0;(!P||!await(h.isElement==null?void 0:h.isElement(ee)))&&(P=g.floating[B]||m.floating[M]);const $=K/2-V/2,se=P/2-E[M]/2-1,J=nl(N[k],se),ue=nl(N[U],se),re=J,ve=P-E[M]-ue,pe=P/2-E[M]/2+$,Y=bu(re,pe,ve),j=!y.arrow&&Ma(u)!=null&&pe!==Y&&m.reference[M]/2-(pe<re?J:ue)-E[M]/2<0,Z=j?pe<re?pe-re:pe-ve:0;return{[R]:C[R]+Z,data:{[R]:Y,centerOffset:pe-Y-Z,...j&&{alignmentOffset:Z}},reset:j}}}),zS=function(a){return a===void 0&&(a={}),{name:"flip",options:a,async fn(r){var s,c;const{placement:u,middlewareData:m,rects:h,initialPlacement:g,platform:y,elements:v}=r,{mainAxis:b=!0,crossAxis:N=!0,fallbackPlacements:C,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:M="none",flipAlignment:E=!0,..._}=Tn(a,r);if((s=m.arrow)!=null&&s.alignmentOffset)return{};const k=Cn(u),U=An(g),B=Cn(g)===g,K=await(y.isRTL==null?void 0:y.isRTL(v.floating)),V=C||(B||!E?[Sr(g)]:jS(g)),ee=M!=="none";!C&&ee&&V.push(...RS(g,E,M,K));const P=[g,...V],$=await Vi(r,_),se=[];let J=((c=m.flip)==null?void 0:c.overflows)||[];if(b&&se.push($[k]),N){const Y=CS(u,h,K);se.push($[Y[0]],$[Y[1]])}if(J=[...J,{placement:u,overflows:se}],!se.every(Y=>Y<=0)){var ue,re;const Y=(((ue=m.flip)==null?void 0:ue.index)||0)+1,j=P[Y];if(j){var ve;const L=N==="alignment"?U!==An(j):!1,ie=((ve=J[0])==null?void 0:ve.overflows[0])>0;if(!L||ie)return{data:{index:Y,overflows:J},reset:{placement:j}}}let Z=(re=J.filter(L=>L.overflows[0]<=0).sort((L,ie)=>L.overflows[1]-ie.overflows[1])[0])==null?void 0:re.placement;if(!Z)switch(R){case"bestFit":{var pe;const L=(pe=J.filter(ie=>{if(ee){const w=An(ie.placement);return w===U||w==="y"}return!0}).map(ie=>[ie.placement,ie.overflows.filter(w=>w>0).reduce((w,G)=>w+G,0)]).sort((ie,w)=>ie[1]-w[1])[0])==null?void 0:pe[0];L&&(Z=L);break}case"initialPlacement":Z=g;break}if(u!==Z)return{reset:{placement:Z}}}return{}}}};function xv(a,r){return{top:a.top-r.height,right:a.right-r.width,bottom:a.bottom-r.height,left:a.left-r.width}}function bv(a){return NS.some(r=>a[r]>=0)}const US=function(a){return a===void 0&&(a={}),{name:"hide",options:a,async fn(r){const{rects:s}=r,{strategy:c="referenceHidden",...u}=Tn(a,r);switch(c){case"referenceHidden":{const m=await Vi(r,{...u,elementContext:"reference"}),h=xv(m,s.reference);return{data:{referenceHiddenOffsets:h,referenceHidden:bv(h)}}}case"escaped":{const m=await Vi(r,{...u,altBoundary:!0}),h=xv(m,s.floating);return{data:{escapedOffsets:h,escaped:bv(h)}}}default:return{}}}}};async function HS(a,r){const{placement:s,platform:c,elements:u}=a,m=await(c.isRTL==null?void 0:c.isRTL(u.floating)),h=Cn(s),g=Ma(s),y=An(s)==="y",v=["left","top"].includes(h)?-1:1,b=m&&y?-1:1,N=Tn(r,a);let{mainAxis:C,crossAxis:R,alignmentAxis:M}=typeof N=="number"?{mainAxis:N,crossAxis:0,alignmentAxis:null}:{mainAxis:N.mainAxis||0,crossAxis:N.crossAxis||0,alignmentAxis:N.alignmentAxis};return g&&typeof M=="number"&&(R=g==="end"?M*-1:M),y?{x:R*b,y:C*v}:{x:C*v,y:R*b}}const BS=function(a){return a===void 0&&(a=0),{name:"offset",options:a,async fn(r){var s,c;const{x:u,y:m,placement:h,middlewareData:g}=r,y=await HS(r,a);return h===((s=g.offset)==null?void 0:s.placement)&&(c=g.arrow)!=null&&c.alignmentOffset?{}:{x:u+y.x,y:m+y.y,data:{...y,placement:h}}}}},LS=function(a){return a===void 0&&(a={}),{name:"shift",options:a,async fn(r){const{x:s,y:c,placement:u}=r,{mainAxis:m=!0,crossAxis:h=!1,limiter:g={fn:_=>{let{x:k,y:U}=_;return{x:k,y:U}}},...y}=Tn(a,r),v={x:s,y:c},b=await Vi(r,y),N=An(Cn(u)),C=Bu(N);let R=v[C],M=v[N];if(m){const _=C==="y"?"top":"left",k=C==="y"?"bottom":"right",U=R+b[_],B=R-b[k];R=bu(U,R,B)}if(h){const _=N==="y"?"top":"left",k=N==="y"?"bottom":"right",U=M+b[_],B=M-b[k];M=bu(U,M,B)}const E=g.fn({...r,[C]:R,[N]:M});return{...E,data:{x:E.x-s,y:E.y-c,enabled:{[C]:m,[N]:h}}}}}},kS=function(a){return a===void 0&&(a={}),{options:a,fn(r){const{x:s,y:c,placement:u,rects:m,middlewareData:h}=r,{offset:g=0,mainAxis:y=!0,crossAxis:v=!0}=Tn(a,r),b={x:s,y:c},N=An(u),C=Bu(N);let R=b[C],M=b[N];const E=Tn(g,r),_=typeof E=="number"?{mainAxis:E,crossAxis:0}:{mainAxis:0,crossAxis:0,...E};if(y){const B=C==="y"?"height":"width",K=m.reference[C]-m.floating[B]+_.mainAxis,V=m.reference[C]+m.reference[B]-_.mainAxis;R<K?R=K:R>V&&(R=V)}if(v){var k,U;const B=C==="y"?"width":"height",K=["top","left"].includes(Cn(u)),V=m.reference[N]-m.floating[B]+(K&&((k=h.offset)==null?void 0:k[N])||0)+(K?0:_.crossAxis),ee=m.reference[N]+m.reference[B]+(K?0:((U=h.offset)==null?void 0:U[N])||0)-(K?_.crossAxis:0);M<V?M=V:M>ee&&(M=ee)}return{[C]:R,[N]:M}}}},qS=function(a){return a===void 0&&(a={}),{name:"size",options:a,async fn(r){var s,c;const{placement:u,rects:m,platform:h,elements:g}=r,{apply:y=()=>{},...v}=Tn(a,r),b=await Vi(r,v),N=Cn(u),C=Ma(u),R=An(u)==="y",{width:M,height:E}=m.floating;let _,k;N==="top"||N==="bottom"?(_=N,k=C===(await(h.isRTL==null?void 0:h.isRTL(g.floating))?"start":"end")?"left":"right"):(k=N,_=C==="end"?"top":"bottom");const U=E-b.top-b.bottom,B=M-b.left-b.right,K=nl(E-b[_],U),V=nl(M-b[k],B),ee=!r.middlewareData.shift;let P=K,$=V;if((s=r.middlewareData.shift)!=null&&s.enabled.x&&($=B),(c=r.middlewareData.shift)!=null&&c.enabled.y&&(P=U),ee&&!C){const J=Ot(b.left,0),ue=Ot(b.right,0),re=Ot(b.top,0),ve=Ot(b.bottom,0);R?$=M-2*(J!==0||ue!==0?J+ue:Ot(b.left,b.right)):P=E-2*(re!==0||ve!==0?re+ve:Ot(b.top,b.bottom))}await y({...r,availableWidth:$,availableHeight:P});const se=await h.getDimensions(g.floating);return M!==se.width||E!==se.height?{reset:{rects:!0}}:{}}}};function _r(){return typeof window<"u"}function Da(a){return Dg(a)?(a.nodeName||"").toLowerCase():"#document"}function Mt(a){var r;return(a==null||(r=a.ownerDocument)==null?void 0:r.defaultView)||window}function rn(a){var r;return(r=(Dg(a)?a.ownerDocument:a.document)||window.document)==null?void 0:r.documentElement}function Dg(a){return _r()?a instanceof Node||a instanceof Mt(a).Node:!1}function $t(a){return _r()?a instanceof Element||a instanceof Mt(a).Element:!1}function on(a){return _r()?a instanceof HTMLElement||a instanceof Mt(a).HTMLElement:!1}function Sv(a){return!_r()||typeof ShadowRoot>"u"?!1:a instanceof ShadowRoot||a instanceof Mt(a).ShadowRoot}function Xi(a){const{overflow:r,overflowX:s,overflowY:c,display:u}=Wt(a);return/auto|scroll|overlay|hidden|clip/.test(r+c+s)&&!["inline","contents"].includes(u)}function VS(a){return["table","td","th"].includes(Da(a))}function Rr(a){return[":popover-open",":modal"].some(r=>{try{return a.matches(r)}catch{return!1}})}function qu(a){const r=Vu(),s=$t(a)?Wt(a):a;return["transform","translate","scale","rotate","perspective"].some(c=>s[c]?s[c]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!r&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!r&&(s.filter?s.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(c=>(s.willChange||"").includes(c))||["paint","layout","strict","content"].some(c=>(s.contain||"").includes(c))}function YS(a){let r=ll(a);for(;on(r)&&!_a(r);){if(qu(r))return r;if(Rr(r))return null;r=ll(r)}return null}function Vu(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function _a(a){return["html","body","#document"].includes(Da(a))}function Wt(a){return Mt(a).getComputedStyle(a)}function Or(a){return $t(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function ll(a){if(Da(a)==="html")return a;const r=a.assignedSlot||a.parentNode||Sv(a)&&a.host||rn(a);return Sv(r)?r.host:r}function zg(a){const r=ll(a);return _a(r)?a.ownerDocument?a.ownerDocument.body:a.body:on(r)&&Xi(r)?r:zg(r)}function Yi(a,r,s){var c;r===void 0&&(r=[]),s===void 0&&(s=!0);const u=zg(a),m=u===((c=a.ownerDocument)==null?void 0:c.body),h=Mt(u);if(m){const g=wu(h);return r.concat(h,h.visualViewport||[],Xi(u)?u:[],g&&s?Yi(g):[])}return r.concat(u,Yi(u,[],s))}function wu(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function Ug(a){const r=Wt(a);let s=parseFloat(r.width)||0,c=parseFloat(r.height)||0;const u=on(a),m=u?a.offsetWidth:s,h=u?a.offsetHeight:c,g=br(s)!==m||br(c)!==h;return g&&(s=m,c=h),{width:s,height:c,$:g}}function Yu(a){return $t(a)?a:a.contextElement}function Ca(a){const r=Yu(a);if(!on(r))return an(1);const s=r.getBoundingClientRect(),{width:c,height:u,$:m}=Ug(r);let h=(m?br(s.width):s.width)/c,g=(m?br(s.height):s.height)/u;return(!h||!Number.isFinite(h))&&(h=1),(!g||!Number.isFinite(g))&&(g=1),{x:h,y:g}}const GS=an(0);function Hg(a){const r=Mt(a);return!Vu()||!r.visualViewport?GS:{x:r.visualViewport.offsetLeft,y:r.visualViewport.offsetTop}}function XS(a,r,s){return r===void 0&&(r=!1),!s||r&&s!==Mt(a)?!1:r}function Rl(a,r,s,c){r===void 0&&(r=!1),s===void 0&&(s=!1);const u=a.getBoundingClientRect(),m=Yu(a);let h=an(1);r&&(c?$t(c)&&(h=Ca(c)):h=Ca(a));const g=XS(m,s,c)?Hg(m):an(0);let y=(u.left+g.x)/h.x,v=(u.top+g.y)/h.y,b=u.width/h.x,N=u.height/h.y;if(m){const C=Mt(m),R=c&&$t(c)?Mt(c):c;let M=C,E=wu(M);for(;E&&c&&R!==M;){const _=Ca(E),k=E.getBoundingClientRect(),U=Wt(E),B=k.left+(E.clientLeft+parseFloat(U.paddingLeft))*_.x,K=k.top+(E.clientTop+parseFloat(U.paddingTop))*_.y;y*=_.x,v*=_.y,b*=_.x,N*=_.y,y+=B,v+=K,M=Mt(E),E=wu(M)}}return wr({width:b,height:N,x:y,y:v})}function Gu(a,r){const s=Or(a).scrollLeft;return r?r.left+s:Rl(rn(a)).left+s}function Bg(a,r,s){s===void 0&&(s=!1);const c=a.getBoundingClientRect(),u=c.left+r.scrollLeft-(s?0:Gu(a,c)),m=c.top+r.scrollTop;return{x:u,y:m}}function QS(a){let{elements:r,rect:s,offsetParent:c,strategy:u}=a;const m=u==="fixed",h=rn(c),g=r?Rr(r.floating):!1;if(c===h||g&&m)return s;let y={scrollLeft:0,scrollTop:0},v=an(1);const b=an(0),N=on(c);if((N||!N&&!m)&&((Da(c)!=="body"||Xi(h))&&(y=Or(c)),on(c))){const R=Rl(c);v=Ca(c),b.x=R.x+c.clientLeft,b.y=R.y+c.clientTop}const C=h&&!N&&!m?Bg(h,y,!0):an(0);return{width:s.width*v.x,height:s.height*v.y,x:s.x*v.x-y.scrollLeft*v.x+b.x+C.x,y:s.y*v.y-y.scrollTop*v.y+b.y+C.y}}function ZS(a){return Array.from(a.getClientRects())}function KS(a){const r=rn(a),s=Or(a),c=a.ownerDocument.body,u=Ot(r.scrollWidth,r.clientWidth,c.scrollWidth,c.clientWidth),m=Ot(r.scrollHeight,r.clientHeight,c.scrollHeight,c.clientHeight);let h=-s.scrollLeft+Gu(a);const g=-s.scrollTop;return Wt(c).direction==="rtl"&&(h+=Ot(r.clientWidth,c.clientWidth)-u),{width:u,height:m,x:h,y:g}}function JS(a,r){const s=Mt(a),c=rn(a),u=s.visualViewport;let m=c.clientWidth,h=c.clientHeight,g=0,y=0;if(u){m=u.width,h=u.height;const v=Vu();(!v||v&&r==="fixed")&&(g=u.offsetLeft,y=u.offsetTop)}return{width:m,height:h,x:g,y}}function $S(a,r){const s=Rl(a,!0,r==="fixed"),c=s.top+a.clientTop,u=s.left+a.clientLeft,m=on(a)?Ca(a):an(1),h=a.clientWidth*m.x,g=a.clientHeight*m.y,y=u*m.x,v=c*m.y;return{width:h,height:g,x:y,y:v}}function wv(a,r,s){let c;if(r==="viewport")c=JS(a,s);else if(r==="document")c=KS(rn(a));else if($t(r))c=$S(r,s);else{const u=Hg(a);c={x:r.x-u.x,y:r.y-u.y,width:r.width,height:r.height}}return wr(c)}function Lg(a,r){const s=ll(a);return s===r||!$t(s)||_a(s)?!1:Wt(s).position==="fixed"||Lg(s,r)}function WS(a,r){const s=r.get(a);if(s)return s;let c=Yi(a,[],!1).filter(g=>$t(g)&&Da(g)!=="body"),u=null;const m=Wt(a).position==="fixed";let h=m?ll(a):a;for(;$t(h)&&!_a(h);){const g=Wt(h),y=qu(h);!y&&g.position==="fixed"&&(u=null),(m?!y&&!u:!y&&g.position==="static"&&!!u&&["absolute","fixed"].includes(u.position)||Xi(h)&&!y&&Lg(a,h))?c=c.filter(b=>b!==h):u=g,h=ll(h)}return r.set(a,c),c}function PS(a){let{element:r,boundary:s,rootBoundary:c,strategy:u}=a;const h=[...s==="clippingAncestors"?Rr(r)?[]:WS(r,this._c):[].concat(s),c],g=h[0],y=h.reduce((v,b)=>{const N=wv(r,b,u);return v.top=Ot(N.top,v.top),v.right=nl(N.right,v.right),v.bottom=nl(N.bottom,v.bottom),v.left=Ot(N.left,v.left),v},wv(r,g,u));return{width:y.right-y.left,height:y.bottom-y.top,x:y.left,y:y.top}}function FS(a){const{width:r,height:s}=Ug(a);return{width:r,height:s}}function IS(a,r,s){const c=on(r),u=rn(r),m=s==="fixed",h=Rl(a,!0,m,r);let g={scrollLeft:0,scrollTop:0};const y=an(0);function v(){y.x=Gu(u)}if(c||!c&&!m)if((Da(r)!=="body"||Xi(u))&&(g=Or(r)),c){const R=Rl(r,!0,m,r);y.x=R.x+r.clientLeft,y.y=R.y+r.clientTop}else u&&v();m&&!c&&u&&v();const b=u&&!c&&!m?Bg(u,g):an(0),N=h.left+g.scrollLeft-y.x-b.x,C=h.top+g.scrollTop-y.y-b.y;return{x:N,y:C,width:h.width,height:h.height}}function cu(a){return Wt(a).position==="static"}function Ev(a,r){if(!on(a)||Wt(a).position==="fixed")return null;if(r)return r(a);let s=a.offsetParent;return rn(a)===s&&(s=s.ownerDocument.body),s}function kg(a,r){const s=Mt(a);if(Rr(a))return s;if(!on(a)){let u=ll(a);for(;u&&!_a(u);){if($t(u)&&!cu(u))return u;u=ll(u)}return s}let c=Ev(a,r);for(;c&&VS(c)&&cu(c);)c=Ev(c,r);return c&&_a(c)&&cu(c)&&!qu(c)?s:c||YS(a)||s}const e2=async function(a){const r=this.getOffsetParent||kg,s=this.getDimensions,c=await s(a.floating);return{reference:IS(a.reference,await r(a.floating),a.strategy),floating:{x:0,y:0,width:c.width,height:c.height}}};function t2(a){return Wt(a).direction==="rtl"}const n2={convertOffsetParentRelativeRectToViewportRelativeRect:QS,getDocumentElement:rn,getClippingRect:PS,getOffsetParent:kg,getElementRects:e2,getClientRects:ZS,getDimensions:FS,getScale:Ca,isElement:$t,isRTL:t2};function qg(a,r){return a.x===r.x&&a.y===r.y&&a.width===r.width&&a.height===r.height}function l2(a,r){let s=null,c;const u=rn(a);function m(){var g;clearTimeout(c),(g=s)==null||g.disconnect(),s=null}function h(g,y){g===void 0&&(g=!1),y===void 0&&(y=1),m();const v=a.getBoundingClientRect(),{left:b,top:N,width:C,height:R}=v;if(g||r(),!C||!R)return;const M=mr(N),E=mr(u.clientWidth-(b+C)),_=mr(u.clientHeight-(N+R)),k=mr(b),B={rootMargin:-M+"px "+-E+"px "+-_+"px "+-k+"px",threshold:Ot(0,nl(1,y))||1};let K=!0;function V(ee){const P=ee[0].intersectionRatio;if(P!==y){if(!K)return h();P?h(!1,P):c=setTimeout(()=>{h(!1,1e-7)},1e3)}P===1&&!qg(v,a.getBoundingClientRect())&&h(),K=!1}try{s=new IntersectionObserver(V,{...B,root:u.ownerDocument})}catch{s=new IntersectionObserver(V,B)}s.observe(a)}return h(!0),m}function a2(a,r,s,c){c===void 0&&(c={});const{ancestorScroll:u=!0,ancestorResize:m=!0,elementResize:h=typeof ResizeObserver=="function",layoutShift:g=typeof IntersectionObserver=="function",animationFrame:y=!1}=c,v=Yu(a),b=u||m?[...v?Yi(v):[],...Yi(r)]:[];b.forEach(k=>{u&&k.addEventListener("scroll",s,{passive:!0}),m&&k.addEventListener("resize",s)});const N=v&&g?l2(v,s):null;let C=-1,R=null;h&&(R=new ResizeObserver(k=>{let[U]=k;U&&U.target===v&&R&&(R.unobserve(r),cancelAnimationFrame(C),C=requestAnimationFrame(()=>{var B;(B=R)==null||B.observe(r)})),s()}),v&&!y&&R.observe(v),R.observe(r));let M,E=y?Rl(a):null;y&&_();function _(){const k=Rl(a);E&&!qg(E,k)&&s(),E=k,M=requestAnimationFrame(_)}return s(),()=>{var k;b.forEach(U=>{u&&U.removeEventListener("scroll",s),m&&U.removeEventListener("resize",s)}),N==null||N(),(k=R)==null||k.disconnect(),R=null,y&&cancelAnimationFrame(M)}}const i2=BS,o2=LS,r2=zS,c2=qS,s2=US,Nv=DS,u2=kS,f2=(a,r,s)=>{const c=new Map,u={platform:n2,...s},m={...u.platform,_c:c};return MS(a,r,{...u,platform:m})};var pr=typeof document<"u"?x.useLayoutEffect:x.useEffect;function Er(a,r){if(a===r)return!0;if(typeof a!=typeof r)return!1;if(typeof a=="function"&&a.toString()===r.toString())return!0;let s,c,u;if(a&&r&&typeof a=="object"){if(Array.isArray(a)){if(s=a.length,s!==r.length)return!1;for(c=s;c--!==0;)if(!Er(a[c],r[c]))return!1;return!0}if(u=Object.keys(a),s=u.length,s!==Object.keys(r).length)return!1;for(c=s;c--!==0;)if(!{}.hasOwnProperty.call(r,u[c]))return!1;for(c=s;c--!==0;){const m=u[c];if(!(m==="_owner"&&a.$$typeof)&&!Er(a[m],r[m]))return!1}return!0}return a!==a&&r!==r}function Vg(a){return typeof window>"u"?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function Av(a,r){const s=Vg(a);return Math.round(r*s)/s}function su(a){const r=x.useRef(a);return pr(()=>{r.current=a}),r}function d2(a){a===void 0&&(a={});const{placement:r="bottom",strategy:s="absolute",middleware:c=[],platform:u,elements:{reference:m,floating:h}={},transform:g=!0,whileElementsMounted:y,open:v}=a,[b,N]=x.useState({x:0,y:0,strategy:s,placement:r,middlewareData:{},isPositioned:!1}),[C,R]=x.useState(c);Er(C,c)||R(c);const[M,E]=x.useState(null),[_,k]=x.useState(null),U=x.useCallback(L=>{L!==ee.current&&(ee.current=L,E(L))},[]),B=x.useCallback(L=>{L!==P.current&&(P.current=L,k(L))},[]),K=m||M,V=h||_,ee=x.useRef(null),P=x.useRef(null),$=x.useRef(b),se=y!=null,J=su(y),ue=su(u),re=su(v),ve=x.useCallback(()=>{if(!ee.current||!P.current)return;const L={placement:r,strategy:s,middleware:C};ue.current&&(L.platform=ue.current),f2(ee.current,P.current,L).then(ie=>{const w={...ie,isPositioned:re.current!==!1};pe.current&&!Er($.current,w)&&($.current=w,Gi.flushSync(()=>{N(w)}))})},[C,r,s,ue,re]);pr(()=>{v===!1&&$.current.isPositioned&&($.current.isPositioned=!1,N(L=>({...L,isPositioned:!1})))},[v]);const pe=x.useRef(!1);pr(()=>(pe.current=!0,()=>{pe.current=!1}),[]),pr(()=>{if(K&&(ee.current=K),V&&(P.current=V),K&&V){if(J.current)return J.current(K,V,ve);ve()}},[K,V,ve,J,se]);const Y=x.useMemo(()=>({reference:ee,floating:P,setReference:U,setFloating:B}),[U,B]),j=x.useMemo(()=>({reference:K,floating:V}),[K,V]),Z=x.useMemo(()=>{const L={position:s,left:0,top:0};if(!j.floating)return L;const ie=Av(j.floating,b.x),w=Av(j.floating,b.y);return g?{...L,transform:"translate("+ie+"px, "+w+"px)",...Vg(j.floating)>=1.5&&{willChange:"transform"}}:{position:s,left:ie,top:w}},[s,g,j.floating,b.x,b.y]);return x.useMemo(()=>({...b,update:ve,refs:Y,elements:j,floatingStyles:Z}),[b,ve,Y,j,Z])}const m2=a=>{function r(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:a,fn(s){const{element:c,padding:u}=typeof a=="function"?a(s):a;return c&&r(c)?c.current!=null?Nv({element:c.current,padding:u}).fn(s):{}:c?Nv({element:c,padding:u}).fn(s):{}}}},h2=(a,r)=>({...i2(a),options:[a,r]}),v2=(a,r)=>({...o2(a),options:[a,r]}),g2=(a,r)=>({...u2(a),options:[a,r]}),p2=(a,r)=>({...r2(a),options:[a,r]}),y2=(a,r)=>({...c2(a),options:[a,r]}),x2=(a,r)=>({...s2(a),options:[a,r]}),b2=(a,r)=>({...m2(a),options:[a,r]});var S2="Arrow",Yg=x.forwardRef((a,r)=>{const{children:s,width:c=10,height:u=5,...m}=a;return f.jsx(Le.svg,{...m,ref:r,width:c,height:u,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?s:f.jsx("polygon",{points:"0,0 30,0 15,10"})})});Yg.displayName=S2;var w2=Yg;function E2(a){const[r,s]=x.useState(void 0);return ht(()=>{if(a){s({width:a.offsetWidth,height:a.offsetHeight});const c=new ResizeObserver(u=>{if(!Array.isArray(u)||!u.length)return;const m=u[0];let h,g;if("borderBoxSize"in m){const y=m.borderBoxSize,v=Array.isArray(y)?y[0]:y;h=v.inlineSize,g=v.blockSize}else h=a.offsetWidth,g=a.offsetHeight;s({width:h,height:g})});return c.observe(a,{box:"border-box"}),()=>c.unobserve(a)}else s(void 0)},[a]),r}var Xu="Popper",[Gg,Xg]=Ar(Xu),[N2,Qg]=Gg(Xu),Zg=a=>{const{__scopePopper:r,children:s}=a,[c,u]=x.useState(null);return f.jsx(N2,{scope:r,anchor:c,onAnchorChange:u,children:s})};Zg.displayName=Xu;var Kg="PopperAnchor",Jg=x.forwardRef((a,r)=>{const{__scopePopper:s,virtualRef:c,...u}=a,m=Qg(Kg,s),h=x.useRef(null),g=Ze(r,h);return x.useEffect(()=>{m.onAnchorChange((c==null?void 0:c.current)||h.current)}),c?null:f.jsx(Le.div,{...u,ref:g})});Jg.displayName=Kg;var Qu="PopperContent",[A2,T2]=Gg(Qu),$g=x.forwardRef((a,r)=>{var I,fe,Re,Te,we,Ee;const{__scopePopper:s,side:c="bottom",sideOffset:u=0,align:m="center",alignOffset:h=0,arrowPadding:g=0,avoidCollisions:y=!0,collisionBoundary:v=[],collisionPadding:b=0,sticky:N="partial",hideWhenDetached:C=!1,updatePositionStrategy:R="optimized",onPlaced:M,...E}=a,_=Qg(Qu,s),[k,U]=x.useState(null),B=Ze(r,ot=>U(ot)),[K,V]=x.useState(null),ee=E2(K),P=(ee==null?void 0:ee.width)??0,$=(ee==null?void 0:ee.height)??0,se=c+(m!=="center"?"-"+m:""),J=typeof b=="number"?b:{top:0,right:0,bottom:0,left:0,...b},ue=Array.isArray(v)?v:[v],re=ue.length>0,ve={padding:J,boundary:ue.filter(j2),altBoundary:re},{refs:pe,floatingStyles:Y,placement:j,isPositioned:Z,middlewareData:L}=d2({strategy:"fixed",placement:se,whileElementsMounted:(...ot)=>a2(...ot,{animationFrame:R==="always"}),elements:{reference:_.anchor},middleware:[h2({mainAxis:u+$,alignmentAxis:h}),y&&v2({mainAxis:!0,crossAxis:!1,limiter:N==="partial"?g2():void 0,...ve}),y&&p2({...ve}),y2({...ve,apply:({elements:ot,rects:gt,availableWidth:ol,availableHeight:rl})=>{const{width:ut,height:Ur}=gt.reference,cl=ot.floating.style;cl.setProperty("--radix-popper-available-width",`${ol}px`),cl.setProperty("--radix-popper-available-height",`${rl}px`),cl.setProperty("--radix-popper-anchor-width",`${ut}px`),cl.setProperty("--radix-popper-anchor-height",`${Ur}px`)}}),K&&b2({element:K,padding:g}),_2({arrowWidth:P,arrowHeight:$}),C&&x2({strategy:"referenceHidden",...ve})]}),[ie,w]=Fg(j),G=jl(M);ht(()=>{Z&&(G==null||G())},[Z,G]);const F=(I=L.arrow)==null?void 0:I.x,W=(fe=L.arrow)==null?void 0:fe.y,te=((Re=L.arrow)==null?void 0:Re.centerOffset)!==0,[ge,ce]=x.useState();return ht(()=>{k&&ce(window.getComputedStyle(k).zIndex)},[k]),f.jsx("div",{ref:pe.setFloating,"data-radix-popper-content-wrapper":"",style:{...Y,transform:Z?Y.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ge,"--radix-popper-transform-origin":[(Te=L.transformOrigin)==null?void 0:Te.x,(we=L.transformOrigin)==null?void 0:we.y].join(" "),...((Ee=L.hide)==null?void 0:Ee.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:f.jsx(A2,{scope:s,placedSide:ie,onArrowChange:V,arrowX:F,arrowY:W,shouldHideArrow:te,children:f.jsx(Le.div,{"data-side":ie,"data-align":w,...E,ref:B,style:{...E.style,animation:Z?void 0:"none"}})})})});$g.displayName=Qu;var Wg="PopperArrow",C2={top:"bottom",right:"left",bottom:"top",left:"right"},Pg=x.forwardRef(function(r,s){const{__scopePopper:c,...u}=r,m=T2(Wg,c),h=C2[m.placedSide];return f.jsx("span",{ref:m.onArrowChange,style:{position:"absolute",left:m.arrowX,top:m.arrowY,[h]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[m.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[m.placedSide],visibility:m.shouldHideArrow?"hidden":void 0},children:f.jsx(w2,{...u,ref:s,style:{...u.style,display:"block"}})})});Pg.displayName=Wg;function j2(a){return a!==null}var _2=a=>({name:"transformOrigin",options:a,fn(r){var _,k,U;const{placement:s,rects:c,middlewareData:u}=r,h=((_=u.arrow)==null?void 0:_.centerOffset)!==0,g=h?0:a.arrowWidth,y=h?0:a.arrowHeight,[v,b]=Fg(s),N={start:"0%",center:"50%",end:"100%"}[b],C=(((k=u.arrow)==null?void 0:k.x)??0)+g/2,R=(((U=u.arrow)==null?void 0:U.y)??0)+y/2;let M="",E="";return v==="bottom"?(M=h?N:`${C}px`,E=`${-y}px`):v==="top"?(M=h?N:`${C}px`,E=`${c.floating.height+y}px`):v==="right"?(M=`${-y}px`,E=h?N:`${R}px`):v==="left"&&(M=`${c.floating.width+y}px`,E=h?N:`${R}px`),{data:{x:M,y:E}}}});function Fg(a){const[r,s="center"]=a.split("-");return[r,s]}var R2=Zg,O2=Jg,M2=$g,D2=Pg;function z2(a){const r=x.useRef({value:a,previous:a});return x.useMemo(()=>(r.current.value!==a&&(r.current.previous=r.current.value,r.current.value=a),r.current.previous),[a])}var Ig=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),U2="VisuallyHidden",H2=x.forwardRef((a,r)=>f.jsx(Le.span,{...a,ref:r,style:{...Ig,...a.style}}));H2.displayName=U2;var B2=[" ","Enter","ArrowUp","ArrowDown"],L2=[" ","Enter"],Ol="Select",[Mr,Dr,k2]=SS(Ol),[za,Aw]=Ar(Ol,[k2,Xg]),zr=Xg(),[q2,al]=za(Ol),[V2,Y2]=za(Ol),ep=a=>{const{__scopeSelect:r,children:s,open:c,defaultOpen:u,onOpenChange:m,value:h,defaultValue:g,onValueChange:y,dir:v,name:b,autoComplete:N,disabled:C,required:R,form:M}=a,E=zr(r),[_,k]=x.useState(null),[U,B]=x.useState(null),[K,V]=x.useState(!1),ee=ES(v),[P,$]=pu({prop:c,defaultProp:u??!1,onChange:m,caller:Ol}),[se,J]=pu({prop:h,defaultProp:g,onChange:y,caller:Ol}),ue=x.useRef(null),re=_?M||!!_.closest("form"):!0,[ve,pe]=x.useState(new Set),Y=Array.from(ve).map(j=>j.props.value).join(";");return f.jsx(R2,{...E,children:f.jsxs(q2,{required:R,scope:r,trigger:_,onTriggerChange:k,valueNode:U,onValueNodeChange:B,valueNodeHasChildren:K,onValueNodeHasChildrenChange:V,contentId:Aa(),value:se,onValueChange:J,open:P,onOpenChange:$,dir:ee,triggerPointerDownPosRef:ue,disabled:C,children:[f.jsx(Mr.Provider,{scope:r,children:f.jsx(V2,{scope:a.__scopeSelect,onNativeOptionAdd:x.useCallback(j=>{pe(Z=>new Set(Z).add(j))},[]),onNativeOptionRemove:x.useCallback(j=>{pe(Z=>{const L=new Set(Z);return L.delete(j),L})},[]),children:s})}),re?f.jsxs(wp,{"aria-hidden":!0,required:R,tabIndex:-1,name:b,autoComplete:N,value:se,onChange:j=>J(j.target.value),disabled:C,form:M,children:[se===void 0?f.jsx("option",{value:""}):null,Array.from(ve)]},Y):null]})})};ep.displayName=Ol;var tp="SelectTrigger",np=x.forwardRef((a,r)=>{const{__scopeSelect:s,disabled:c=!1,...u}=a,m=zr(s),h=al(tp,s),g=h.disabled||c,y=Ze(r,h.onTriggerChange),v=Dr(s),b=x.useRef("touch"),[N,C,R]=Np(E=>{const _=v().filter(B=>!B.disabled),k=_.find(B=>B.value===h.value),U=Ap(_,E,k);U!==void 0&&h.onValueChange(U.value)}),M=E=>{g||(h.onOpenChange(!0),R()),E&&(h.triggerPointerDownPosRef.current={x:Math.round(E.pageX),y:Math.round(E.pageY)})};return f.jsx(O2,{asChild:!0,...m,children:f.jsx(Le.button,{type:"button",role:"combobox","aria-controls":h.contentId,"aria-expanded":h.open,"aria-required":h.required,"aria-autocomplete":"none",dir:h.dir,"data-state":h.open?"open":"closed",disabled:g,"data-disabled":g?"":void 0,"data-placeholder":Ep(h.value)?"":void 0,...u,ref:y,onClick:qe(u.onClick,E=>{E.currentTarget.focus(),b.current!=="mouse"&&M(E)}),onPointerDown:qe(u.onPointerDown,E=>{b.current=E.pointerType;const _=E.target;_.hasPointerCapture(E.pointerId)&&_.releasePointerCapture(E.pointerId),E.button===0&&E.ctrlKey===!1&&E.pointerType==="mouse"&&(M(E),E.preventDefault())}),onKeyDown:qe(u.onKeyDown,E=>{const _=N.current!=="";!(E.ctrlKey||E.altKey||E.metaKey)&&E.key.length===1&&C(E.key),!(_&&E.key===" ")&&B2.includes(E.key)&&(M(),E.preventDefault())})})})});np.displayName=tp;var lp="SelectValue",ap=x.forwardRef((a,r)=>{const{__scopeSelect:s,className:c,style:u,children:m,placeholder:h="",...g}=a,y=al(lp,s),{onValueNodeHasChildrenChange:v}=y,b=m!==void 0,N=Ze(r,y.onValueNodeChange);return ht(()=>{v(b)},[v,b]),f.jsx(Le.span,{...g,ref:N,style:{pointerEvents:"none"},children:Ep(y.value)?f.jsx(f.Fragment,{children:h}):m})});ap.displayName=lp;var G2="SelectIcon",ip=x.forwardRef((a,r)=>{const{__scopeSelect:s,children:c,...u}=a;return f.jsx(Le.span,{"aria-hidden":!0,...u,ref:r,children:c||"▼"})});ip.displayName=G2;var X2="SelectPortal",op=a=>f.jsx(Mu,{asChild:!0,...a});op.displayName=X2;var Ml="SelectContent",rp=x.forwardRef((a,r)=>{const s=al(Ml,a.__scopeSelect),[c,u]=x.useState();if(ht(()=>{u(new DocumentFragment)},[]),!s.open){const m=c;return m?Gi.createPortal(f.jsx(cp,{scope:a.__scopeSelect,children:f.jsx(Mr.Slot,{scope:a.__scopeSelect,children:f.jsx("div",{children:a.children})})}),m):null}return f.jsx(sp,{...a,ref:r})});rp.displayName=Ml;var Qt=10,[cp,il]=za(Ml),Q2="SelectContentImpl",Z2=ja("SelectContent.RemoveScroll"),sp=x.forwardRef((a,r)=>{const{__scopeSelect:s,position:c="item-aligned",onCloseAutoFocus:u,onEscapeKeyDown:m,onPointerDownOutside:h,side:g,sideOffset:y,align:v,alignOffset:b,arrowPadding:N,collisionBoundary:C,collisionPadding:R,sticky:M,hideWhenDetached:E,avoidCollisions:_,...k}=a,U=al(Ml,s),[B,K]=x.useState(null),[V,ee]=x.useState(null),P=Ze(r,I=>K(I)),[$,se]=x.useState(null),[J,ue]=x.useState(null),re=Dr(s),[ve,pe]=x.useState(!1),Y=x.useRef(!1);x.useEffect(()=>{if(B)return rg(B)},[B]),Fv();const j=x.useCallback(I=>{const[fe,...Re]=re().map(Ee=>Ee.ref.current),[Te]=Re.slice(-1),we=document.activeElement;for(const Ee of I)if(Ee===we||(Ee==null||Ee.scrollIntoView({block:"nearest"}),Ee===fe&&V&&(V.scrollTop=0),Ee===Te&&V&&(V.scrollTop=V.scrollHeight),Ee==null||Ee.focus(),document.activeElement!==we))return},[re,V]),Z=x.useCallback(()=>j([$,B]),[j,$,B]);x.useEffect(()=>{ve&&Z()},[ve,Z]);const{onOpenChange:L,triggerPointerDownPosRef:ie}=U;x.useEffect(()=>{if(B){let I={x:0,y:0};const fe=Te=>{var we,Ee;I={x:Math.abs(Math.round(Te.pageX)-(((we=ie.current)==null?void 0:we.x)??0)),y:Math.abs(Math.round(Te.pageY)-(((Ee=ie.current)==null?void 0:Ee.y)??0))}},Re=Te=>{I.x<=10&&I.y<=10?Te.preventDefault():B.contains(Te.target)||L(!1),document.removeEventListener("pointermove",fe),ie.current=null};return ie.current!==null&&(document.addEventListener("pointermove",fe),document.addEventListener("pointerup",Re,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",fe),document.removeEventListener("pointerup",Re,{capture:!0})}}},[B,L,ie]),x.useEffect(()=>{const I=()=>L(!1);return window.addEventListener("blur",I),window.addEventListener("resize",I),()=>{window.removeEventListener("blur",I),window.removeEventListener("resize",I)}},[L]);const[w,G]=Np(I=>{const fe=re().filter(we=>!we.disabled),Re=fe.find(we=>we.ref.current===document.activeElement),Te=Ap(fe,I,Re);Te&&setTimeout(()=>Te.ref.current.focus())}),F=x.useCallback((I,fe,Re)=>{const Te=!Y.current&&!Re;(U.value!==void 0&&U.value===fe||Te)&&(se(I),Te&&(Y.current=!0))},[U.value]),W=x.useCallback(()=>B==null?void 0:B.focus(),[B]),te=x.useCallback((I,fe,Re)=>{const Te=!Y.current&&!Re;(U.value!==void 0&&U.value===fe||Te)&&ue(I)},[U.value]),ge=c==="popper"?Eu:up,ce=ge===Eu?{side:g,sideOffset:y,align:v,alignOffset:b,arrowPadding:N,collisionBoundary:C,collisionPadding:R,sticky:M,hideWhenDetached:E,avoidCollisions:_}:{};return f.jsx(cp,{scope:s,content:B,viewport:V,onViewportChange:ee,itemRefCallback:F,selectedItem:$,onItemLeave:W,itemTextRefCallback:te,focusSelectedItem:Z,selectedItemText:J,position:c,isPositioned:ve,searchRef:w,children:f.jsx(Du,{as:Z2,allowPinchZoom:!0,children:f.jsx(Ou,{asChild:!0,trapped:U.open,onMountAutoFocus:I=>{I.preventDefault()},onUnmountAutoFocus:qe(u,I=>{var fe;(fe=U.trigger)==null||fe.focus({preventScroll:!0}),I.preventDefault()}),children:f.jsx(Ru,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:m,onPointerDownOutside:h,onFocusOutside:I=>I.preventDefault(),onDismiss:()=>U.onOpenChange(!1),children:f.jsx(ge,{role:"listbox",id:U.contentId,"data-state":U.open?"open":"closed",dir:U.dir,onContextMenu:I=>I.preventDefault(),...k,...ce,onPlaced:()=>pe(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:qe(k.onKeyDown,I=>{const fe=I.ctrlKey||I.altKey||I.metaKey;if(I.key==="Tab"&&I.preventDefault(),!fe&&I.key.length===1&&G(I.key),["ArrowUp","ArrowDown","Home","End"].includes(I.key)){let Te=re().filter(we=>!we.disabled).map(we=>we.ref.current);if(["ArrowUp","End"].includes(I.key)&&(Te=Te.slice().reverse()),["ArrowUp","ArrowDown"].includes(I.key)){const we=I.target,Ee=Te.indexOf(we);Te=Te.slice(Ee+1)}setTimeout(()=>j(Te)),I.preventDefault()}})})})})})})});sp.displayName=Q2;var K2="SelectItemAlignedPosition",up=x.forwardRef((a,r)=>{const{__scopeSelect:s,onPlaced:c,...u}=a,m=al(Ml,s),h=il(Ml,s),[g,y]=x.useState(null),[v,b]=x.useState(null),N=Ze(r,P=>b(P)),C=Dr(s),R=x.useRef(!1),M=x.useRef(!0),{viewport:E,selectedItem:_,selectedItemText:k,focusSelectedItem:U}=h,B=x.useCallback(()=>{if(m.trigger&&m.valueNode&&g&&v&&E&&_&&k){const P=m.trigger.getBoundingClientRect(),$=v.getBoundingClientRect(),se=m.valueNode.getBoundingClientRect(),J=k.getBoundingClientRect();if(m.dir!=="rtl"){const we=J.left-$.left,Ee=se.left-we,ot=P.left-Ee,gt=P.width+ot,ol=Math.max(gt,$.width),rl=window.innerWidth-Qt,ut=pv(Ee,[Qt,Math.max(Qt,rl-ol)]);g.style.minWidth=gt+"px",g.style.left=ut+"px"}else{const we=$.right-J.right,Ee=window.innerWidth-se.right-we,ot=window.innerWidth-P.right-Ee,gt=P.width+ot,ol=Math.max(gt,$.width),rl=window.innerWidth-Qt,ut=pv(Ee,[Qt,Math.max(Qt,rl-ol)]);g.style.minWidth=gt+"px",g.style.right=ut+"px"}const ue=C(),re=window.innerHeight-Qt*2,ve=E.scrollHeight,pe=window.getComputedStyle(v),Y=parseInt(pe.borderTopWidth,10),j=parseInt(pe.paddingTop,10),Z=parseInt(pe.borderBottomWidth,10),L=parseInt(pe.paddingBottom,10),ie=Y+j+ve+L+Z,w=Math.min(_.offsetHeight*5,ie),G=window.getComputedStyle(E),F=parseInt(G.paddingTop,10),W=parseInt(G.paddingBottom,10),te=P.top+P.height/2-Qt,ge=re-te,ce=_.offsetHeight/2,I=_.offsetTop+ce,fe=Y+j+I,Re=ie-fe;if(fe<=te){const we=ue.length>0&&_===ue[ue.length-1].ref.current;g.style.bottom="0px";const Ee=v.clientHeight-E.offsetTop-E.offsetHeight,ot=Math.max(ge,ce+(we?W:0)+Ee+Z),gt=fe+ot;g.style.height=gt+"px"}else{const we=ue.length>0&&_===ue[0].ref.current;g.style.top="0px";const ot=Math.max(te,Y+E.offsetTop+(we?F:0)+ce)+Re;g.style.height=ot+"px",E.scrollTop=fe-te+E.offsetTop}g.style.margin=`${Qt}px 0`,g.style.minHeight=w+"px",g.style.maxHeight=re+"px",c==null||c(),requestAnimationFrame(()=>R.current=!0)}},[C,m.trigger,m.valueNode,g,v,E,_,k,m.dir,c]);ht(()=>B(),[B]);const[K,V]=x.useState();ht(()=>{v&&V(window.getComputedStyle(v).zIndex)},[v]);const ee=x.useCallback(P=>{P&&M.current===!0&&(B(),U==null||U(),M.current=!1)},[B,U]);return f.jsx($2,{scope:s,contentWrapper:g,shouldExpandOnScrollRef:R,onScrollButtonChange:ee,children:f.jsx("div",{ref:y,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:K},children:f.jsx(Le.div,{...u,ref:N,style:{boxSizing:"border-box",maxHeight:"100%",...u.style}})})})});up.displayName=K2;var J2="SelectPopperPosition",Eu=x.forwardRef((a,r)=>{const{__scopeSelect:s,align:c="start",collisionPadding:u=Qt,...m}=a,h=zr(s);return f.jsx(M2,{...h,...m,ref:r,align:c,collisionPadding:u,style:{boxSizing:"border-box",...m.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Eu.displayName=J2;var[$2,Zu]=za(Ml,{}),Nu="SelectViewport",fp=x.forwardRef((a,r)=>{const{__scopeSelect:s,nonce:c,...u}=a,m=il(Nu,s),h=Zu(Nu,s),g=Ze(r,m.onViewportChange),y=x.useRef(0);return f.jsxs(f.Fragment,{children:[f.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:c}),f.jsx(Mr.Slot,{scope:s,children:f.jsx(Le.div,{"data-radix-select-viewport":"",role:"presentation",...u,ref:g,style:{position:"relative",flex:1,overflow:"hidden auto",...u.style},onScroll:qe(u.onScroll,v=>{const b=v.currentTarget,{contentWrapper:N,shouldExpandOnScrollRef:C}=h;if(C!=null&&C.current&&N){const R=Math.abs(y.current-b.scrollTop);if(R>0){const M=window.innerHeight-Qt*2,E=parseFloat(N.style.minHeight),_=parseFloat(N.style.height),k=Math.max(E,_);if(k<M){const U=k+R,B=Math.min(M,U),K=U-B;N.style.height=B+"px",N.style.bottom==="0px"&&(b.scrollTop=K>0?K:0,N.style.justifyContent="flex-end")}}}y.current=b.scrollTop})})})]})});fp.displayName=Nu;var dp="SelectGroup",[W2,P2]=za(dp),F2=x.forwardRef((a,r)=>{const{__scopeSelect:s,...c}=a,u=Aa();return f.jsx(W2,{scope:s,id:u,children:f.jsx(Le.div,{role:"group","aria-labelledby":u,...c,ref:r})})});F2.displayName=dp;var mp="SelectLabel",I2=x.forwardRef((a,r)=>{const{__scopeSelect:s,...c}=a,u=P2(mp,s);return f.jsx(Le.div,{id:u.id,...c,ref:r})});I2.displayName=mp;var Nr="SelectItem",[ew,hp]=za(Nr),vp=x.forwardRef((a,r)=>{const{__scopeSelect:s,value:c,disabled:u=!1,textValue:m,...h}=a,g=al(Nr,s),y=il(Nr,s),v=g.value===c,[b,N]=x.useState(m??""),[C,R]=x.useState(!1),M=Ze(r,U=>{var B;return(B=y.itemRefCallback)==null?void 0:B.call(y,U,c,u)}),E=Aa(),_=x.useRef("touch"),k=()=>{u||(g.onValueChange(c),g.onOpenChange(!1))};if(c==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return f.jsx(ew,{scope:s,value:c,disabled:u,textId:E,isSelected:v,onItemTextChange:x.useCallback(U=>{N(B=>B||((U==null?void 0:U.textContent)??"").trim())},[]),children:f.jsx(Mr.ItemSlot,{scope:s,value:c,disabled:u,textValue:b,children:f.jsx(Le.div,{role:"option","aria-labelledby":E,"data-highlighted":C?"":void 0,"aria-selected":v&&C,"data-state":v?"checked":"unchecked","aria-disabled":u||void 0,"data-disabled":u?"":void 0,tabIndex:u?void 0:-1,...h,ref:M,onFocus:qe(h.onFocus,()=>R(!0)),onBlur:qe(h.onBlur,()=>R(!1)),onClick:qe(h.onClick,()=>{_.current!=="mouse"&&k()}),onPointerUp:qe(h.onPointerUp,()=>{_.current==="mouse"&&k()}),onPointerDown:qe(h.onPointerDown,U=>{_.current=U.pointerType}),onPointerMove:qe(h.onPointerMove,U=>{var B;_.current=U.pointerType,u?(B=y.onItemLeave)==null||B.call(y):_.current==="mouse"&&U.currentTarget.focus({preventScroll:!0})}),onPointerLeave:qe(h.onPointerLeave,U=>{var B;U.currentTarget===document.activeElement&&((B=y.onItemLeave)==null||B.call(y))}),onKeyDown:qe(h.onKeyDown,U=>{var K;((K=y.searchRef)==null?void 0:K.current)!==""&&U.key===" "||(L2.includes(U.key)&&k(),U.key===" "&&U.preventDefault())})})})})});vp.displayName=Nr;var Ui="SelectItemText",gp=x.forwardRef((a,r)=>{const{__scopeSelect:s,className:c,style:u,...m}=a,h=al(Ui,s),g=il(Ui,s),y=hp(Ui,s),v=Y2(Ui,s),[b,N]=x.useState(null),C=Ze(r,k=>N(k),y.onItemTextChange,k=>{var U;return(U=g.itemTextRefCallback)==null?void 0:U.call(g,k,y.value,y.disabled)}),R=b==null?void 0:b.textContent,M=x.useMemo(()=>f.jsx("option",{value:y.value,disabled:y.disabled,children:R},y.value),[y.disabled,y.value,R]),{onNativeOptionAdd:E,onNativeOptionRemove:_}=v;return ht(()=>(E(M),()=>_(M)),[E,_,M]),f.jsxs(f.Fragment,{children:[f.jsx(Le.span,{id:y.textId,...m,ref:C}),y.isSelected&&h.valueNode&&!h.valueNodeHasChildren?Gi.createPortal(m.children,h.valueNode):null]})});gp.displayName=Ui;var pp="SelectItemIndicator",yp=x.forwardRef((a,r)=>{const{__scopeSelect:s,...c}=a;return hp(pp,s).isSelected?f.jsx(Le.span,{"aria-hidden":!0,...c,ref:r}):null});yp.displayName=pp;var Au="SelectScrollUpButton",xp=x.forwardRef((a,r)=>{const s=il(Au,a.__scopeSelect),c=Zu(Au,a.__scopeSelect),[u,m]=x.useState(!1),h=Ze(r,c.onScrollButtonChange);return ht(()=>{if(s.viewport&&s.isPositioned){let g=function(){const v=y.scrollTop>0;m(v)};const y=s.viewport;return g(),y.addEventListener("scroll",g),()=>y.removeEventListener("scroll",g)}},[s.viewport,s.isPositioned]),u?f.jsx(Sp,{...a,ref:h,onAutoScroll:()=>{const{viewport:g,selectedItem:y}=s;g&&y&&(g.scrollTop=g.scrollTop-y.offsetHeight)}}):null});xp.displayName=Au;var Tu="SelectScrollDownButton",bp=x.forwardRef((a,r)=>{const s=il(Tu,a.__scopeSelect),c=Zu(Tu,a.__scopeSelect),[u,m]=x.useState(!1),h=Ze(r,c.onScrollButtonChange);return ht(()=>{if(s.viewport&&s.isPositioned){let g=function(){const v=y.scrollHeight-y.clientHeight,b=Math.ceil(y.scrollTop)<v;m(b)};const y=s.viewport;return g(),y.addEventListener("scroll",g),()=>y.removeEventListener("scroll",g)}},[s.viewport,s.isPositioned]),u?f.jsx(Sp,{...a,ref:h,onAutoScroll:()=>{const{viewport:g,selectedItem:y}=s;g&&y&&(g.scrollTop=g.scrollTop+y.offsetHeight)}}):null});bp.displayName=Tu;var Sp=x.forwardRef((a,r)=>{const{__scopeSelect:s,onAutoScroll:c,...u}=a,m=il("SelectScrollButton",s),h=x.useRef(null),g=Dr(s),y=x.useCallback(()=>{h.current!==null&&(window.clearInterval(h.current),h.current=null)},[]);return x.useEffect(()=>()=>y(),[y]),ht(()=>{var b;const v=g().find(N=>N.ref.current===document.activeElement);(b=v==null?void 0:v.ref.current)==null||b.scrollIntoView({block:"nearest"})},[g]),f.jsx(Le.div,{"aria-hidden":!0,...u,ref:r,style:{flexShrink:0,...u.style},onPointerDown:qe(u.onPointerDown,()=>{h.current===null&&(h.current=window.setInterval(c,50))}),onPointerMove:qe(u.onPointerMove,()=>{var v;(v=m.onItemLeave)==null||v.call(m),h.current===null&&(h.current=window.setInterval(c,50))}),onPointerLeave:qe(u.onPointerLeave,()=>{y()})})}),tw="SelectSeparator",nw=x.forwardRef((a,r)=>{const{__scopeSelect:s,...c}=a;return f.jsx(Le.div,{"aria-hidden":!0,...c,ref:r})});nw.displayName=tw;var Cu="SelectArrow",lw=x.forwardRef((a,r)=>{const{__scopeSelect:s,...c}=a,u=zr(s),m=al(Cu,s),h=il(Cu,s);return m.open&&h.position==="popper"?f.jsx(D2,{...u,...c,ref:r}):null});lw.displayName=Cu;var aw="SelectBubbleInput",wp=x.forwardRef(({__scopeSelect:a,value:r,...s},c)=>{const u=x.useRef(null),m=Ze(c,u),h=z2(r);return x.useEffect(()=>{const g=u.current;if(!g)return;const y=window.HTMLSelectElement.prototype,b=Object.getOwnPropertyDescriptor(y,"value").set;if(h!==r&&b){const N=new Event("change",{bubbles:!0});b.call(g,r),g.dispatchEvent(N)}},[h,r]),f.jsx(Le.select,{...s,style:{...Ig,...s.style},ref:m,defaultValue:r})});wp.displayName=aw;function Ep(a){return a===""||a===void 0}function Np(a){const r=jl(a),s=x.useRef(""),c=x.useRef(0),u=x.useCallback(h=>{const g=s.current+h;r(g),function y(v){s.current=v,window.clearTimeout(c.current),v!==""&&(c.current=window.setTimeout(()=>y(""),1e3))}(g)},[r]),m=x.useCallback(()=>{s.current="",window.clearTimeout(c.current)},[]);return x.useEffect(()=>()=>window.clearTimeout(c.current),[]),[s,u,m]}function Ap(a,r,s){const u=r.length>1&&Array.from(r).every(v=>v===r[0])?r[0]:r,m=s?a.indexOf(s):-1;let h=iw(a,Math.max(m,0));u.length===1&&(h=h.filter(v=>v!==s));const y=h.find(v=>v.textValue.toLowerCase().startsWith(u.toLowerCase()));return y!==s?y:void 0}function iw(a,r){return a.map((s,c)=>a[(r+c)%a.length])}var ow=ep,rw=np,cw=ap,sw=ip,uw=op,fw=rp,dw=fp,mw=vp,hw=gp,vw=yp,gw=xp,pw=bp;function Hi({...a}){return f.jsx(ow,{"data-slot":"select",...a})}function Bi({...a}){return f.jsx(cw,{"data-slot":"select-value",...a})}function Li({className:a,size:r="default",children:s,...c}){return f.jsxs(rw,{"data-slot":"select-trigger","data-size":r,className:$e("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...c,children:[s,f.jsx(sw,{asChild:!0,children:f.jsx(Rv,{className:"size-4 opacity-50"})})]})}function ki({className:a,children:r,position:s="popper",...c}){return f.jsx(uw,{children:f.jsxs(fw,{"data-slot":"select-content",className:$e("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",s==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:s,...c,children:[f.jsx(yw,{}),f.jsx(dw,{className:$e("p-1",s==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),f.jsx(xw,{})]})})}function Fe({className:a,children:r,...s}){return f.jsxs(mw,{"data-slot":"select-item",className:$e("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...s,children:[f.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:f.jsx(vw,{children:f.jsx(hx,{className:"size-4"})})}),f.jsx(hw,{children:r})]})}function yw({className:a,...r}){return f.jsx(gw,{"data-slot":"select-scroll-up-button",className:$e("flex cursor-default items-center justify-center py-1",a),...r,children:f.jsx(px,{className:"size-4"})})}function xw({className:a,...r}){return f.jsx(pw,{"data-slot":"select-scroll-down-button",className:$e("flex cursor-default items-center justify-center py-1",a),...r,children:f.jsx(Rv,{className:"size-4"})})}const bw=()=>{const[a,r]=x.useState([]),[s,c]=x.useState(!0),[u,m]=x.useState(""),[h,g]=x.useState("all"),[y,v]=x.useState(null),[b,N]=x.useState(!1),[C,R]=x.useState(!1),[M,E]=x.useState({phone_number:"",name:"",email:"",notes:"",status:"new",deal_value:"",tags:[]});x.useEffect(()=>{_()},[h]);const _=async()=>{try{c(!0);const J=new URLSearchParams;h!=="all"&&J.append("status",h),u&&J.append("search",u);const ue=await fetch(`/api/contacts?${J}`);if(ue.ok){const re=await ue.json();r(re.contacts||[])}}catch(J){console.error("Erro ao buscar contatos:",J)}finally{c(!1)}},k=()=>{_()},U=async()=>{try{(await fetch("/api/contacts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...M,tags:M.tags,deal_value:M.deal_value?parseFloat(M.deal_value):null})})).ok&&(N(!1),P(),_())}catch(J){console.error("Erro ao criar contato:",J)}},B=async()=>{try{(await fetch(`/api/contacts/${y.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...M,tags:M.tags,deal_value:M.deal_value?parseFloat(M.deal_value):null})})).ok&&(N(!1),R(!1),P(),_())}catch(J){console.error("Erro ao atualizar contato:",J)}},K=async J=>{if(confirm("Tem certeza que deseja deletar este contato?"))try{(await fetch(`/api/contacts/${J}`,{method:"DELETE"})).ok&&_()}catch(ue){console.error("Erro ao deletar contato:",ue)}},V=async()=>{try{const J=await fetch("/api/contacts/export");if(J.ok){const ue=await J.blob(),re=window.URL.createObjectURL(ue),ve=document.createElement("a");ve.style.display="none",ve.href=re,ve.download="contacts.csv",document.body.appendChild(ve),ve.click(),window.URL.revokeObjectURL(re)}}catch(J){console.error("Erro ao exportar contatos:",J)}},ee=J=>{v(J),E({phone_number:J.phone_number,name:J.name||"",email:J.email||"",notes:J.notes||"",status:J.status,deal_value:J.deal_value||"",tags:JSON.parse(J.tags||"[]")}),R(!0),N(!0)},P=()=>{E({phone_number:"",name:"",email:"",notes:"",status:"new",deal_value:"",tags:[]}),v(null),R(!1)},$=J=>({new:"bg-blue-100 text-blue-800",contacted:"bg-yellow-100 text-yellow-800",qualified:"bg-green-100 text-green-800",converted:"bg-purple-100 text-purple-800"})[J]||"bg-gray-100 text-gray-800",se=J=>({new:"Novo",contacted:"Contatado",qualified:"Qualificado",converted:"Convertido"})[J]||J;return f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{children:[f.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Contatos"}),f.jsx("p",{className:"text-gray-600",children:"Gerencie seus contatos do WhatsApp"})]}),f.jsxs("div",{className:"flex space-x-2",children:[f.jsxs(Qe,{variant:"outline",onClick:V,children:[f.jsx(Ax,{className:"h-4 w-4 mr-2"}),"Exportar"]}),f.jsxs(Ng,{open:b,onOpenChange:N,children:[f.jsx(Ag,{asChild:!0,children:f.jsxs(Qe,{onClick:P,children:[f.jsx(yr,{className:"h-4 w-4 mr-2"}),"Novo Contato"]})}),f.jsxs(Tg,{className:"max-w-md",children:[f.jsxs(Cg,{children:[f.jsx(jg,{children:C?"Editar Contato":"Novo Contato"}),f.jsx(_g,{children:C?"Atualize as informações do contato":"Adicione um novo contato"})]}),f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"phone",children:"Telefone *"}),f.jsx(Nn,{id:"phone",value:M.phone_number,onChange:J=>E({...M,phone_number:J.target.value}),placeholder:"+55 11 99999-9999",disabled:C})]}),f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"name",children:"Nome"}),f.jsx(Nn,{id:"name",value:M.name,onChange:J=>E({...M,name:J.target.value}),placeholder:"Nome do contato"})]}),f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"email",children:"Email"}),f.jsx(Nn,{id:"email",type:"email",value:M.email,onChange:J=>E({...M,email:J.target.value}),placeholder:"<EMAIL>"})]}),f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"status",children:"Status"}),f.jsxs(Hi,{value:M.status,onValueChange:J=>E({...M,status:J}),children:[f.jsx(Li,{children:f.jsx(Bi,{})}),f.jsxs(ki,{children:[f.jsx(Fe,{value:"new",children:"Novo"}),f.jsx(Fe,{value:"contacted",children:"Contatado"}),f.jsx(Fe,{value:"qualified",children:"Qualificado"}),f.jsx(Fe,{value:"converted",children:"Convertido"})]})]})]}),f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"deal_value",children:"Valor da Negociação"}),f.jsx(Nn,{id:"deal_value",type:"number",value:M.deal_value,onChange:J=>E({...M,deal_value:J.target.value}),placeholder:"0.00"})]}),f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"notes",children:"Observações"}),f.jsx(Og,{id:"notes",value:M.notes,onChange:J=>E({...M,notes:J.target.value}),placeholder:"Observações sobre o contato",rows:3})]}),f.jsxs("div",{className:"flex space-x-2",children:[f.jsx(Qe,{onClick:C?B:U,className:"flex-1",children:C?"Atualizar":"Criar"}),f.jsx(Qe,{variant:"outline",onClick:()=>N(!1),className:"flex-1",children:"Cancelar"})]})]})]})]})]})]}),f.jsx(Kt,{children:f.jsx(Jt,{className:"pt-6",children:f.jsxs("div",{className:"flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4",children:[f.jsx("div",{className:"flex-1",children:f.jsxs("div",{className:"relative",children:[f.jsx(fu,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),f.jsx(Nn,{placeholder:"Buscar por nome, telefone ou email...",value:u,onChange:J=>m(J.target.value),className:"pl-10",onKeyPress:J=>J.key==="Enter"&&k()})]})}),f.jsxs(Hi,{value:h,onValueChange:g,children:[f.jsx(Li,{className:"w-full md:w-48",children:f.jsx(Bi,{placeholder:"Filtrar por status"})}),f.jsxs(ki,{children:[f.jsx(Fe,{value:"all",children:"Todos os status"}),f.jsx(Fe,{value:"new",children:"Novo"}),f.jsx(Fe,{value:"contacted",children:"Contatado"}),f.jsx(Fe,{value:"qualified",children:"Qualificado"}),f.jsx(Fe,{value:"converted",children:"Convertido"})]})]}),f.jsxs(Qe,{onClick:k,children:[f.jsx(fu,{className:"h-4 w-4 mr-2"}),"Buscar"]})]})})}),f.jsxs(Kt,{children:[f.jsx(tl,{children:f.jsxs(Ea,{className:"flex items-center space-x-2",children:[f.jsx(Na,{className:"h-5 w-5"}),f.jsxs("span",{children:["Lista de Contatos (",a.length,")"]})]})}),f.jsx(Jt,{children:s?f.jsx("div",{className:"space-y-4",children:[1,2,3].map(J=>f.jsx("div",{className:"animate-pulse",children:f.jsx("div",{className:"h-20 bg-gray-200 rounded"})},J))}):a.length>0?f.jsx("div",{className:"space-y-4",children:a.map(J=>f.jsx("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("div",{className:"flex-1",children:f.jsxs("div",{className:"flex items-center space-x-4",children:[f.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:f.jsx(Na,{className:"h-6 w-6 text-green-600"})}),f.jsxs("div",{className:"flex-1",children:[f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx("h3",{className:"font-medium text-gray-900",children:J.name||"Sem nome"}),f.jsx(qi,{className:$(J.status),children:se(J.status)})]}),f.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mt-1",children:[f.jsxs("div",{className:"flex items-center space-x-1",children:[f.jsx(Ov,{className:"h-4 w-4"}),f.jsx("span",{children:J.phone_number})]}),J.email&&f.jsxs("div",{className:"flex items-center space-x-1",children:[f.jsx(Rx,{className:"h-4 w-4"}),f.jsx("span",{children:J.email})]}),J.deal_value&&f.jsxs("div",{className:"flex items-center space-x-1",children:[f.jsx(Ex,{className:"h-4 w-4"}),f.jsxs("span",{children:["R$ ",J.deal_value.toLocaleString("pt-BR")]})]})]}),J.tags&&JSON.parse(J.tags).length>0&&f.jsxs("div",{className:"flex items-center space-x-2 mt-2",children:[f.jsx(Gx,{className:"h-4 w-4 text-gray-400"}),f.jsx("div",{className:"flex space-x-1",children:JSON.parse(J.tags).map((ue,re)=>f.jsx(qi,{variant:"outline",className:"text-xs",children:ue},re))})]})]})]})}),f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx(Qe,{variant:"outline",size:"sm",onClick:()=>ee(J),children:f.jsx(Mv,{className:"h-4 w-4"})}),f.jsx(Qe,{variant:"outline",size:"sm",onClick:()=>K(J.id),children:f.jsx(Dv,{className:"h-4 w-4"})})]})]})},J.id))}):f.jsxs("div",{className:"text-center py-12",children:[f.jsx(Na,{className:"h-12 w-12 mx-auto text-gray-300 mb-4"}),f.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Nenhum contato encontrado"}),f.jsx("p",{className:"text-gray-600 mb-4",children:u||h!=="all"?"Tente ajustar os filtros de busca":"Comece adicionando seu primeiro contato"}),!u&&h==="all"&&f.jsxs(Qe,{onClick:()=>N(!0),children:[f.jsx(yr,{className:"h-4 w-4 mr-2"}),"Adicionar Contato"]})]})})]})]})},Sw=()=>{const[a,r]=x.useState([]),[s,c]=x.useState([]),[u,m]=x.useState(!0),[h,g]=x.useState(""),[y,v]=x.useState("all"),[b,N]=x.useState("all"),[C,R]=x.useState(null),[M,E]=x.useState(!1),[_,k]=x.useState(!1),[U,B]=x.useState({name:"",content:"",template_type:"text",category:"",media_url:"",variables:[]});x.useEffect(()=>{K(),V()},[y,b]);const K=async()=>{try{m(!0);const Y=new URLSearchParams;y!=="all"&&Y.append("category",y),b!=="all"&&Y.append("type",b);const j=await fetch(`/api/templates?${Y}`);if(j.ok){const Z=await j.json();r(Z||[])}}catch(Y){console.error("Erro ao buscar templates:",Y)}finally{m(!1)}},V=async()=>{try{const Y=await fetch("/api/templates/categories");if(Y.ok){const j=await Y.json();c(j||[])}}catch(Y){console.error("Erro ao buscar categorias:",Y)}},ee=async()=>{try{(await fetch("/api/templates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...U,variables:U.variables})})).ok&&(E(!1),ue(),K(),V())}catch(Y){console.error("Erro ao criar template:",Y)}},P=async()=>{try{(await fetch(`/api/templates/${C.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...U,variables:U.variables})})).ok&&(E(!1),k(!1),ue(),K(),V())}catch(Y){console.error("Erro ao atualizar template:",Y)}},$=async Y=>{if(confirm("Tem certeza que deseja deletar este template?"))try{(await fetch(`/api/templates/${Y}`,{method:"DELETE"})).ok&&K()}catch(j){console.error("Erro ao deletar template:",j)}},se=Y=>{navigator.clipboard.writeText(Y.content)},J=Y=>{R(Y),B({name:Y.name,content:Y.content,template_type:Y.template_type,category:Y.category||"",media_url:Y.media_url||"",variables:JSON.parse(Y.variables||"[]")}),k(!0),E(!0)},ue=()=>{B({name:"",content:"",template_type:"text",category:"",media_url:"",variables:[]}),R(null),k(!1)},re=Y=>({text:hr,image:jx,audio:Ux,video:Jx})[Y]||hr,ve=Y=>({text:"bg-blue-100 text-blue-800",image:"bg-green-100 text-green-800",audio:"bg-purple-100 text-purple-800",video:"bg-red-100 text-red-800"})[Y]||"bg-gray-100 text-gray-800",pe=a.filter(Y=>Y.name.toLowerCase().includes(h.toLowerCase())||Y.content.toLowerCase().includes(h.toLowerCase()));return f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{children:[f.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Templates"}),f.jsx("p",{className:"text-gray-600",children:"Gerencie seus templates de mensagens"})]}),f.jsxs(Ng,{open:M,onOpenChange:E,children:[f.jsx(Ag,{asChild:!0,children:f.jsxs(Qe,{onClick:ue,children:[f.jsx(yr,{className:"h-4 w-4 mr-2"}),"Novo Template"]})}),f.jsxs(Tg,{className:"max-w-2xl",children:[f.jsxs(Cg,{children:[f.jsx(jg,{children:_?"Editar Template":"Novo Template"}),f.jsx(_g,{children:_?"Atualize as informações do template":"Crie um novo template de mensagem"})]}),f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"name",children:"Nome do Template *"}),f.jsx(Nn,{id:"name",value:U.name,onChange:Y=>B({...U,name:Y.target.value}),placeholder:"Nome do template"})]}),f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"type",children:"Tipo"}),f.jsxs(Hi,{value:U.template_type,onValueChange:Y=>B({...U,template_type:Y}),children:[f.jsx(Li,{children:f.jsx(Bi,{})}),f.jsxs(ki,{children:[f.jsx(Fe,{value:"text",children:"Texto"}),f.jsx(Fe,{value:"image",children:"Imagem"}),f.jsx(Fe,{value:"audio",children:"Áudio"}),f.jsx(Fe,{value:"video",children:"Vídeo"})]})]})]})]}),f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"category",children:"Categoria"}),f.jsx(Nn,{id:"category",value:U.category,onChange:Y=>B({...U,category:Y.target.value}),placeholder:"Ex: saudacao, follow_up, fechamento"})]}),U.template_type!=="text"&&f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"media_url",children:"URL da Mídia"}),f.jsx(Nn,{id:"media_url",value:U.media_url,onChange:Y=>B({...U,media_url:Y.target.value}),placeholder:"https://exemplo.com/arquivo.jpg"})]}),f.jsxs("div",{children:[f.jsx(Zt,{htmlFor:"content",children:"Conteúdo *"}),f.jsx(Og,{id:"content",value:U.content,onChange:Y=>B({...U,content:Y.target.value}),placeholder:"Olá {name}, como posso ajudá-lo hoje?",rows:6}),f.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["Use ","{name}",", ","{phone}"," para variáveis dinâmicas"]})]}),f.jsxs("div",{className:"flex space-x-2",children:[f.jsx(Qe,{onClick:_?P:ee,className:"flex-1",children:_?"Atualizar":"Criar"}),f.jsx(Qe,{variant:"outline",onClick:()=>E(!1),className:"flex-1",children:"Cancelar"})]})]})]})]})]}),f.jsx(Kt,{children:f.jsx(Jt,{className:"pt-6",children:f.jsxs("div",{className:"flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4",children:[f.jsx("div",{className:"flex-1",children:f.jsxs("div",{className:"relative",children:[f.jsx(fu,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),f.jsx(Nn,{placeholder:"Buscar templates...",value:h,onChange:Y=>g(Y.target.value),className:"pl-10"})]})}),f.jsxs(Hi,{value:y,onValueChange:v,children:[f.jsx(Li,{className:"w-full md:w-48",children:f.jsx(Bi,{placeholder:"Filtrar por categoria"})}),f.jsxs(ki,{children:[f.jsx(Fe,{value:"all",children:"Todas as categorias"}),s.map(Y=>f.jsx(Fe,{value:Y,children:Y},Y))]})]}),f.jsxs(Hi,{value:b,onValueChange:N,children:[f.jsx(Li,{className:"w-full md:w-48",children:f.jsx(Bi,{placeholder:"Filtrar por tipo"})}),f.jsxs(ki,{children:[f.jsx(Fe,{value:"all",children:"Todos os tipos"}),f.jsx(Fe,{value:"text",children:"Texto"}),f.jsx(Fe,{value:"image",children:"Imagem"}),f.jsx(Fe,{value:"audio",children:"Áudio"}),f.jsx(Fe,{value:"video",children:"Vídeo"})]})]})]})})}),f.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u?Array.from({length:6}).map((Y,j)=>f.jsxs(Kt,{className:"animate-pulse",children:[f.jsxs(tl,{children:[f.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),f.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]}),f.jsx(Jt,{children:f.jsxs("div",{className:"space-y-2",children:[f.jsx("div",{className:"h-3 bg-gray-200 rounded"}),f.jsx("div",{className:"h-3 bg-gray-200 rounded"}),f.jsx("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]})})]},j)):pe.length>0?pe.map(Y=>{const j=re(Y.template_type);return f.jsxs(Kt,{className:"hover:shadow-lg transition-shadow duration-200",children:[f.jsxs(tl,{className:"pb-3",children:[f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx(Ea,{className:"text-lg",children:Y.name}),f.jsxs(qi,{className:ve(Y.template_type),children:[f.jsx(j,{className:"h-3 w-3 mr-1"}),Y.template_type]})]}),Y.category&&f.jsxs(gu,{children:["Categoria: ",Y.category]})]}),f.jsx(Jt,{children:f.jsxs("div",{className:"space-y-4",children:[f.jsx("div",{className:"bg-gray-50 p-3 rounded-lg",children:f.jsx("p",{className:"text-sm text-gray-700 line-clamp-3",children:Y.content})}),Y.media_url&&f.jsxs("div",{className:"text-xs text-gray-500",children:[f.jsx("span",{className:"font-medium",children:"Mídia:"})," ",Y.media_url]}),f.jsxs("div",{className:"flex items-center justify-between pt-2",children:[f.jsxs("div",{className:"text-xs text-gray-500",children:["Criado em ",new Date(Y.created_at).toLocaleDateString("pt-BR")]}),f.jsxs("div",{className:"flex space-x-1",children:[f.jsx(Qe,{variant:"outline",size:"sm",onClick:()=>se(Y),title:"Copiar conteúdo",children:f.jsx(Sx,{className:"h-4 w-4"})}),f.jsx(Qe,{variant:"outline",size:"sm",onClick:()=>J(Y),title:"Editar template",children:f.jsx(Mv,{className:"h-4 w-4"})}),f.jsx(Qe,{variant:"outline",size:"sm",onClick:()=>$(Y.id),title:"Deletar template",children:f.jsx(Dv,{className:"h-4 w-4"})})]})]})]})})]},Y.id)}):f.jsx("div",{className:"col-span-full",children:f.jsx(Kt,{children:f.jsxs(Jt,{className:"text-center py-12",children:[f.jsx(hr,{className:"h-12 w-12 mx-auto text-gray-300 mb-4"}),f.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Nenhum template encontrado"}),f.jsx("p",{className:"text-gray-600 mb-4",children:h||y!=="all"||b!=="all"?"Tente ajustar os filtros de busca":"Comece criando seu primeiro template de mensagem"}),!h&&y==="all"&&b==="all"&&f.jsxs(Qe,{onClick:()=>E(!0),children:[f.jsx(yr,{className:"h-4 w-4 mr-2"}),"Criar Template"]})]})})})})]})};function ww(){const[a,r]=x.useState("dashboard"),s=()=>{switch(a){case"dashboard":return f.jsx(av,{});case"contacts":return f.jsx(bw,{});case"templates":return f.jsx(Sw,{});case"conversations":return f.jsxs("div",{className:"text-center py-12",children:[f.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Conversas"}),f.jsx("p",{className:"text-gray-600",children:"Em desenvolvimento..."})]});case"automation":return f.jsxs("div",{className:"text-center py-12",children:[f.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Automação"}),f.jsx("p",{className:"text-gray-600",children:"Em desenvolvimento..."})]});case"whatsapp":return f.jsxs("div",{className:"text-center py-12",children:[f.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"WhatsApp"}),f.jsx("p",{className:"text-gray-600",children:"Em desenvolvimento..."})]});case"ai-analysis":return f.jsxs("div",{className:"text-center py-12",children:[f.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Análise IA"}),f.jsx("p",{className:"text-gray-600",children:"Em desenvolvimento..."})]});case"settings":return f.jsxs("div",{className:"text-center py-12",children:[f.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Configurações"}),f.jsx("p",{className:"text-gray-600",children:"Em desenvolvimento..."})]});default:return f.jsx(av,{})}};return f.jsx(Lb,{currentPage:a,onPageChange:r,children:s()})}ex.createRoot(document.getElementById("root")).render(f.jsx(x.StrictMode,{children:f.jsx(ww,{})}));
