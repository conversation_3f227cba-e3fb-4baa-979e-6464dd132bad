import json
import re
import logging
from typing import Dict, List, Optional
from src.models.automation_rule import AutomationRule
from src.models.contact import Contact
from src.models.template import Template
from src.models.user import db
from src.services.whatsapp_service import WhatsAppService
from datetime import datetime

logger = logging.getLogger(__name__)

class AutomationService:
    """Serviço para automação de etiquetagem e respostas"""
    
    def __init__(self):
        self.whatsapp_service = WhatsAppService()
    
    def process_message(self, message_data: Dict) -> Dict:
        """Processar mensagem através das regras de automação"""
        try:
            content = message_data.get('content', '').lower()
            contact_id = message_data.get('contact_id')
            phone_number = message_data.get('phone_number')
            
            # Buscar regras ativas ordenadas por prioridade
            rules = AutomationRule.query.filter_by(is_active=True).order_by(
                AutomationRule.priority.desc()
            ).all()
            
            executed_actions = []
            
            for rule in rules:
                if self._should_trigger_rule(rule, content, contact_id):
                    action_result = self._execute_rule_action(rule, contact_id, phone_number, message_data)
                    if action_result:
                        executed_actions.append({
                            'rule_id': rule.id,
                            'rule_name': rule.name,
                            'action_type': rule.action_type,
                            'result': action_result
                        })
            
            return {
                'executed_actions': executed_actions,
                'total_actions': len(executed_actions)
            }
            
        except Exception as e:
            logger.error(f"Erro ao processar automação: {e}")
            return {'error': str(e)}
    
    def _should_trigger_rule(self, rule: AutomationRule, content: str, contact_id: int) -> bool:
        """Verificar se uma regra deve ser acionada"""
        try:
            # Verificar palavras-chave
            trigger_keywords = json.loads(rule.trigger_keywords or '[]')
            keyword_match = False
            
            if trigger_keywords:
                for keyword in trigger_keywords:
                    if keyword.lower() in content:
                        keyword_match = True
                        break
            else:
                keyword_match = True  # Se não há palavras-chave, sempre aciona
            
            if not keyword_match:
                return False
            
            # Verificar condições adicionais
            trigger_conditions = json.loads(rule.trigger_conditions or '{}')
            
            if trigger_conditions:
                contact = Contact.query.get(contact_id)
                if not contact:
                    return False
                
                # Verificar condição de status do contato
                if 'contact_status' in trigger_conditions:
                    required_status = trigger_conditions['contact_status']
                    if contact.status != required_status:
                        return False
                
                # Verificar condição de tags do contato
                if 'has_tag' in trigger_conditions:
                    required_tag = trigger_conditions['has_tag']
                    contact_tags = json.loads(contact.tags or '[]')
                    if required_tag not in contact_tags:
                        return False
                
                # Verificar condição de regex
                if 'regex_pattern' in trigger_conditions:
                    pattern = trigger_conditions['regex_pattern']
                    if not re.search(pattern, content, re.IGNORECASE):
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao verificar regra {rule.id}: {e}")
            return False
    
    def _execute_rule_action(self, rule: AutomationRule, contact_id: int, phone_number: str, message_data: Dict) -> Optional[Dict]:
        """Executar ação da regra de automação"""
        try:
            action_data = json.loads(rule.action_data or '{}')
            
            if rule.action_type == 'add_tag':
                return self._add_tag_to_contact(contact_id, action_data)
            
            elif rule.action_type == 'send_template':
                return self._send_template_response(contact_id, phone_number, action_data)
            
            elif rule.action_type == 'update_status':
                return self._update_contact_status(contact_id, action_data)
            
            elif rule.action_type == 'save_contact_info':
                return self._save_contact_info(contact_id, message_data, action_data)
            
            elif rule.action_type == 'send_message':
                return self._send_custom_message(contact_id, phone_number, action_data)
            
            else:
                logger.warning(f"Tipo de ação desconhecido: {rule.action_type}")
                return None
                
        except Exception as e:
            logger.error(f"Erro ao executar ação da regra {rule.id}: {e}")
            return None
    
    def _add_tag_to_contact(self, contact_id: int, action_data: Dict) -> Dict:
        """Adicionar tag ao contato"""
        try:
            contact = Contact.query.get(contact_id)
            if not contact:
                return {'error': 'Contato não encontrado'}
            
            tag_to_add = action_data.get('tag')
            if not tag_to_add:
                return {'error': 'Tag não especificada'}
            
            current_tags = json.loads(contact.tags or '[]')
            
            if tag_to_add not in current_tags:
                current_tags.append(tag_to_add)
                contact.tags = json.dumps(current_tags)
                contact.updated_at = datetime.utcnow()
                db.session.commit()
            
            return {
                'action': 'tag_added',
                'tag': tag_to_add,
                'all_tags': current_tags
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Erro ao adicionar tag: {e}")
            return {'error': str(e)}
    
    def _send_template_response(self, contact_id: int, phone_number: str, action_data: Dict) -> Dict:
        """Enviar resposta usando template"""
        try:
            template_id = action_data.get('template_id')
            account_id = action_data.get('account_id', 1)  # Default account
            variables = action_data.get('variables', {})
            
            if not template_id:
                return {'error': 'Template não especificado'}
            
            template = Template.query.get(template_id)
            if not template or not template.is_active:
                return {'error': 'Template não encontrado ou inativo'}
            
            # Renderizar template com variáveis
            rendered_content = template.content
            
            # Adicionar variáveis do contato
            contact = Contact.query.get(contact_id)
            if contact:
                variables.update({
                    'name': contact.name or 'Cliente',
                    'phone': contact.phone_number
                })
            
            for key, value in variables.items():
                rendered_content = rendered_content.replace(f"{{{key}}}", str(value))
            
            # Enviar mensagem
            result = self.whatsapp_service.send_message(
                account_id=account_id,
                to=phone_number,
                message_type=template.template_type,
                content=rendered_content,
                media_url=template.media_url
            )
            
            return {
                'action': 'template_sent',
                'template_id': template_id,
                'template_name': template.name,
                'rendered_content': rendered_content,
                'whatsapp_result': result
            }
            
        except Exception as e:
            logger.error(f"Erro ao enviar template: {e}")
            return {'error': str(e)}
    
    def _update_contact_status(self, contact_id: int, action_data: Dict) -> Dict:
        """Atualizar status do contato"""
        try:
            contact = Contact.query.get(contact_id)
            if not contact:
                return {'error': 'Contato não encontrado'}
            
            new_status = action_data.get('status')
            if not new_status:
                return {'error': 'Status não especificado'}
            
            old_status = contact.status
            contact.status = new_status
            contact.updated_at = datetime.utcnow()
            db.session.commit()
            
            return {
                'action': 'status_updated',
                'old_status': old_status,
                'new_status': new_status
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Erro ao atualizar status: {e}")
            return {'error': str(e)}
    
    def _save_contact_info(self, contact_id: int, message_data: Dict, action_data: Dict) -> Dict:
        """Salvar informações do contato automaticamente"""
        try:
            contact = Contact.query.get(contact_id)
            if not contact:
                return {'error': 'Contato não encontrado'}
            
            content = message_data.get('content', '')
            extraction_rules = action_data.get('extraction_rules', {})
            
            updated_fields = []
            
            # Extrair nome se não existir
            if not contact.name and 'name_pattern' in extraction_rules:
                name_pattern = extraction_rules['name_pattern']
                name_match = re.search(name_pattern, content, re.IGNORECASE)
                if name_match:
                    contact.name = name_match.group(1).strip()
                    updated_fields.append('name')
            
            # Extrair email
            if not contact.email and 'email_pattern' in extraction_rules:
                email_pattern = extraction_rules['email_pattern']
                email_match = re.search(email_pattern, content, re.IGNORECASE)
                if email_match:
                    contact.email = email_match.group(0).strip()
                    updated_fields.append('email')
            
            # Extrair valor de negociação
            if 'deal_value_pattern' in extraction_rules:
                deal_pattern = extraction_rules['deal_value_pattern']
                deal_match = re.search(deal_pattern, content, re.IGNORECASE)
                if deal_match:
                    try:
                        deal_value = float(deal_match.group(1).replace(',', '.'))
                        contact.deal_value = deal_value
                        updated_fields.append('deal_value')
                    except ValueError:
                        pass
            
            if updated_fields:
                contact.updated_at = datetime.utcnow()
                db.session.commit()
            
            return {
                'action': 'contact_info_saved',
                'updated_fields': updated_fields
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Erro ao salvar informações do contato: {e}")
            return {'error': str(e)}
    
    def _send_custom_message(self, contact_id: int, phone_number: str, action_data: Dict) -> Dict:
        """Enviar mensagem personalizada"""
        try:
            account_id = action_data.get('account_id', 1)
            message_content = action_data.get('content', '')
            message_type = action_data.get('message_type', 'text')
            media_url = action_data.get('media_url')
            
            if not message_content:
                return {'error': 'Conteúdo da mensagem não especificado'}
            
            # Substituir variáveis do contato
            contact = Contact.query.get(contact_id)
            if contact:
                message_content = message_content.replace('{name}', contact.name or 'Cliente')
                message_content = message_content.replace('{phone}', contact.phone_number)
            
            result = self.whatsapp_service.send_message(
                account_id=account_id,
                to=phone_number,
                message_type=message_type,
                content=message_content,
                media_url=media_url
            )
            
            return {
                'action': 'custom_message_sent',
                'content': message_content,
                'message_type': message_type,
                'whatsapp_result': result
            }
            
        except Exception as e:
            logger.error(f"Erro ao enviar mensagem personalizada: {e}")
            return {'error': str(e)}
    
    def create_default_rules(self) -> List[Dict]:
        """Criar regras de automação padrão"""
        try:
            default_rules = [
                {
                    'name': 'Saudação Automática',
                    'rule_type': 'auto_reply',
                    'trigger_keywords': ['oi', 'olá', 'bom dia', 'boa tarde', 'boa noite'],
                    'action_type': 'send_message',
                    'action_data': {
                        'content': 'Olá {name}! Obrigado por entrar em contato. Como posso ajudá-lo hoje?',
                        'message_type': 'text'
                    },
                    'priority': 1
                },
                {
                    'name': 'Tag Interessado',
                    'rule_type': 'tag',
                    'trigger_keywords': ['interessado', 'quero saber', 'tenho interesse'],
                    'action_type': 'add_tag',
                    'action_data': {
                        'tag': 'interessado'
                    },
                    'priority': 2
                },
                {
                    'name': 'Status Qualificado',
                    'rule_type': 'tag',
                    'trigger_keywords': ['orçamento', 'preço', 'valor', 'quanto custa'],
                    'action_type': 'update_status',
                    'action_data': {
                        'status': 'qualified'
                    },
                    'priority': 3
                }
            ]
            
            created_rules = []
            
            for rule_data in default_rules:
                # Verificar se a regra já existe
                existing_rule = AutomationRule.query.filter_by(name=rule_data['name']).first()
                if not existing_rule:
                    rule = AutomationRule(
                        name=rule_data['name'],
                        rule_type=rule_data['rule_type'],
                        trigger_keywords=json.dumps(rule_data['trigger_keywords']),
                        action_type=rule_data['action_type'],
                        action_data=json.dumps(rule_data['action_data']),
                        priority=rule_data['priority']
                    )
                    
                    db.session.add(rule)
                    created_rules.append(rule_data['name'])
            
            db.session.commit()
            
            return created_rules
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Erro ao criar regras padrão: {e}")
            return []

