import hashlib
import secrets
import jwt
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, List
from functools import wraps
from flask import request, jsonify, current_app
import re

logger = logging.getLogger(__name__)

class SecurityService:
    """Serviço de segurança e conformidade com LGPD"""
    
    def __init__(self):
        self.jwt_secret = secrets.token_urlsafe(32)
        self.token_expiry_hours = 24
        
        # Padrões para dados sensíveis (LGPD)
        self.sensitive_patterns = {
            'cpf': r'\d{3}\.?\d{3}\.?\d{3}-?\d{2}',
            'cnpj': r'\d{2}\.?\d{3}\.?\d{3}\/?\d{4}-?\d{2}',
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'(?:\+55\s?)?(?:\(?[1-9]{2}\)?\s?)?(?:9\s?)?[0-9]{4}[-\s]?[0-9]{4}',
            'credit_card': r'\b(?:\d{4}[-\s]?){3}\d{4}\b'
        }
    
    def generate_api_key(self, user_id: str) -> str:
        """Gerar chave de API para usuário"""
        try:
            timestamp = str(int(datetime.utcnow().timestamp()))
            raw_key = f"{user_id}:{timestamp}:{secrets.token_urlsafe(32)}"
            api_key = hashlib.sha256(raw_key.encode()).hexdigest()
            
            return f"wa_ai_{api_key[:32]}"
        except Exception as e:
            logger.error(f"Erro ao gerar API key: {e}")
            raise
    
    def generate_jwt_token(self, user_id: str, permissions: List[str] = None) -> str:
        """Gerar token JWT para autenticação"""
        try:
            payload = {
                'user_id': user_id,
                'permissions': permissions or [],
                'iat': datetime.utcnow(),
                'exp': datetime.utcnow() + timedelta(hours=self.token_expiry_hours)
            }
            
            token = jwt.encode(payload, self.jwt_secret, algorithm='HS256')
            return token
        except Exception as e:
            logger.error(f"Erro ao gerar JWT token: {e}")
            raise
    
    def verify_jwt_token(self, token: str) -> Optional[Dict]:
        """Verificar e decodificar token JWT"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("Token JWT expirado")
            return None
        except jwt.InvalidTokenError:
            logger.warning("Token JWT inválido")
            return None
        except Exception as e:
            logger.error(f"Erro ao verificar JWT token: {e}")
            return None
    
    def hash_password(self, password: str) -> str:
        """Hash de senha com salt"""
        try:
            salt = secrets.token_hex(16)
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return f"{salt}:{password_hash.hex()}"
        except Exception as e:
            logger.error(f"Erro ao fazer hash da senha: {e}")
            raise
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verificar senha contra hash"""
        try:
            salt, stored_hash = hashed_password.split(':')
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash.hex() == stored_hash
        except Exception as e:
            logger.error(f"Erro ao verificar senha: {e}")
            return False
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """Criptografar dados sensíveis (implementação básica)"""
        try:
            # Em produção, usar uma biblioteca de criptografia robusta como cryptography
            import base64
            encoded_data = base64.b64encode(data.encode()).decode()
            return f"enc_{encoded_data}"
        except Exception as e:
            logger.error(f"Erro ao criptografar dados: {e}")
            raise
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Descriptografar dados sensíveis"""
        try:
            if not encrypted_data.startswith('enc_'):
                return encrypted_data
            
            import base64
            encoded_data = encrypted_data[4:]  # Remove 'enc_' prefix
            decoded_data = base64.b64decode(encoded_data.encode()).decode()
            return decoded_data
        except Exception as e:
            logger.error(f"Erro ao descriptografar dados: {e}")
            raise
    
    def detect_sensitive_data(self, text: str) -> Dict[str, List[str]]:
        """Detectar dados sensíveis no texto (LGPD)"""
        detected = {}
        
        for data_type, pattern in self.sensitive_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                detected[data_type] = matches
        
        return detected
    
    def anonymize_data(self, text: str, anonymize_types: List[str] = None) -> str:
        """Anonimizar dados sensíveis no texto"""
        if anonymize_types is None:
            anonymize_types = list(self.sensitive_patterns.keys())
        
        anonymized_text = text
        
        for data_type in anonymize_types:
            if data_type in self.sensitive_patterns:
                pattern = self.sensitive_patterns[data_type]
                
                if data_type == 'cpf':
                    anonymized_text = re.sub(pattern, '***.***.***-**', anonymized_text)
                elif data_type == 'cnpj':
                    anonymized_text = re.sub(pattern, '**.***.***/****-**', anonymized_text)
                elif data_type == 'email':
                    anonymized_text = re.sub(pattern, '***@***.***', anonymized_text)
                elif data_type == 'phone':
                    anonymized_text = re.sub(pattern, '(**) ****-****', anonymized_text)
                elif data_type == 'credit_card':
                    anonymized_text = re.sub(pattern, '**** **** **** ****', anonymized_text)
        
        return anonymized_text
    
    def validate_whatsapp_webhook(self, verify_token: str, expected_token: str) -> bool:
        """Validar webhook do WhatsApp"""
        try:
            return secrets.compare_digest(verify_token, expected_token)
        except Exception as e:
            logger.error(f"Erro ao validar webhook: {e}")
            return False
    
    def rate_limit_check(self, identifier: str, max_requests: int = 100, window_minutes: int = 60) -> bool:
        """Verificar limite de taxa (rate limiting)"""
        # Implementação básica - em produção usar Redis ou similar
        # Por agora, sempre retorna True
        return True
    
    def log_security_event(self, event_type: str, details: Dict, severity: str = 'INFO'):
        """Registrar evento de segurança"""
        try:
            log_entry = {
                'timestamp': datetime.utcnow().isoformat(),
                'event_type': event_type,
                'severity': severity,
                'details': details,
                'ip_address': request.remote_addr if request else 'unknown'
            }
            
            if severity == 'CRITICAL':
                logger.critical(f"Security Event: {log_entry}")
            elif severity == 'WARNING':
                logger.warning(f"Security Event: {log_entry}")
            else:
                logger.info(f"Security Event: {log_entry}")
                
        except Exception as e:
            logger.error(f"Erro ao registrar evento de segurança: {e}")
    
    def validate_input(self, data: Dict, rules: Dict) -> Dict:
        """Validar entrada de dados"""
        errors = {}
        
        for field, rule in rules.items():
            value = data.get(field)
            
            # Campo obrigatório
            if rule.get('required', False) and not value:
                errors[field] = 'Campo obrigatório'
                continue
            
            if value:
                # Validação de tipo
                expected_type = rule.get('type')
                if expected_type and not isinstance(value, expected_type):
                    errors[field] = f'Tipo inválido, esperado {expected_type.__name__}'
                    continue
                
                # Validação de comprimento
                if isinstance(value, str):
                    min_length = rule.get('min_length')
                    max_length = rule.get('max_length')
                    
                    if min_length and len(value) < min_length:
                        errors[field] = f'Mínimo {min_length} caracteres'
                        continue
                    
                    if max_length and len(value) > max_length:
                        errors[field] = f'Máximo {max_length} caracteres'
                        continue
                
                # Validação de padrão
                pattern = rule.get('pattern')
                if pattern and isinstance(value, str):
                    if not re.match(pattern, value):
                        errors[field] = 'Formato inválido'
                        continue
        
        return errors
    
    def sanitize_input(self, text: str) -> str:
        """Sanitizar entrada de texto"""
        if not isinstance(text, str):
            return text
        
        # Remover caracteres perigosos
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
        sanitized = text
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        # Limitar comprimento
        max_length = 10000
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]
        
        return sanitized.strip()

def require_auth(f):
    """Decorator para exigir autenticação"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        
        if not token:
            return jsonify({'error': 'Token de autorização necessário'}), 401
        
        if token.startswith('Bearer '):
            token = token[7:]
        
        security_service = SecurityService()
        payload = security_service.verify_jwt_token(token)
        
        if not payload:
            return jsonify({'error': 'Token inválido ou expirado'}), 401
        
        request.current_user = payload
        return f(*args, **kwargs)
    
    return decorated_function

def require_permission(permission: str):
    """Decorator para exigir permissão específica"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(request, 'current_user'):
                return jsonify({'error': 'Usuário não autenticado'}), 401
            
            user_permissions = request.current_user.get('permissions', [])
            
            if permission not in user_permissions and 'admin' not in user_permissions:
                return jsonify({'error': 'Permissão insuficiente'}), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

