import requests
import json
import logging
from typing import Dict, List, Optional
from src.models.whatsapp_account import WhatsAppAccount
from src.models.contact import Contact
from src.models.message import Message
from src.models.user import db
from datetime import datetime

logger = logging.getLogger(__name__)

class WhatsAppService:
    """Serviço para integração com WhatsApp Business API"""
    
    def __init__(self):
        self.base_url = "https://graph.facebook.com/v18.0"
    
    def send_message(self, account_id: int, to: str, message_type: str, content: str, media_url: str = None) -> Dict:
        """Enviar mensagem via WhatsApp Business API"""
        try:
            account = WhatsAppAccount.query.get(account_id)
            if not account or not account.is_active:
                raise ValueError("Conta do WhatsApp não encontrada ou inativa")
            
            url = f"{self.base_url}/{account.business_phone_number_id}/messages"
            headers = {
                "Authorization": f"Bearer {account.access_token}",
                "Content-Type": "application/json"
            }
            
            # Preparar payload baseado no tipo de mensagem
            payload = {
                "messaging_product": "whatsapp",
                "to": to,
                "type": message_type
            }
            
            if message_type == "text":
                payload["text"] = {"body": content}
            elif message_type == "image":
                payload["image"] = {
                    "link": media_url,
                    "caption": content if content else ""
                }
            elif message_type == "audio":
                payload["audio"] = {"link": media_url}
            elif message_type == "video":
                payload["video"] = {
                    "link": media_url,
                    "caption": content if content else ""
                }
            elif message_type == "document":
                payload["document"] = {
                    "link": media_url,
                    "filename": content if content else "document"
                }
            
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            
            result = response.json()
            
            # Salvar mensagem no banco de dados
            self._save_outbound_message(
                account_id=account_id,
                to=to,
                content=content,
                message_type=message_type,
                media_url=media_url,
                whatsapp_message_id=result.get("messages", [{}])[0].get("id")
            )
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao enviar mensagem: {e}")
            raise
        except Exception as e:
            logger.error(f"Erro inesperado ao enviar mensagem: {e}")
            raise
    
    def send_template_message(self, account_id: int, to: str, template_name: str, language: str = "pt_BR", parameters: List[str] = None) -> Dict:
        """Enviar mensagem de template via WhatsApp Business API"""
        try:
            account = WhatsAppAccount.query.get(account_id)
            if not account or not account.is_active:
                raise ValueError("Conta do WhatsApp não encontrada ou inativa")
            
            url = f"{self.base_url}/{account.business_phone_number_id}/messages"
            headers = {
                "Authorization": f"Bearer {account.access_token}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "messaging_product": "whatsapp",
                "to": to,
                "type": "template",
                "template": {
                    "name": template_name,
                    "language": {"code": language}
                }
            }
            
            if parameters:
                payload["template"]["components"] = [{
                    "type": "body",
                    "parameters": [{"type": "text", "text": param} for param in parameters]
                }]
            
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            
            result = response.json()
            
            # Salvar mensagem no banco de dados
            self._save_outbound_message(
                account_id=account_id,
                to=to,
                content=f"Template: {template_name}",
                message_type="template",
                whatsapp_message_id=result.get("messages", [{}])[0].get("id")
            )
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao enviar template: {e}")
            raise
        except Exception as e:
            logger.error(f"Erro inesperado ao enviar template: {e}")
            raise
    
    def process_webhook(self, webhook_data: Dict) -> Dict:
        """Processar webhook recebido do WhatsApp"""
        try:
            if webhook_data.get("object") != "whatsapp_business_account":
                return {"status": "ignored", "reason": "Not a WhatsApp webhook"}
            
            entries = webhook_data.get("entry", [])
            processed_messages = []
            
            for entry in entries:
                changes = entry.get("changes", [])
                
                for change in changes:
                    if change.get("field") == "messages":
                        value = change.get("value", {})
                        messages = value.get("messages", [])
                        
                        for message in messages:
                            processed_message = self._process_incoming_message(message, value)
                            if processed_message:
                                processed_messages.append(processed_message)
            
            return {
                "status": "success",
                "processed_messages": processed_messages,
                "total": len(processed_messages)
            }
            
        except Exception as e:
            logger.error(f"Erro ao processar webhook: {e}")
            raise
    
    def _process_incoming_message(self, message_data: Dict, value_data: Dict) -> Optional[Dict]:
        """Processar mensagem recebida"""
        try:
            message_id = message_data.get("id")
            from_number = message_data.get("from")
            timestamp = datetime.fromtimestamp(int(message_data.get("timestamp", 0)))
            message_type = message_data.get("type", "text")
            
            # Extrair conteúdo baseado no tipo
            content = ""
            media_url = None
            
            if message_type == "text":
                content = message_data.get("text", {}).get("body", "")
            elif message_type in ["image", "video", "audio", "document"]:
                media_data = message_data.get(message_type, {})
                content = media_data.get("caption", "")
                media_url = media_data.get("link") or media_data.get("id")
            
            # Buscar ou criar contato
            contact = Contact.query.filter_by(phone_number=from_number).first()
            if not contact:
                contact = Contact(
                    phone_number=from_number,
                    name=value_data.get("contacts", [{}])[0].get("profile", {}).get("name", ""),
                    status="new"
                )
                db.session.add(contact)
                db.session.flush()  # Para obter o ID
            
            # Salvar mensagem
            message = Message(
                contact_id=contact.id,
                message_id=message_id,
                content=content,
                message_type=message_type,
                direction="inbound",
                timestamp=timestamp,
                media_url=media_url
            )
            
            db.session.add(message)
            db.session.commit()
            
            return {
                "message_id": message_id,
                "contact_id": contact.id,
                "phone_number": from_number,
                "content": content,
                "message_type": message_type,
                "timestamp": timestamp.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Erro ao processar mensagem recebida: {e}")
            db.session.rollback()
            return None
    
    def _save_outbound_message(self, account_id: int, to: str, content: str, message_type: str, whatsapp_message_id: str = None, media_url: str = None):
        """Salvar mensagem enviada no banco de dados"""
        try:
            # Buscar ou criar contato
            contact = Contact.query.filter_by(phone_number=to).first()
            if not contact:
                contact = Contact(
                    phone_number=to,
                    status="new"
                )
                db.session.add(contact)
                db.session.flush()
            
            # Salvar mensagem
            message = Message(
                contact_id=contact.id,
                message_id=whatsapp_message_id or f"out_{datetime.utcnow().timestamp()}",
                content=content,
                message_type=message_type,
                direction="outbound",
                media_url=media_url
            )
            
            db.session.add(message)
            db.session.commit()
            
        except Exception as e:
            logger.error(f"Erro ao salvar mensagem enviada: {e}")
            db.session.rollback()
    
    def get_account_info(self, account_id: int) -> Dict:
        """Obter informações da conta do WhatsApp Business"""
        try:
            account = WhatsAppAccount.query.get(account_id)
            if not account:
                raise ValueError("Conta não encontrada")
            
            url = f"{self.base_url}/{account.business_phone_number_id}"
            headers = {
                "Authorization": f"Bearer {account.access_token}"
            }
            
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao obter informações da conta: {e}")
            raise
        except Exception as e:
            logger.error(f"Erro inesperado ao obter informações da conta: {e}")
            raise
    
    def verify_webhook(self, verify_token: str, challenge: str, account_id: int = None) -> str:
        """Verificar webhook do WhatsApp"""
        try:
            if account_id:
                account = WhatsAppAccount.query.get(account_id)
                if account and account.webhook_verify_token == verify_token:
                    return challenge
            
            # Verificação genérica se não especificar conta
            return challenge if verify_token else None
            
        except Exception as e:
            logger.error(f"Erro ao verificar webhook: {e}")
            return None

