import re
import json
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

class AIService:
    """Serviço de IA para análise inteligente de mensagens"""
    
    def __init__(self):
        # Padrões para extração de informações
        self.patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'(?:\+55\s?)?(?:\(?[1-9]{2}\)?\s?)?(?:9\s?)?[0-9]{4}[-\s]?[0-9]{4}',
            'name': r'(?:meu nome é|me chamo|sou o|sou a)\s+([A-Za-zÀ-ÿ\s]+)',
            'money': r'R\$\s?(\d{1,3}(?:\.\d{3})*(?:,\d{2})?)',
            'cpf': r'\d{3}\.?\d{3}\.?\d{3}-?\d{2}',
            'cep': r'\d{5}-?\d{3}'
        }
        
        # Palavras-chave para classificação de intenção
        self.intent_keywords = {
            'greeting': ['oi', 'olá', 'bom dia', 'boa tarde', 'boa noite', 'e aí'],
            'interest': ['interessado', 'quero saber', 'tenho interesse', 'gostaria de'],
            'pricing': ['preço', 'valor', 'quanto custa', 'orçamento', 'cotação'],
            'complaint': ['problema', 'reclamação', 'não funciona', 'defeito', 'ruim'],
            'support': ['ajuda', 'suporte', 'dúvida', 'como fazer', 'não consigo'],
            'purchase': ['comprar', 'adquirir', 'fechar negócio', 'aceito', 'quero'],
            'goodbye': ['tchau', 'até logo', 'obrigado', 'valeu', 'falou']
        }
        
        # Sentimentos
        self.sentiment_keywords = {
            'positive': ['ótimo', 'excelente', 'perfeito', 'adorei', 'maravilhoso', 'bom'],
            'negative': ['ruim', 'péssimo', 'horrível', 'não gostei', 'problema', 'defeito'],
            'neutral': ['ok', 'normal', 'regular', 'mais ou menos']
        }
    
    def analyze_message(self, content: str, contact_data: Dict = None) -> Dict:
        """Análise completa de uma mensagem"""
        try:
            analysis = {
                'content': content,
                'timestamp': datetime.utcnow().isoformat(),
                'extracted_info': self.extract_information(content),
                'intent': self.classify_intent(content),
                'sentiment': self.analyze_sentiment(content),
                'urgency': self.assess_urgency(content),
                'suggested_tags': self.suggest_tags(content),
                'suggested_actions': self.suggest_actions(content, contact_data)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Erro na análise da mensagem: {e}")
            return {'error': str(e)}
    
    def extract_information(self, content: str) -> Dict:
        """Extrair informações estruturadas da mensagem"""
        extracted = {}
        
        for info_type, pattern in self.patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                if info_type == 'name':
                    # Para nomes, pegar apenas o primeiro match e limpar
                    extracted[info_type] = matches[0].strip().title()
                elif info_type == 'money':
                    # Para valores monetários, converter para float
                    try:
                        value = matches[0].replace('.', '').replace(',', '.')
                        extracted[info_type] = float(value)
                    except ValueError:
                        extracted[info_type] = matches[0]
                else:
                    extracted[info_type] = matches[0] if len(matches) == 1 else matches
        
        return extracted
    
    def classify_intent(self, content: str) -> Dict:
        """Classificar a intenção da mensagem"""
        content_lower = content.lower()
        intent_scores = {}
        
        for intent, keywords in self.intent_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in content_lower:
                    score += 1
            
            if score > 0:
                intent_scores[intent] = score
        
        if intent_scores:
            primary_intent = max(intent_scores, key=intent_scores.get)
            confidence = intent_scores[primary_intent] / len(content_lower.split())
            
            return {
                'primary': primary_intent,
                'confidence': min(confidence, 1.0),
                'all_intents': intent_scores
            }
        
        return {
            'primary': 'unknown',
            'confidence': 0.0,
            'all_intents': {}
        }
    
    def analyze_sentiment(self, content: str) -> Dict:
        """Analisar sentimento da mensagem"""
        content_lower = content.lower()
        sentiment_scores = {
            'positive': 0,
            'negative': 0,
            'neutral': 0
        }
        
        for sentiment, keywords in self.sentiment_keywords.items():
            for keyword in keywords:
                if keyword in content_lower:
                    sentiment_scores[sentiment] += 1
        
        total_score = sum(sentiment_scores.values())
        
        if total_score == 0:
            return {
                'sentiment': 'neutral',
                'confidence': 0.5,
                'scores': sentiment_scores
            }
        
        primary_sentiment = max(sentiment_scores, key=sentiment_scores.get)
        confidence = sentiment_scores[primary_sentiment] / total_score
        
        return {
            'sentiment': primary_sentiment,
            'confidence': confidence,
            'scores': sentiment_scores
        }
    
    def assess_urgency(self, content: str) -> Dict:
        """Avaliar urgência da mensagem"""
        urgency_indicators = {
            'high': ['urgente', 'emergência', 'imediato', 'agora', 'rápido', 'hoje'],
            'medium': ['importante', 'preciso', 'necessário', 'logo'],
            'low': ['quando possível', 'sem pressa', 'qualquer hora']
        }
        
        content_lower = content.lower()
        urgency_score = 0
        level = 'low'
        
        for urgency_level, indicators in urgency_indicators.items():
            for indicator in indicators:
                if indicator in content_lower:
                    if urgency_level == 'high':
                        urgency_score = 3
                        level = 'high'
                    elif urgency_level == 'medium' and urgency_score < 2:
                        urgency_score = 2
                        level = 'medium'
                    elif urgency_level == 'low' and urgency_score < 1:
                        urgency_score = 1
                        level = 'low'
        
        return {
            'level': level,
            'score': urgency_score,
            'requires_immediate_attention': urgency_score >= 3
        }
    
    def suggest_tags(self, content: str) -> List[str]:
        """Sugerir tags baseadas no conteúdo"""
        suggested_tags = []
        content_lower = content.lower()
        
        # Tags baseadas em intenção
        intent_analysis = self.classify_intent(content)
        if intent_analysis['primary'] != 'unknown':
            suggested_tags.append(intent_analysis['primary'])
        
        # Tags baseadas em sentimento
        sentiment_analysis = self.analyze_sentiment(content)
        if sentiment_analysis['confidence'] > 0.6:
            suggested_tags.append(f"sentiment_{sentiment_analysis['sentiment']}")
        
        # Tags específicas de negócio
        business_tags = {
            'lead_quente': ['interessado', 'quero comprar', 'fechar negócio'],
            'lead_frio': ['só olhando', 'talvez', 'pensando'],
            'cliente_vip': ['sempre compro', 'cliente fiel', 'há anos'],
            'primeira_compra': ['primeira vez', 'nunca comprei', 'novo cliente'],
            'reclamacao': ['problema', 'não funciona', 'defeito', 'reclamação'],
            'elogio': ['parabéns', 'excelente', 'adorei', 'recomendo']
        }
        
        for tag, keywords in business_tags.items():
            for keyword in keywords:
                if keyword in content_lower:
                    suggested_tags.append(tag)
                    break
        
        return list(set(suggested_tags))  # Remove duplicatas
    
    def suggest_actions(self, content: str, contact_data: Dict = None) -> List[Dict]:
        """Sugerir ações baseadas na análise"""
        actions = []
        
        intent_analysis = self.classify_intent(content)
        sentiment_analysis = self.analyze_sentiment(content)
        urgency_analysis = self.assess_urgency(content)
        extracted_info = self.extract_information(content)
        
        # Ações baseadas na intenção
        if intent_analysis['primary'] == 'greeting':
            actions.append({
                'type': 'send_template',
                'priority': 'medium',
                'description': 'Enviar saudação de boas-vindas',
                'template_category': 'greeting'
            })
        
        elif intent_analysis['primary'] == 'pricing':
            actions.append({
                'type': 'send_template',
                'priority': 'high',
                'description': 'Enviar informações de preços',
                'template_category': 'pricing'
            })
            actions.append({
                'type': 'update_status',
                'priority': 'medium',
                'description': 'Marcar como lead qualificado',
                'new_status': 'qualified'
            })
        
        elif intent_analysis['primary'] == 'complaint':
            actions.append({
                'type': 'add_tag',
                'priority': 'high',
                'description': 'Adicionar tag de reclamação',
                'tag': 'reclamacao'
            })
            actions.append({
                'type': 'notify_support',
                'priority': 'high',
                'description': 'Notificar equipe de suporte'
            })
        
        # Ações baseadas em informações extraídas
        if 'email' in extracted_info:
            actions.append({
                'type': 'save_contact_info',
                'priority': 'medium',
                'description': 'Salvar email do contato',
                'field': 'email',
                'value': extracted_info['email']
            })
        
        if 'name' in extracted_info:
            actions.append({
                'type': 'save_contact_info',
                'priority': 'medium',
                'description': 'Salvar nome do contato',
                'field': 'name',
                'value': extracted_info['name']
            })
        
        if 'money' in extracted_info:
            actions.append({
                'type': 'save_contact_info',
                'priority': 'medium',
                'description': 'Salvar valor de negociação',
                'field': 'deal_value',
                'value': extracted_info['money']
            })
        
        # Ações baseadas na urgência
        if urgency_analysis['requires_immediate_attention']:
            actions.append({
                'type': 'priority_notification',
                'priority': 'urgent',
                'description': 'Notificação prioritária para atendimento imediato'
            })
        
        # Ações baseadas no sentimento
        if sentiment_analysis['sentiment'] == 'negative' and sentiment_analysis['confidence'] > 0.7:
            actions.append({
                'type': 'escalate_to_manager',
                'priority': 'high',
                'description': 'Escalar para gerente devido ao sentimento negativo'
            })
        
        return actions
    
    def generate_response_suggestions(self, content: str, contact_data: Dict = None) -> List[str]:
        """Gerar sugestões de resposta"""
        suggestions = []
        
        intent_analysis = self.classify_intent(content)
        
        response_templates = {
            'greeting': [
                "Olá! Obrigado por entrar em contato. Como posso ajudá-lo hoje?",
                "Oi! Seja bem-vindo. Em que posso ser útil?",
                "Olá! É um prazer falar com você. O que você gostaria de saber?"
            ],
            'pricing': [
                "Claro! Vou enviar nossa tabela de preços. Você tem algum produto específico em mente?",
                "Perfeito! Para fazer um orçamento personalizado, preciso de algumas informações. Pode me ajudar?",
                "Ótima pergunta! Nossos preços variam conforme o produto. Qual seria seu interesse?"
            ],
            'support': [
                "Entendo sua dúvida. Vou ajudá-lo a resolver isso. Pode me dar mais detalhes?",
                "Sem problemas! Estou aqui para ajudar. Qual exatamente é a dificuldade?",
                "Claro que posso ajudar! Me conte mais sobre o que está acontecendo."
            ],
            'complaint': [
                "Peço desculpas pelo inconveniente. Vou resolver isso para você imediatamente.",
                "Lamento muito pelo problema. Pode me dar mais detalhes para que eu possa ajudar?",
                "Entendo sua frustração. Vamos resolver isso juntos. Me conte o que aconteceu."
            ]
        }
        
        primary_intent = intent_analysis.get('primary', 'unknown')
        if primary_intent in response_templates:
            suggestions.extend(response_templates[primary_intent])
        
        # Personalizar com nome se disponível
        if contact_data and contact_data.get('name'):
            name = contact_data['name']
            suggestions = [s.replace('Olá!', f'Olá, {name}!') for s in suggestions]
        
        return suggestions[:3]  # Retornar apenas as 3 melhores sugestões

