from flask import Blueprint, request, jsonify
from src.models.user import db
from src.models.contact import Contact
import json
from datetime import datetime

contacts_bp = Blueprint('contacts', __name__)

@contacts_bp.route('/contacts', methods=['GET'])
def get_contacts():
    """Listar todos os contatos com filtros opcionais"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        tag = request.args.get('tag')
        search = request.args.get('search')
        
        query = Contact.query
        
        if status:
            query = query.filter(Contact.status == status)
        
        if tag:
            query = query.filter(Contact.tags.contains(tag))
        
        if search:
            query = query.filter(
                db.or_(
                    Contact.name.contains(search),
                    Contact.phone_number.contains(search),
                    Contact.email.contains(search)
                )
            )
        
        contacts = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'contacts': [contact.to_dict() for contact in contacts.items],
            'total': contacts.total,
            'pages': contacts.pages,
            'current_page': page
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@contacts_bp.route('/contacts', methods=['POST'])
def create_contact():
    """Criar um novo contato"""
    try:
        data = request.get_json()
        
        # Verificar se o contato já existe
        existing_contact = Contact.query.filter_by(
            phone_number=data['phone_number']
        ).first()
        
        if existing_contact:
            return jsonify({'error': 'Contato já existe'}), 400
        
        contact = Contact(
            phone_number=data['phone_number'],
            name=data.get('name'),
            email=data.get('email'),
            notes=data.get('notes'),
            tags=json.dumps(data.get('tags', [])),
            status=data.get('status', 'new'),
            deal_value=data.get('deal_value')
        )
        
        db.session.add(contact)
        db.session.commit()
        
        return jsonify(contact.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@contacts_bp.route('/contacts/<int:contact_id>', methods=['GET'])
def get_contact(contact_id):
    """Obter um contato específico"""
    try:
        contact = Contact.query.get_or_404(contact_id)
        return jsonify(contact.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@contacts_bp.route('/contacts/<int:contact_id>', methods=['PUT'])
def update_contact(contact_id):
    """Atualizar um contato"""
    try:
        contact = Contact.query.get_or_404(contact_id)
        data = request.get_json()
        
        contact.name = data.get('name', contact.name)
        contact.email = data.get('email', contact.email)
        contact.notes = data.get('notes', contact.notes)
        contact.status = data.get('status', contact.status)
        contact.deal_value = data.get('deal_value', contact.deal_value)
        
        if 'tags' in data:
            contact.tags = json.dumps(data['tags'])
        
        contact.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify(contact.to_dict())
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@contacts_bp.route('/contacts/<int:contact_id>', methods=['DELETE'])
def delete_contact(contact_id):
    """Deletar um contato"""
    try:
        contact = Contact.query.get_or_404(contact_id)
        db.session.delete(contact)
        db.session.commit()
        
        return jsonify({'message': 'Contato deletado com sucesso'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@contacts_bp.route('/contacts/export', methods=['GET'])
def export_contacts():
    """Exportar contatos em formato CSV"""
    try:
        contacts = Contact.query.all()
        
        # Criar dados CSV
        csv_data = "phone_number,name,email,status,tags,deal_value,created_at\n"
        for contact in contacts:
            csv_data += f"{contact.phone_number},{contact.name or ''},{contact.email or ''},{contact.status},{contact.tags or ''},{contact.deal_value or ''},{contact.created_at}\n"
        
        return csv_data, 200, {
            'Content-Type': 'text/csv',
            'Content-Disposition': 'attachment; filename=contacts.csv'
        }
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@contacts_bp.route('/contacts/stats', methods=['GET'])
def get_contacts_stats():
    """Obter estatísticas dos contatos"""
    try:
        total_contacts = Contact.query.count()
        new_contacts = Contact.query.filter_by(status='new').count()
        qualified_contacts = Contact.query.filter_by(status='qualified').count()
        converted_contacts = Contact.query.filter_by(status='converted').count()
        
        total_deal_value = db.session.query(
            db.func.sum(Contact.deal_value)
        ).scalar() or 0
        
        return jsonify({
            'total_contacts': total_contacts,
            'new_contacts': new_contacts,
            'qualified_contacts': qualified_contacts,
            'converted_contacts': converted_contacts,
            'total_deal_value': total_deal_value
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

