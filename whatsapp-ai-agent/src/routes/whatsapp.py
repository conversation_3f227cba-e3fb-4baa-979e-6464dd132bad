from flask import Blueprint, request, jsonify
from src.models.user import db
from src.models.whatsapp_account import WhatsAppAccount
from src.models.message import Message
from src.services.whatsapp_service import WhatsAppService
import json
from datetime import datetime

whatsapp_bp = Blueprint('whatsapp', __name__)
whatsapp_service = WhatsAppService()

@whatsapp_bp.route('/whatsapp/accounts', methods=['GET'])
def get_whatsapp_accounts():
    """Listar todas as contas do WhatsApp"""
    try:
        accounts = WhatsAppAccount.query.all()
        return jsonify([account.to_dict() for account in accounts])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@whatsapp_bp.route('/whatsapp/accounts', methods=['POST'])
def create_whatsapp_account():
    """Criar uma nova conta do WhatsApp Business"""
    try:
        data = request.get_json()
        
        # Verificar se a conta já existe
        existing_account = WhatsAppAccount.query.filter_by(
            business_phone_number_id=data['business_phone_number_id']
        ).first()
        
        if existing_account:
            return jsonify({'error': 'Conta já existe'}), 400
        
        account = WhatsAppAccount(
            business_phone_number_id=data['business_phone_number_id'],
            phone_number=data['phone_number'],
            display_name=data.get('display_name'),
            access_token=data['access_token'],
            webhook_verify_token=data['webhook_verify_token']
        )
        
        db.session.add(account)
        db.session.commit()
        
        return jsonify(account.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@whatsapp_bp.route('/whatsapp/accounts/<int:account_id>', methods=['PUT'])
def update_whatsapp_account(account_id):
    """Atualizar uma conta do WhatsApp"""
    try:
        account = WhatsAppAccount.query.get_or_404(account_id)
        data = request.get_json()
        
        account.display_name = data.get('display_name', account.display_name)
        account.access_token = data.get('access_token', account.access_token)
        account.webhook_verify_token = data.get('webhook_verify_token', account.webhook_verify_token)
        account.is_active = data.get('is_active', account.is_active)
        account.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify(account.to_dict())
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@whatsapp_bp.route('/whatsapp/accounts/<int:account_id>', methods=['DELETE'])
def delete_whatsapp_account(account_id):
    """Deletar uma conta do WhatsApp"""
    try:
        account = WhatsAppAccount.query.get_or_404(account_id)
        db.session.delete(account)
        db.session.commit()
        
        return jsonify({'message': 'Conta deletada com sucesso'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@whatsapp_bp.route('/whatsapp/send-message', methods=['POST'])
def send_message():
    """Enviar mensagem via WhatsApp"""
    try:
        data = request.get_json()
        
        result = whatsapp_service.send_message(
            account_id=data['account_id'],
            to=data['to'],
            message_type=data.get('message_type', 'text'),
            content=data['content'],
            media_url=data.get('media_url')
        )
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@whatsapp_bp.route('/whatsapp/send-template', methods=['POST'])
def send_template():
    """Enviar template via WhatsApp"""
    try:
        data = request.get_json()
        
        result = whatsapp_service.send_template_message(
            account_id=data['account_id'],
            to=data['to'],
            template_name=data['template_name'],
            language=data.get('language', 'pt_BR'),
            parameters=data.get('parameters', [])
        )
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@whatsapp_bp.route('/whatsapp/webhook', methods=['GET'])
def verify_webhook():
    """Verificar webhook do WhatsApp"""
    try:
        verify_token = request.args.get('hub.verify_token')
        challenge = request.args.get('hub.challenge')
        
        if verify_token and challenge:
            result = whatsapp_service.verify_webhook(verify_token, challenge)
            if result:
                return result
        
        return 'Forbidden', 403
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@whatsapp_bp.route('/whatsapp/webhook', methods=['POST'])
def receive_webhook():
    """Receber webhook do WhatsApp"""
    try:
        data = request.get_json()
        
        result = whatsapp_service.process_webhook(data)
        
        # Processar automações para mensagens recebidas
        if result.get('processed_messages'):
            from src.services.automation_service import AutomationService
            automation_service = AutomationService()
            
            for message in result['processed_messages']:
                automation_service.process_message(message)
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@whatsapp_bp.route('/whatsapp/messages', methods=['GET'])
def get_messages():
    """Listar mensagens com filtros"""
    try:
        contact_id = request.args.get('contact_id', type=int)
        direction = request.args.get('direction')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        query = Message.query
        
        if contact_id:
            query = query.filter(Message.contact_id == contact_id)
        
        if direction:
            query = query.filter(Message.direction == direction)
        
        messages = query.order_by(Message.timestamp.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'messages': [message.to_dict() for message in messages.items],
            'total': messages.total,
            'pages': messages.pages,
            'current_page': page
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@whatsapp_bp.route('/whatsapp/accounts/<int:account_id>/info', methods=['GET'])
def get_account_info(account_id):
    """Obter informações da conta do WhatsApp Business"""
    try:
        result = whatsapp_service.get_account_info(account_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@whatsapp_bp.route('/whatsapp/accounts/<int:account_id>/sync', methods=['POST'])
def sync_account(account_id):
    """Sincronizar conta com WhatsApp Business API"""
    try:
        account = WhatsAppAccount.query.get_or_404(account_id)
        
        # Atualizar informações da conta
        info = whatsapp_service.get_account_info(account_id)
        
        if info:
            account.display_name = info.get('display_phone_number', account.display_name)
            account.last_sync = datetime.utcnow()
            db.session.commit()
        
        return jsonify({
            'message': 'Conta sincronizada com sucesso',
            'last_sync': account.last_sync.isoformat() if account.last_sync else None
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

