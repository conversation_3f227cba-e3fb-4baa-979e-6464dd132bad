from flask import Blueprint, request, jsonify
from src.models.user import db
from src.models.automation_rule import AutomationRule
import json
from datetime import datetime

automation_bp = Blueprint('automation', __name__)

@automation_bp.route('/automation/rules', methods=['GET'])
def get_automation_rules():
    """Listar todas as regras de automação"""
    try:
        rule_type = request.args.get('type')
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        query = AutomationRule.query
        
        if active_only:
            query = query.filter(AutomationRule.is_active == True)
        
        if rule_type:
            query = query.filter(AutomationRule.rule_type == rule_type)
        
        rules = query.order_by(AutomationRule.priority.desc()).all()
        
        return jsonify([rule.to_dict() for rule in rules])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/rules', methods=['POST'])
def create_automation_rule():
    """Criar uma nova regra de automação"""
    try:
        data = request.get_json()
        
        rule = AutomationRule(
            name=data['name'],
            rule_type=data['rule_type'],
            trigger_keywords=json.dumps(data.get('trigger_keywords', [])),
            trigger_conditions=json.dumps(data.get('trigger_conditions', {})),
            action_type=data['action_type'],
            action_data=json.dumps(data.get('action_data', {})),
            priority=data.get('priority', 1)
        )
        
        db.session.add(rule)
        db.session.commit()
        
        return jsonify(rule.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/rules/<int:rule_id>', methods=['GET'])
def get_automation_rule(rule_id):
    """Obter uma regra de automação específica"""
    try:
        rule = AutomationRule.query.get_or_404(rule_id)
        return jsonify(rule.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/rules/<int:rule_id>', methods=['PUT'])
def update_automation_rule(rule_id):
    """Atualizar uma regra de automação"""
    try:
        rule = AutomationRule.query.get_or_404(rule_id)
        data = request.get_json()
        
        rule.name = data.get('name', rule.name)
        rule.rule_type = data.get('rule_type', rule.rule_type)
        rule.action_type = data.get('action_type', rule.action_type)
        rule.is_active = data.get('is_active', rule.is_active)
        rule.priority = data.get('priority', rule.priority)
        
        if 'trigger_keywords' in data:
            rule.trigger_keywords = json.dumps(data['trigger_keywords'])
        
        if 'trigger_conditions' in data:
            rule.trigger_conditions = json.dumps(data['trigger_conditions'])
        
        if 'action_data' in data:
            rule.action_data = json.dumps(data['action_data'])
        
        rule.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify(rule.to_dict())
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/rules/<int:rule_id>', methods=['DELETE'])
def delete_automation_rule(rule_id):
    """Deletar uma regra de automação"""
    try:
        rule = AutomationRule.query.get_or_404(rule_id)
        db.session.delete(rule)
        db.session.commit()
        
        return jsonify({'message': 'Regra de automação deletada com sucesso'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/rules/<int:rule_id>/toggle', methods=['POST'])
def toggle_automation_rule(rule_id):
    """Ativar/desativar uma regra de automação"""
    try:
        rule = AutomationRule.query.get_or_404(rule_id)
        rule.is_active = not rule.is_active
        rule.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'message': f'Regra {"ativada" if rule.is_active else "desativada"} com sucesso',
            'is_active': rule.is_active
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@automation_bp.route('/automation/process-message', methods=['POST'])
def process_message():
    """Processar uma mensagem através das regras de automação"""
    try:
        data = request.get_json()
        message_content = data.get('content', '').lower()
        contact_phone = data.get('contact_phone')
        
        # Buscar regras ativas ordenadas por prioridade
        rules = AutomationRule.query.filter_by(is_active=True).order_by(
            AutomationRule.priority.desc()
        ).all()
        
        triggered_actions = []
        
        for rule in rules:
            # Verificar se a regra deve ser acionada
            trigger_keywords = json.loads(rule.trigger_keywords or '[]')
            
            # Verificar palavras-chave
            keyword_match = False
            if trigger_keywords:
                for keyword in trigger_keywords:
                    if keyword.lower() in message_content:
                        keyword_match = True
                        break
            else:
                keyword_match = True  # Se não há palavras-chave, sempre aciona
            
            if keyword_match:
                action_data = json.loads(rule.action_data or '{}')
                
                triggered_actions.append({
                    'rule_id': rule.id,
                    'rule_name': rule.name,
                    'action_type': rule.action_type,
                    'action_data': action_data
                })
        
        return jsonify({
            'triggered_actions': triggered_actions,
            'total_actions': len(triggered_actions)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

