from flask import Blueprint, request, jsonify
from src.models.user import db, User
from src.services.security_service import SecurityService, require_auth
import json
from datetime import datetime

auth_bp = Blueprint('auth', __name__)
security_service = SecurityService()

@auth_bp.route('/auth/register', methods=['POST'])
def register():
    """Registrar novo usuário"""
    try:
        data = request.get_json()
        
        # Validar entrada
        validation_rules = {
            'username': {'required': True, 'type': str, 'min_length': 3, 'max_length': 50},
            'email': {'required': True, 'type': str, 'pattern': r'^[^@]+@[^@]+\.[^@]+$'},
            'password': {'required': True, 'type': str, 'min_length': 8}
        }
        
        errors = security_service.validate_input(data, validation_rules)
        if errors:
            return jsonify({'errors': errors}), 400
        
        # Sanitizar dados
        username = security_service.sanitize_input(data['username'])
        email = security_service.sanitize_input(data['email'])
        password = data['password']
        
        # Verificar se usuário já existe
        existing_user = User.query.filter(
            db.or_(User.username == username, User.email == email)
        ).first()
        
        if existing_user:
            return jsonify({'error': 'Usuário ou email já existe'}), 400
        
        # Criar novo usuário
        password_hash = security_service.hash_password(password)
        
        user = User(
            username=username,
            email=email
        )
        
        # Adicionar campo de senha ao modelo se necessário
        if hasattr(user, 'password_hash'):
            user.password_hash = password_hash
        
        db.session.add(user)
        db.session.commit()
        
        # Gerar token
        token = security_service.generate_jwt_token(
            user_id=str(user.id),
            permissions=['user']
        )
        
        # Log do evento
        security_service.log_security_event(
            'user_registration',
            {'user_id': user.id, 'username': username, 'email': email}
        )
        
        return jsonify({
            'message': 'Usuário criado com sucesso',
            'token': token,
            'user': user.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        security_service.log_security_event(
            'registration_error',
            {'error': str(e)},
            'WARNING'
        )
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/auth/login', methods=['POST'])
def login():
    """Fazer login do usuário"""
    try:
        data = request.get_json()
        
        # Validar entrada
        validation_rules = {
            'username': {'required': True, 'type': str},
            'password': {'required': True, 'type': str}
        }
        
        errors = security_service.validate_input(data, validation_rules)
        if errors:
            return jsonify({'errors': errors}), 400
        
        username = security_service.sanitize_input(data['username'])
        password = data['password']
        
        # Buscar usuário
        user = User.query.filter(
            db.or_(User.username == username, User.email == username)
        ).first()
        
        if not user:
            security_service.log_security_event(
                'login_attempt_failed',
                {'username': username, 'reason': 'user_not_found'},
                'WARNING'
            )
            return jsonify({'error': 'Credenciais inválidas'}), 401
        
        # Verificar senha (se o campo existir)
        if hasattr(user, 'password_hash'):
            if not security_service.verify_password(password, user.password_hash):
                security_service.log_security_event(
                    'login_attempt_failed',
                    {'user_id': user.id, 'username': username, 'reason': 'invalid_password'},
                    'WARNING'
                )
                return jsonify({'error': 'Credenciais inválidas'}), 401
        
        # Gerar token
        token = security_service.generate_jwt_token(
            user_id=str(user.id),
            permissions=['user']
        )
        
        # Log do evento
        security_service.log_security_event(
            'user_login',
            {'user_id': user.id, 'username': user.username}
        )
        
        return jsonify({
            'message': 'Login realizado com sucesso',
            'token': token,
            'user': user.to_dict()
        })
        
    except Exception as e:
        security_service.log_security_event(
            'login_error',
            {'error': str(e)},
            'WARNING'
        )
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/auth/verify-token', methods=['POST'])
def verify_token():
    """Verificar validade do token"""
    try:
        data = request.get_json()
        token = data.get('token')
        
        if not token:
            return jsonify({'error': 'Token necessário'}), 400
        
        payload = security_service.verify_jwt_token(token)
        
        if not payload:
            return jsonify({'valid': False, 'error': 'Token inválido ou expirado'}), 401
        
        return jsonify({
            'valid': True,
            'payload': payload
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/auth/generate-api-key', methods=['POST'])
@require_auth
def generate_api_key():
    """Gerar chave de API para o usuário"""
    try:
        user_id = request.current_user['user_id']
        
        api_key = security_service.generate_api_key(user_id)
        
        # Log do evento
        security_service.log_security_event(
            'api_key_generated',
            {'user_id': user_id}
        )
        
        return jsonify({
            'api_key': api_key,
            'message': 'Chave de API gerada com sucesso'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/auth/profile', methods=['GET'])
@require_auth
def get_profile():
    """Obter perfil do usuário autenticado"""
    try:
        user_id = request.current_user['user_id']
        
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'Usuário não encontrado'}), 404
        
        return jsonify({
            'user': user.to_dict(),
            'permissions': request.current_user.get('permissions', [])
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/auth/change-password', methods=['POST'])
@require_auth
def change_password():
    """Alterar senha do usuário"""
    try:
        data = request.get_json()
        user_id = request.current_user['user_id']
        
        # Validar entrada
        validation_rules = {
            'current_password': {'required': True, 'type': str},
            'new_password': {'required': True, 'type': str, 'min_length': 8}
        }
        
        errors = security_service.validate_input(data, validation_rules)
        if errors:
            return jsonify({'errors': errors}), 400
        
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'Usuário não encontrado'}), 404
        
        # Verificar senha atual (se o campo existir)
        if hasattr(user, 'password_hash'):
            if not security_service.verify_password(data['current_password'], user.password_hash):
                security_service.log_security_event(
                    'password_change_failed',
                    {'user_id': user_id, 'reason': 'invalid_current_password'},
                    'WARNING'
                )
                return jsonify({'error': 'Senha atual incorreta'}), 400
            
            # Atualizar senha
            user.password_hash = security_service.hash_password(data['new_password'])
            db.session.commit()
        
        # Log do evento
        security_service.log_security_event(
            'password_changed',
            {'user_id': user_id}
        )
        
        return jsonify({'message': 'Senha alterada com sucesso'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/auth/logout', methods=['POST'])
@require_auth
def logout():
    """Fazer logout do usuário"""
    try:
        user_id = request.current_user['user_id']
        
        # Log do evento
        security_service.log_security_event(
            'user_logout',
            {'user_id': user_id}
        )
        
        return jsonify({'message': 'Logout realizado com sucesso'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/auth/data-privacy/request', methods=['POST'])
@require_auth
def request_data_privacy():
    """Solicitar dados pessoais (LGPD)"""
    try:
        user_id = request.current_user['user_id']
        data = request.get_json()
        request_type = data.get('type')  # 'export', 'delete', 'anonymize'
        
        if request_type not in ['export', 'delete', 'anonymize']:
            return jsonify({'error': 'Tipo de solicitação inválido'}), 400
        
        # Log da solicitação
        security_service.log_security_event(
            'data_privacy_request',
            {'user_id': user_id, 'request_type': request_type}
        )
        
        # Em uma implementação real, isso seria processado em background
        return jsonify({
            'message': f'Solicitação de {request_type} registrada com sucesso',
            'request_id': f'req_{user_id}_{int(datetime.utcnow().timestamp())}',
            'status': 'pending'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/auth/data-privacy/anonymize-text', methods=['POST'])
@require_auth
def anonymize_text():
    """Anonimizar texto (LGPD)"""
    try:
        data = request.get_json()
        text = data.get('text', '')
        anonymize_types = data.get('types', None)
        
        if not text:
            return jsonify({'error': 'Texto necessário'}), 400
        
        # Detectar dados sensíveis
        detected = security_service.detect_sensitive_data(text)
        
        # Anonimizar texto
        anonymized_text = security_service.anonymize_data(text, anonymize_types)
        
        return jsonify({
            'original_text': text,
            'anonymized_text': anonymized_text,
            'detected_data': detected
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

