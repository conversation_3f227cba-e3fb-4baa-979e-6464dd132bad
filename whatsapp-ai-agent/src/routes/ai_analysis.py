from flask import Blueprint, request, jsonify
from src.models.contact import Contact
from src.models.message import Message
from src.services.ai_service import AIService
from src.services.automation_service import AutomationService
import json

ai_bp = Blueprint('ai', __name__)
ai_service = AIService()
automation_service = AutomationService()

@ai_bp.route('/ai/analyze-message', methods=['POST'])
def analyze_message():
    """Analisar mensagem com IA"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        contact_id = data.get('contact_id')
        
        contact_data = None
        if contact_id:
            contact = Contact.query.get(contact_id)
            if contact:
                contact_data = contact.to_dict()
        
        analysis = ai_service.analyze_message(content, contact_data)
        
        return jsonify(analysis)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/ai/suggest-response', methods=['POST'])
def suggest_response():
    """Sugerir respostas para uma mensagem"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        contact_id = data.get('contact_id')
        
        contact_data = None
        if contact_id:
            contact = Contact.query.get(contact_id)
            if contact:
                contact_data = contact.to_dict()
        
        suggestions = ai_service.generate_response_suggestions(content, contact_data)
        
        return jsonify({
            'suggestions': suggestions,
            'total': len(suggestions)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/ai/suggest-tags', methods=['POST'])
def suggest_tags():
    """Sugerir tags para uma mensagem"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        
        tags = ai_service.suggest_tags(content)
        
        return jsonify({
            'suggested_tags': tags,
            'total': len(tags)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/ai/extract-info', methods=['POST'])
def extract_information():
    """Extrair informações estruturadas de uma mensagem"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        
        extracted_info = ai_service.extract_information(content)
        
        return jsonify({
            'extracted_info': extracted_info,
            'fields_found': len(extracted_info)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/ai/analyze-conversation', methods=['POST'])
def analyze_conversation():
    """Analisar uma conversa completa"""
    try:
        data = request.get_json()
        contact_id = data.get('contact_id')
        limit = data.get('limit', 10)
        
        if not contact_id:
            return jsonify({'error': 'contact_id é obrigatório'}), 400
        
        # Buscar mensagens da conversa
        messages = Message.query.filter_by(contact_id=contact_id).order_by(
            Message.timestamp.desc()
        ).limit(limit).all()
        
        if not messages:
            return jsonify({'error': 'Nenhuma mensagem encontrada'}), 404
        
        # Analisar cada mensagem
        conversation_analysis = {
            'contact_id': contact_id,
            'total_messages': len(messages),
            'messages_analysis': [],
            'conversation_summary': {
                'dominant_intent': {},
                'overall_sentiment': {},
                'urgency_level': 'low',
                'suggested_actions': []
            }
        }
        
        intent_counts = {}
        sentiment_counts = {}
        max_urgency = 0
        
        contact = Contact.query.get(contact_id)
        contact_data = contact.to_dict() if contact else None
        
        for message in reversed(messages):  # Analisar em ordem cronológica
            if message.direction == 'inbound':  # Apenas mensagens recebidas
                analysis = ai_service.analyze_message(message.content, contact_data)
                
                conversation_analysis['messages_analysis'].append({
                    'message_id': message.id,
                    'content': message.content,
                    'timestamp': message.timestamp.isoformat(),
                    'analysis': analysis
                })
                
                # Contar intenções
                intent = analysis.get('intent', {}).get('primary', 'unknown')
                intent_counts[intent] = intent_counts.get(intent, 0) + 1
                
                # Contar sentimentos
                sentiment = analysis.get('sentiment', {}).get('sentiment', 'neutral')
                sentiment_counts[sentiment] = sentiment_counts.get(sentiment, 0) + 1
                
                # Verificar urgência máxima
                urgency_score = analysis.get('urgency', {}).get('score', 0)
                if urgency_score > max_urgency:
                    max_urgency = urgency_score
        
        # Resumo da conversa
        if intent_counts:
            conversation_analysis['conversation_summary']['dominant_intent'] = {
                'intent': max(intent_counts, key=intent_counts.get),
                'count': max(intent_counts.values()),
                'distribution': intent_counts
            }
        
        if sentiment_counts:
            conversation_analysis['conversation_summary']['overall_sentiment'] = {
                'sentiment': max(sentiment_counts, key=sentiment_counts.get),
                'count': max(sentiment_counts.values()),
                'distribution': sentiment_counts
            }
        
        # Nível de urgência
        if max_urgency >= 3:
            conversation_analysis['conversation_summary']['urgency_level'] = 'high'
        elif max_urgency >= 2:
            conversation_analysis['conversation_summary']['urgency_level'] = 'medium'
        else:
            conversation_analysis['conversation_summary']['urgency_level'] = 'low'
        
        return jsonify(conversation_analysis)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/ai/auto-process', methods=['POST'])
def auto_process_message():
    """Processar mensagem automaticamente com IA e automação"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        contact_id = data.get('contact_id')
        phone_number = data.get('phone_number')
        
        if not content:
            return jsonify({'error': 'Conteúdo da mensagem é obrigatório'}), 400
        
        # Análise com IA
        contact_data = None
        if contact_id:
            contact = Contact.query.get(contact_id)
            if contact:
                contact_data = contact.to_dict()
        
        ai_analysis = ai_service.analyze_message(content, contact_data)
        
        # Processamento de automação
        message_data = {
            'content': content,
            'contact_id': contact_id,
            'phone_number': phone_number
        }
        
        automation_result = automation_service.process_message(message_data)
        
        return jsonify({
            'ai_analysis': ai_analysis,
            'automation_result': automation_result,
            'processed_at': ai_analysis.get('timestamp')
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/ai/setup-default-rules', methods=['POST'])
def setup_default_automation_rules():
    """Configurar regras de automação padrão"""
    try:
        created_rules = automation_service.create_default_rules()
        
        return jsonify({
            'message': 'Regras padrão criadas com sucesso',
            'created_rules': created_rules,
            'total': len(created_rules)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

