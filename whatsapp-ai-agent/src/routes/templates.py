from flask import Blueprint, request, jsonify
from src.models.user import db
from src.models.template import Template
import json
from datetime import datetime

templates_bp = Blueprint('templates', __name__)

@templates_bp.route('/templates', methods=['GET'])
def get_templates():
    """Listar todos os templates"""
    try:
        category = request.args.get('category')
        template_type = request.args.get('type')
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        query = Template.query
        
        if active_only:
            query = query.filter(Template.is_active == True)
        
        if category:
            query = query.filter(Template.category == category)
        
        if template_type:
            query = query.filter(Template.template_type == template_type)
        
        templates = query.order_by(Template.created_at.desc()).all()
        
        return jsonify([template.to_dict() for template in templates])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@templates_bp.route('/templates', methods=['POST'])
def create_template():
    """Criar um novo template"""
    try:
        data = request.get_json()
        
        template = Template(
            name=data['name'],
            content=data['content'],
            template_type=data.get('template_type', 'text'),
            category=data.get('category'),
            media_url=data.get('media_url'),
            variables=json.dumps(data.get('variables', []))
        )
        
        db.session.add(template)
        db.session.commit()
        
        return jsonify(template.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@templates_bp.route('/templates/<int:template_id>', methods=['GET'])
def get_template(template_id):
    """Obter um template específico"""
    try:
        template = Template.query.get_or_404(template_id)
        return jsonify(template.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@templates_bp.route('/templates/<int:template_id>', methods=['PUT'])
def update_template(template_id):
    """Atualizar um template"""
    try:
        template = Template.query.get_or_404(template_id)
        data = request.get_json()
        
        template.name = data.get('name', template.name)
        template.content = data.get('content', template.content)
        template.template_type = data.get('template_type', template.template_type)
        template.category = data.get('category', template.category)
        template.media_url = data.get('media_url', template.media_url)
        template.is_active = data.get('is_active', template.is_active)
        
        if 'variables' in data:
            template.variables = json.dumps(data['variables'])
        
        template.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify(template.to_dict())
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@templates_bp.route('/templates/<int:template_id>', methods=['DELETE'])
def delete_template(template_id):
    """Deletar um template"""
    try:
        template = Template.query.get_or_404(template_id)
        db.session.delete(template)
        db.session.commit()
        
        return jsonify({'message': 'Template deletado com sucesso'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@templates_bp.route('/templates/<int:template_id>/render', methods=['POST'])
def render_template(template_id):
    """Renderizar um template com variáveis"""
    try:
        template = Template.query.get_or_404(template_id)
        data = request.get_json()
        variables = data.get('variables', {})
        
        # Renderizar o template substituindo as variáveis
        rendered_content = template.content
        for key, value in variables.items():
            rendered_content = rendered_content.replace(f"{{{key}}}", str(value))
        
        return jsonify({
            'rendered_content': rendered_content,
            'template_type': template.template_type,
            'media_url': template.media_url
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@templates_bp.route('/templates/categories', methods=['GET'])
def get_template_categories():
    """Obter todas as categorias de templates"""
    try:
        categories = db.session.query(Template.category).distinct().all()
        categories = [cat[0] for cat in categories if cat[0]]
        
        return jsonify(categories)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

