from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from src.models.user import db

class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    contact_id = db.Column(db.Integer, db.<PERSON><PERSON>('contact.id'), nullable=False)
    message_id = db.Column(db.String(100), unique=True, nullable=False)  # WhatsApp message ID
    content = db.Column(db.Text, nullable=False)
    message_type = db.Column(db.String(20), default='text')  # text, image, audio, video, document
    direction = db.Column(db.String(10), nullable=False)  # inbound, outbound
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='sent')  # sent, delivered, read, failed
    media_url = db.Column(db.String(500), nullable=True)
    
    # Relationship
    contact = db.relationship('Contact', backref=db.backref('messages', lazy=True))
    
    def __repr__(self):
        return f'<Message {self.message_id}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'contact_id': self.contact_id,
            'message_id': self.message_id,
            'content': self.content,
            'message_type': self.message_type,
            'direction': self.direction,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'status': self.status,
            'media_url': self.media_url
        }

