from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from src.models.user import db

class AutomationRule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    rule_type = db.Column(db.String(20), nullable=False)  # tag, auto_reply, contact_save
    trigger_keywords = db.Column(db.Text, nullable=True)  # JSON array of keywords
    trigger_conditions = db.Column(db.Text, nullable=True)  # JSON object with conditions
    action_type = db.Column(db.String(20), nullable=False)  # add_tag, send_template, save_contact
    action_data = db.Column(db.Text, nullable=True)  # JSON object with action data
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    priority = db.Column(db.Integer, default=1)  # Higher number = higher priority
    
    def __repr__(self):
        return f'<AutomationRule {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'rule_type': self.rule_type,
            'trigger_keywords': self.trigger_keywords,
            'trigger_conditions': self.trigger_conditions,
            'action_type': self.action_type,
            'action_data': self.action_data,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'priority': self.priority
        }

