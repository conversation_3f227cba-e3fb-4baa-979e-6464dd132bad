from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from src.models.user import db

class Template(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    template_type = db.Column(db.String(20), default='text')  # text, image, audio, video
    category = db.Column(db.String(50), nullable=True)  # greeting, follow_up, closing, etc.
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    media_url = db.Column(db.String(500), nullable=True)
    variables = db.Column(db.Text, nullable=True)  # JSON string of variables like {name}, {company}
    
    def __repr__(self):
        return f'<Template {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'content': self.content,
            'template_type': self.template_type,
            'category': self.category,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active,
            'media_url': self.media_url,
            'variables': self.variables
        }

