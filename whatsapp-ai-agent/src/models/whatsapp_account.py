from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from src.models.user import db

class WhatsAppAccount(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    business_phone_number_id = db.Column(db.String(100), unique=True, nullable=False)
    phone_number = db.Column(db.String(20), nullable=False)
    display_name = db.Column(db.String(100), nullable=True)
    access_token = db.Column(db.String(500), nullable=False)
    webhook_verify_token = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.<PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_sync = db.Column(db.DateTime, nullable=True)
    
    def __repr__(self):
        return f'<WhatsAppAccount {self.phone_number}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'business_phone_number_id': self.business_phone_number_id,
            'phone_number': self.phone_number,
            'display_name': self.display_name,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_sync': self.last_sync.isoformat() if self.last_sync else None
        }

