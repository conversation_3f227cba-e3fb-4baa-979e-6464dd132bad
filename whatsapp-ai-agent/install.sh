#!/bin/bash

# WhatsApp AI Agent - Script de Instalação Automatizada
# Este script configura automaticamente o ambiente de desenvolvimento

set -e  # Parar em caso de erro

echo "🤖 WhatsApp AI Agent - Instalação Automatizada"
echo "================================================"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar se está executando no diretório correto
if [ ! -f "src/main.py" ]; then
    print_error "Execute este script no diretório raiz do projeto whatsapp-ai-agent"
    exit 1
fi

# Verificar dependências do sistema
print_status "Verificando dependências do sistema..."

# Verificar Python
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 não encontrado. Instale Python 3.11 ou superior."
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
print_success "Python $PYTHON_VERSION encontrado"

# Verificar Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js não encontrado. Instale Node.js 20 ou superior."
    exit 1
fi

NODE_VERSION=$(node --version)
print_success "Node.js $NODE_VERSION encontrado"

# Verificar pnpm
if ! command -v pnpm &> /dev/null; then
    print_warning "pnpm não encontrado. Instalando..."
    npm install -g pnpm
fi

# Configurar ambiente Python
print_status "Configurando ambiente Python..."

# Criar ambiente virtual se não existir
if [ ! -d "venv" ]; then
    print_status "Criando ambiente virtual Python..."
    python3 -m venv venv
    print_success "Ambiente virtual criado"
else
    print_success "Ambiente virtual já existe"
fi

# Ativar ambiente virtual
print_status "Ativando ambiente virtual..."
source venv/bin/activate

# Atualizar pip
print_status "Atualizando pip..."
pip install --upgrade pip

# Instalar dependências Python
print_status "Instalando dependências Python..."
pip install -r requirements.txt
print_success "Dependências Python instaladas"

# Configurar banco de dados
print_status "Configurando banco de dados..."
mkdir -p src/database
mkdir -p logs
mkdir -p uploads
mkdir -p backups

# Criar arquivo .env se não existir
if [ ! -f ".env" ]; then
    print_status "Criando arquivo de configuração .env..."
    cp .env.example .env
    print_warning "Configure as variáveis de ambiente no arquivo .env antes de executar a aplicação"
else
    print_success "Arquivo .env já existe"
fi

# Configurar frontend
print_status "Configurando frontend React..."

# Verificar se o diretório do dashboard existe
if [ -d "../whatsapp-dashboard" ]; then
    cd ../whatsapp-dashboard
    
    print_status "Instalando dependências do frontend..."
    pnpm install
    print_success "Dependências do frontend instaladas"
    
    print_status "Fazendo build do frontend..."
    pnpm run build
    print_success "Build do frontend concluído"
    
    print_status "Copiando arquivos para o backend..."
    cp -r dist/* ../whatsapp-ai-agent/src/static/
    print_success "Arquivos copiados para o backend"
    
    cd ../whatsapp-ai-agent
else
    print_warning "Diretório whatsapp-dashboard não encontrado. Frontend não configurado."
fi

# Criar scripts úteis
print_status "Criando scripts úteis..."

# Script para iniciar desenvolvimento
cat > start_dev.sh << 'EOF'
#!/bin/bash
echo "🚀 Iniciando WhatsApp AI Agent em modo desenvolvimento..."
source venv/bin/activate
export FLASK_ENV=development
python src/main.py
EOF

chmod +x start_dev.sh

# Script para fazer backup
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backups"
mkdir -p $BACKUP_DIR

echo "📦 Criando backup do banco de dados..."
cp src/database/app.db $BACKUP_DIR/backup_$DATE.db

echo "📦 Criando backup dos logs..."
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz logs/

echo "✅ Backup criado: backup_$DATE"
EOF

chmod +x backup.sh

# Script para atualizar frontend
cat > update_frontend.sh << 'EOF'
#!/bin/bash
echo "🔄 Atualizando frontend..."
cd ../whatsapp-dashboard
pnpm run build
cp -r dist/* ../whatsapp-ai-agent/src/static/
cd ../whatsapp-ai-agent
echo "✅ Frontend atualizado"
EOF

chmod +x update_frontend.sh

print_success "Scripts criados: start_dev.sh, backup.sh, update_frontend.sh"

# Verificar configuração do WhatsApp
print_status "Verificando configuração do WhatsApp..."

if grep -q "your_whatsapp_access_token_here" .env; then
    print_warning "Configure o WHATSAPP_ACCESS_TOKEN no arquivo .env"
fi

if grep -q "your_phone_number_id_here" .env; then
    print_warning "Configure o WHATSAPP_PHONE_NUMBER_ID no arquivo .env"
fi

if grep -q "your_webhook_verify_token_here" .env; then
    print_warning "Configure o WHATSAPP_VERIFY_TOKEN no arquivo .env"
fi

# Teste de instalação
print_status "Testando instalação..."

# Testar importações Python
python3 -c "
import sys
sys.path.append('src')
try:
    from main import app
    print('✅ Importações Python OK')
except Exception as e:
    print(f'❌ Erro nas importações: {e}')
    sys.exit(1)
"

print_success "Teste de instalação concluído"

# Resumo final
echo ""
echo "🎉 Instalação concluída com sucesso!"
echo "=================================="
echo ""
echo "📋 Próximos passos:"
echo "1. Configure as variáveis de ambiente no arquivo .env"
echo "2. Execute: ./start_dev.sh para iniciar em modo desenvolvimento"
echo "3. Acesse: http://localhost:5000"
echo ""
echo "📚 Documentação:"
echo "- README.md - Guia rápido"
echo "- DOCUMENTACAO_WHATSAPP_AI_AGENT.md - Documentação completa"
echo ""
echo "🔧 Scripts úteis:"
echo "- ./start_dev.sh - Iniciar desenvolvimento"
echo "- ./backup.sh - Fazer backup"
echo "- ./update_frontend.sh - Atualizar frontend"
echo ""
echo "⚠️  Lembre-se de configurar:"
echo "- WhatsApp Business API credentials"
echo "- Webhook URL no Meta for Developers"
echo "- Certificado SSL para produção"
echo ""
print_success "Instalação finalizada! 🚀"

