# WhatsApp Business API Configuration
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
WHATSAPP_VERIFY_TOKEN=your_webhook_verify_token_here
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id_here

# Flask Application Configuration
FLASK_ENV=development
SECRET_KEY=your_super_secret_key_here_change_in_production
DEBUG=True

# Database Configuration
DATABASE_URL=sqlite:///database/app.db
# Para PostgreSQL em produção:
# DATABASE_URL=postgresql://username:password@localhost:5432/whatsapp_ai

# Redis Configuration (opcional, para cache e sessões)
REDIS_URL=redis://localhost:6379/0

# Security Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_EXPIRATION_HOURS=24
BCRYPT_ROUNDS=12

# LGPD/Privacy Configuration
ENABLE_DATA_ANONYMIZATION=True
DATA_RETENTION_DAYS=365
AUDIT_LOG_RETENTION_DAYS=2555  # 7 anos conforme LGPD

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=True
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Email Configuration (para notificações)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_USE_TLS=True

# Webhook Configuration
WEBHOOK_URL=https://yourdomain.com/api/whatsapp/webhook
WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token

# AI/ML Configuration
AI_SENTIMENT_ENABLED=True
AI_INTENT_DETECTION_ENABLED=True
AI_RESPONSE_SUGGESTIONS_ENABLED=True
AI_CONFIDENCE_THRESHOLD=0.7

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/whatsapp_ai.log
LOG_MAX_BYTES=10485760  # 10MB
LOG_BACKUP_COUNT=5

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx,mp3,mp4,wav

# Backup Configuration
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=/backups

# Monitoring Configuration
HEALTH_CHECK_ENABLED=True
METRICS_ENABLED=True
PROMETHEUS_ENABLED=False

# Development Configuration
FRONTEND_DEV_SERVER=http://localhost:3000
API_BASE_URL=http://localhost:5000/api

# Production Configuration
DOMAIN=yourdomain.com
SSL_ENABLED=True
FORCE_HTTPS=True
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

