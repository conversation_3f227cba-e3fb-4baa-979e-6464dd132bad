# WhatsApp AI Agent - Documentação Completa

## Visão Geral

O WhatsApp AI Agent é uma solução completa para automação de interações no WhatsApp, replicando e expandindo as funcionalidades do WhaScale.com.br. O sistema oferece etiquetagem automática, salvamento de contatos, respostas rápidas, dashboard de gerenciamento, integração com WhatsApp Business API e recursos avançados de segurança e conformidade com a LGPD.

## Índice

1. [Arquitetura do Sistema](#arquitetura-do-sistema)
2. [Funcionalidades Principais](#funcionalidades-principais)
3. [Instalação e Configuração](#instalação-e-configuração)
4. [API Documentation](#api-documentation)
5. [Frontend Dashboard](#frontend-dashboard)
6. [Segurança e Conformidade](#segurança-e-conformidade)
7. [Integração WhatsApp Business API](#integração-whatsapp-business-api)
8. [Sistema de Automação e IA](#sistema-de-automação-e-ia)
9. [Deploy e Produção](#deploy-e-produção)
10. [Troubleshooting](#troubleshooting)

---

## Arquitetura do Sistema

### Tecnologias Utilizadas

**Backend:**
- Flask (Python) - Framework web
- SQLAlchemy - ORM para banco de dados
- SQLite - Banco de dados (desenvolvimento)
- Flask-CORS - Suporte a CORS
- PyJWT - Autenticação JWT
- Requests - Cliente HTTP para WhatsApp API

**Frontend:**
- React - Framework JavaScript
- Tailwind CSS - Framework CSS
- Shadcn/UI - Componentes de interface
- Lucide Icons - Ícones
- Vite - Build tool

**Segurança:**
- Criptografia de dados sensíveis
- Autenticação JWT
- Conformidade com LGPD
- Rate limiting
- Validação de entrada

### Estrutura de Diretórios

```
whatsapp-ai-agent/
├── src/
│   ├── models/           # Modelos de dados
│   │   ├── user.py
│   │   ├── contact.py
│   │   ├── message.py
│   │   ├── template.py
│   │   ├── automation_rule.py
│   │   └── whatsapp_account.py
│   ├── routes/           # Rotas da API
│   │   ├── auth.py
│   │   ├── contacts.py
│   │   ├── templates.py
│   │   ├── automation.py
│   │   ├── whatsapp.py
│   │   ├── ai_analysis.py
│   │   └── user.py
│   ├── services/         # Serviços de negócio
│   │   ├── whatsapp_service.py
│   │   ├── automation_service.py
│   │   ├── ai_service.py
│   │   └── security_service.py
│   ├── static/           # Arquivos estáticos (frontend)
│   ├── database/         # Banco de dados
│   └── main.py          # Arquivo principal
├── venv/                # Ambiente virtual Python
└── requirements.txt     # Dependências Python

whatsapp-dashboard/
├── src/
│   ├── components/      # Componentes React
│   │   ├── ui/         # Componentes base
│   │   ├── Layout.jsx
│   │   ├── Dashboard.jsx
│   │   ├── Contacts.jsx
│   │   └── Templates.jsx
│   ├── App.jsx         # Componente principal
│   └── main.jsx        # Ponto de entrada
├── dist/               # Build de produção
└── package.json        # Dependências Node.js
```

---

## Funcionalidades Principais

### 1. Etiquetagem Automática e Personalizada
- Análise em tempo real de mensagens recebidas
- Aplicação de etiquetas baseada em palavras-chave, intenções ou remetentes
- Critérios personalizáveis pelo usuário
- Suporte a múltiplas regras de etiquetagem

### 2. Salvamento Automático de Contatos
- Identificação automática de novos contatos em conversas
- Extração de informações como nome, email, telefone
- Salvamento automático no banco de contatos
- Atualização de informações existentes

### 3. Respostas Rápidas com Templates
- Templates de texto, áudio, imagens e vídeos
- Envio com um clique
- Criação e edição de templates personalizados
- Variáveis dinâmicas ({name}, {phone}, etc.)
- Categorização de templates

### 4. Dashboard de Gerenciamento
- Interface web intuitiva e responsiva
- Configuração de regras de automação
- Gerenciamento de templates de respostas
- Visualização de histórico de conversas
- Métricas de desempenho e relatórios
- Gestão de contas WhatsApp Business

### 5. Análise de IA Avançada
- Análise de sentimento das mensagens
- Classificação de intenções
- Extração automática de informações
- Sugestões de respostas inteligentes
- Detecção de urgência
- Análise de conversas completas

### 6. Integração WhatsApp Business API
- Envio e recebimento de mensagens
- Suporte a múltiplos tipos de mídia
- Webhooks para recebimento em tempo real
- Gestão de múltiplas contas
- Templates oficiais do WhatsApp

### 7. Segurança e Conformidade LGPD
- Criptografia de dados sensíveis
- Autenticação JWT
- Anonimização de dados pessoais
- Logs de auditoria
- Controle de acesso baseado em permissões
- Solicitações de dados pessoais

---

## Instalação e Configuração

### Pré-requisitos
- Python 3.11+
- Node.js 20+
- Git

### Instalação do Backend

1. **Clone o repositório:**
```bash
git clone <repository-url>
cd whatsapp-ai-agent
```

2. **Crie e ative o ambiente virtual:**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate  # Windows
```

3. **Instale as dependências:**
```bash
pip install -r requirements.txt
```

4. **Configure as variáveis de ambiente:**
```bash
export FLASK_ENV=development
export SECRET_KEY=your-secret-key
export WHATSAPP_ACCESS_TOKEN=your-whatsapp-token
export WHATSAPP_VERIFY_TOKEN=your-verify-token
```

5. **Inicie o servidor:**
```bash
python src/main.py
```

### Instalação do Frontend

1. **Navegue para o diretório do dashboard:**
```bash
cd whatsapp-dashboard
```

2. **Instale as dependências:**
```bash
pnpm install
```

3. **Inicie o servidor de desenvolvimento:**
```bash
pnpm run dev
```

4. **Para produção, faça o build:**
```bash
pnpm run build
cp -r dist/* ../whatsapp-ai-agent/src/static/
```

### Configuração do WhatsApp Business API

1. **Crie uma conta no Meta for Developers**
2. **Configure um app do WhatsApp Business**
3. **Obtenha o Access Token e Phone Number ID**
4. **Configure o webhook endpoint**
5. **Adicione as credenciais no sistema**

---


## API Documentation

### Autenticação

Todas as rotas protegidas requerem um token JWT no header Authorization:
```
Authorization: Bearer <jwt_token>
```

#### POST /api/auth/register
Registrar novo usuário.

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "message": "Usuário criado com sucesso",
  "token": "jwt_token",
  "user": {
    "id": 1,
    "username": "usuario",
    "email": "<EMAIL>"
  }
}
```

#### POST /api/auth/login
Fazer login do usuário.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "message": "Login realizado com sucesso",
  "token": "jwt_token",
  "user": {
    "id": 1,
    "username": "usuario",
    "email": "<EMAIL>"
  }
}
```

### Contatos

#### GET /api/contacts
Listar contatos com filtros opcionais.

**Query Parameters:**
- `status`: Filtrar por status (new, contacted, qualified, converted)
- `search`: Buscar por nome, telefone ou email
- `page`: Número da página (padrão: 1)
- `per_page`: Itens por página (padrão: 20)

**Response:**
```json
{
  "contacts": [
    {
      "id": 1,
      "phone_number": "+*************",
      "name": "João Silva",
      "email": "<EMAIL>",
      "status": "new",
      "tags": ["cliente", "interessado"],
      "deal_value": 1500.00,
      "created_at": "2024-01-01T10:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "per_page": 20
}
```

#### POST /api/contacts
Criar novo contato.

**Request Body:**
```json
{
  "phone_number": "+*************",
  "name": "João Silva",
  "email": "<EMAIL>",
  "status": "new",
  "tags": ["cliente"],
  "deal_value": 1500.00,
  "notes": "Interessado no produto X"
}
```

#### PUT /api/contacts/{id}
Atualizar contato existente.

#### DELETE /api/contacts/{id}
Deletar contato.

#### GET /api/contacts/stats
Obter estatísticas de contatos.

**Response:**
```json
{
  "total_contacts": 150,
  "new_contacts": 25,
  "qualified_contacts": 45,
  "converted_contacts": 30,
  "total_deal_value": 75000.00
}
```

#### GET /api/contacts/export
Exportar contatos em CSV.

### Templates

#### GET /api/templates
Listar templates com filtros opcionais.

**Query Parameters:**
- `category`: Filtrar por categoria
- `type`: Filtrar por tipo (text, image, audio, video)

**Response:**
```json
[
  {
    "id": 1,
    "name": "Saudação Inicial",
    "content": "Olá {name}, como posso ajudá-lo hoje?",
    "template_type": "text",
    "category": "saudacao",
    "variables": ["name"],
    "created_at": "2024-01-01T10:00:00Z"
  }
]
```

#### POST /api/templates
Criar novo template.

**Request Body:**
```json
{
  "name": "Saudação Inicial",
  "content": "Olá {name}, como posso ajudá-lo hoje?",
  "template_type": "text",
  "category": "saudacao",
  "media_url": null,
  "variables": ["name"]
}
```

#### PUT /api/templates/{id}
Atualizar template existente.

#### DELETE /api/templates/{id}
Deletar template.

#### GET /api/templates/categories
Listar categorias de templates.

### Automação

#### GET /api/automation/rules
Listar regras de automação.

**Response:**
```json
[
  {
    "id": 1,
    "name": "Etiqueta Urgente",
    "trigger_type": "keyword",
    "trigger_value": "urgente",
    "action_type": "add_tag",
    "action_value": "urgente",
    "is_active": true,
    "created_at": "2024-01-01T10:00:00Z"
  }
]
```

#### POST /api/automation/rules
Criar nova regra de automação.

**Request Body:**
```json
{
  "name": "Etiqueta Urgente",
  "trigger_type": "keyword",
  "trigger_value": "urgente",
  "action_type": "add_tag",
  "action_value": "urgente",
  "is_active": true
}
```

#### PUT /api/automation/rules/{id}
Atualizar regra de automação.

#### DELETE /api/automation/rules/{id}
Deletar regra de automação.

### WhatsApp

#### POST /api/whatsapp/send-message
Enviar mensagem via WhatsApp.

**Request Body:**
```json
{
  "to": "+*************",
  "type": "text",
  "text": {
    "body": "Olá! Como posso ajudá-lo?"
  }
}
```

#### POST /api/whatsapp/webhook
Webhook para receber mensagens do WhatsApp.

#### GET /api/whatsapp/accounts
Listar contas WhatsApp configuradas.

#### POST /api/whatsapp/accounts
Adicionar nova conta WhatsApp.

### Análise de IA

#### POST /api/ai/analyze-message
Analisar mensagem com IA.

**Request Body:**
```json
{
  "message": "Estou muito interessado no produto, preciso urgente!"
}
```

**Response:**
```json
{
  "sentiment": "positive",
  "intent": "purchase_interest",
  "urgency": "high",
  "entities": {
    "product_interest": true
  },
  "suggested_tags": ["interessado", "urgente"],
  "confidence": 0.85
}
```

#### POST /api/ai/suggest-response
Sugerir resposta para mensagem.

**Request Body:**
```json
{
  "message": "Qual o preço do produto X?",
  "context": {
    "contact_name": "João",
    "previous_messages": []
  }
}
```

**Response:**
```json
{
  "suggested_responses": [
    "Olá João! O produto X custa R$ 299,90. Gostaria de mais informações?",
    "Oi João! Temos uma promoção especial do produto X por R$ 249,90. Interessado?"
  ],
  "confidence": 0.92
}
```

---

## Frontend Dashboard

### Componentes Principais

#### Layout.jsx
Componente de layout principal que inclui:
- Sidebar de navegação
- Header com status do sistema
- Área de conteúdo principal
- Navegação responsiva

#### Dashboard.jsx
Página inicial com métricas e visão geral:
- Cards de estatísticas (contatos, leads, valor total)
- Gráficos de desempenho
- Lista de mensagens recentes
- Botão de atualização de dados

#### Contacts.jsx
Gerenciamento completo de contatos:
- Lista paginada de contatos
- Filtros por status e busca
- Formulário de criação/edição
- Exportação em CSV
- Tags e categorização
- Histórico de interações

#### Templates.jsx
Gerenciamento de templates:
- Grid de templates por categoria
- Criação de templates de texto, imagem, áudio e vídeo
- Preview de templates
- Variáveis dinâmicas
- Cópia rápida de conteúdo

### Funcionalidades da Interface

#### Sistema de Navegação
- Menu lateral com ícones intuitivos
- Indicador de página ativa
- Navegação responsiva para mobile
- Breadcrumbs quando necessário

#### Filtros e Busca
- Filtros dinâmicos por status, categoria, tipo
- Busca em tempo real
- Combinação de múltiplos filtros
- Limpeza rápida de filtros

#### Formulários
- Validação em tempo real
- Feedback visual de erros
- Campos obrigatórios marcados
- Salvamento automático (draft)

#### Tabelas e Listas
- Paginação inteligente
- Ordenação por colunas
- Seleção múltipla
- Ações em lote
- Exportação de dados

#### Modais e Dialogs
- Confirmação de ações destrutivas
- Formulários em overlay
- Carregamento assíncrono
- Escape para fechar

### Responsividade

O dashboard é totalmente responsivo e funciona em:
- Desktop (1920px+)
- Laptop (1024px - 1919px)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

### Temas e Personalização

- Tema claro/escuro (futuro)
- Cores personalizáveis
- Layout adaptável
- Preferências do usuário

---

## Segurança e Conformidade

### Autenticação e Autorização

#### JWT (JSON Web Tokens)
- Tokens com expiração configurável (24h padrão)
- Refresh tokens para renovação automática
- Payload com informações do usuário e permissões
- Assinatura com chave secreta robusta

#### Controle de Acesso
- Sistema de permissões baseado em roles
- Middleware de autenticação em todas as rotas protegidas
- Verificação de permissões específicas por endpoint
- Logs de tentativas de acesso

#### Hash de Senhas
- PBKDF2 com SHA-256
- Salt único por senha
- 100.000 iterações para resistência a ataques
- Verificação segura com compare_digest

### Conformidade com LGPD

#### Detecção de Dados Sensíveis
O sistema detecta automaticamente:
- CPF/CNPJ
- Emails
- Telefones
- Cartões de crédito
- Outros padrões configuráveis

#### Anonimização
- Mascaramento automático de dados sensíveis
- Anonimização configurável por tipo de dado
- Preservação da estrutura dos dados
- Reversibilidade controlada

#### Direitos do Titular
- Solicitação de dados pessoais
- Exclusão de dados (direito ao esquecimento)
- Portabilidade de dados
- Correção de informações

#### Logs de Auditoria
- Registro de todos os acessos a dados pessoais
- Logs de modificações e exclusões
- Rastreabilidade completa
- Retenção configurável

### Criptografia

#### Dados em Trânsito
- HTTPS obrigatório em produção
- TLS 1.3 recomendado
- Certificados SSL válidos
- Headers de segurança

#### Dados em Repouso
- Criptografia de campos sensíveis no banco
- Chaves de criptografia rotacionáveis
- Backup criptografado
- Acesso controlado aos dados

### Rate Limiting

#### Proteção contra Ataques
- Limite de requisições por IP
- Limite por usuário autenticado
- Janelas de tempo configuráveis
- Bloqueio temporário em caso de abuso

#### Implementação
```python
# Exemplo de configuração
RATE_LIMITS = {
    'login': '5 per minute',
    'api_general': '100 per hour',
    'whatsapp_send': '50 per hour'
}
```

### Validação de Entrada

#### Sanitização
- Remoção de caracteres perigosos
- Validação de tipos de dados
- Limitação de tamanho
- Escape de caracteres especiais

#### Validação de Schemas
```python
# Exemplo de regras de validação
contact_rules = {
    'phone_number': {
        'required': True,
        'type': str,
        'pattern': r'^\+[1-9]\d{1,14}$'
    },
    'email': {
        'required': False,
        'type': str,
        'pattern': r'^[^@]+@[^@]+\.[^@]+$'
    }
}
```

### Monitoramento de Segurança

#### Eventos Registrados
- Tentativas de login
- Acessos a dados sensíveis
- Modificações de configuração
- Erros de autenticação
- Atividades suspeitas

#### Alertas
- Múltiplas tentativas de login falhadas
- Acessos de IPs suspeitos
- Modificações não autorizadas
- Uso anômalo da API

---

## Integração WhatsApp Business API

### Configuração Inicial

#### Pré-requisitos
1. Conta Meta for Developers
2. App WhatsApp Business configurado
3. Número de telefone verificado
4. Webhook configurado

#### Credenciais Necessárias
```env
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_VERIFY_TOKEN=your_verify_token
WHATSAPP_WEBHOOK_URL=https://yourdomain.com/api/whatsapp/webhook
```

### Envio de Mensagens

#### Tipos de Mensagem Suportados

**Texto:**
```python
{
    "messaging_product": "whatsapp",
    "to": "+*************",
    "type": "text",
    "text": {
        "body": "Olá! Como posso ajudá-lo?"
    }
}
```

**Imagem:**
```python
{
    "messaging_product": "whatsapp",
    "to": "+*************",
    "type": "image",
    "image": {
        "link": "https://example.com/image.jpg",
        "caption": "Descrição da imagem"
    }
}
```

**Documento:**
```python
{
    "messaging_product": "whatsapp",
    "to": "+*************",
    "type": "document",
    "document": {
        "link": "https://example.com/document.pdf",
        "filename": "documento.pdf"
    }
}
```

**Template:**
```python
{
    "messaging_product": "whatsapp",
    "to": "+*************",
    "type": "template",
    "template": {
        "name": "hello_world",
        "language": {
            "code": "pt_BR"
        }
    }
}
```

### Recebimento de Mensagens

#### Webhook Configuration
O webhook deve ser configurado para receber mensagens em tempo real:

```python
@whatsapp_bp.route('/webhook', methods=['GET', 'POST'])
def webhook():
    if request.method == 'GET':
        # Verificação do webhook
        verify_token = request.args.get('hub.verify_token')
        if verify_token == WHATSAPP_VERIFY_TOKEN:
            return request.args.get('hub.challenge')
        return 'Forbidden', 403
    
    elif request.method == 'POST':
        # Processamento de mensagens recebidas
        data = request.get_json()
        process_incoming_message(data)
        return 'OK', 200
```

#### Processamento de Mensagens
```python
def process_incoming_message(data):
    for entry in data.get('entry', []):
        for change in entry.get('changes', []):
            if change.get('field') == 'messages':
                messages = change.get('value', {}).get('messages', [])
                for message in messages:
                    # Extrair informações da mensagem
                    from_number = message.get('from')
                    message_type = message.get('type')
                    timestamp = message.get('timestamp')
                    
                    # Processar baseado no tipo
                    if message_type == 'text':
                        text_content = message.get('text', {}).get('body')
                        handle_text_message(from_number, text_content, timestamp)
                    elif message_type == 'image':
                        handle_image_message(from_number, message, timestamp)
                    # ... outros tipos
```

### Gestão de Múltiplas Contas

#### Modelo de Dados
```python
class WhatsAppAccount(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    phone_number_id = db.Column(db.String(50), unique=True)
    access_token = db.Column(db.Text)
    business_account_id = db.Column(db.String(50))
    display_name = db.Column(db.String(100))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
```

#### Seleção de Conta
```python
def send_message_from_account(account_id, to, message_data):
    account = WhatsAppAccount.query.get(account_id)
    if not account or not account.is_active:
        raise ValueError("Conta inválida ou inativa")
    
    headers = {
        'Authorization': f'Bearer {account.access_token}',
        'Content-Type': 'application/json'
    }
    
    url = f"https://graph.facebook.com/v18.0/{account.phone_number_id}/messages"
    response = requests.post(url, headers=headers, json=message_data)
    
    return response.json()
```

### Tratamento de Erros

#### Códigos de Erro Comuns
- **400**: Requisição inválida
- **401**: Token de acesso inválido
- **403**: Permissões insuficientes
- **429**: Rate limit excedido
- **500**: Erro interno do WhatsApp

#### Retry Logic
```python
import time
from functools import wraps

def retry_on_failure(max_retries=3, delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except requests.exceptions.RequestException as e:
                    if attempt == max_retries - 1:
                        raise
                    time.sleep(delay * (2 ** attempt))  # Exponential backoff
            return None
        return wrapper
    return decorator

@retry_on_failure(max_retries=3)
def send_whatsapp_message(data):
    # Implementação do envio
    pass
```

### Webhooks e Eventos

#### Tipos de Eventos
- **messages**: Mensagens recebidas
- **message_deliveries**: Status de entrega
- **message_reads**: Mensagens lidas
- **message_reactions**: Reações às mensagens

#### Processamento Assíncrono
```python
from celery import Celery

celery = Celery('whatsapp_processor')

@celery.task
def process_webhook_async(webhook_data):
    # Processamento em background
    process_incoming_message(webhook_data)
    
    # Aplicar automações
    apply_automation_rules(webhook_data)
    
    # Atualizar estatísticas
    update_message_stats(webhook_data)
```

---


## Sistema de Automação e IA

### Engine de Automação

#### Tipos de Triggers (Gatilhos)
1. **Keyword**: Palavras-chave específicas
2. **Intent**: Intenções detectadas pela IA
3. **Sentiment**: Sentimento da mensagem
4. **Time**: Baseado em tempo (horário, data)
5. **Contact Status**: Status do contato
6. **Message Count**: Número de mensagens

#### Tipos de Actions (Ações)
1. **Add Tag**: Adicionar etiqueta
2. **Send Template**: Enviar template
3. **Update Contact**: Atualizar informações do contato
4. **Assign Agent**: Atribuir a um agente
5. **Create Task**: Criar tarefa
6. **Send Notification**: Enviar notificação

#### Exemplo de Regra
```python
{
    "name": "Interesse em Produto",
    "trigger_type": "keyword",
    "trigger_value": ["produto", "preço", "comprar"],
    "conditions": {
        "sentiment": "positive",
        "contact_status": ["new", "contacted"]
    },
    "actions": [
        {
            "type": "add_tag",
            "value": "interessado"
        },
        {
            "type": "send_template",
            "value": "template_produto_info"
        }
    ],
    "is_active": True
}
```

### Análise de IA

#### Análise de Sentimento
```python
def analyze_sentiment(text):
    """
    Analisa o sentimento de uma mensagem
    Retorna: positive, negative, neutral
    """
    # Palavras positivas e negativas em português
    positive_words = ['bom', 'ótimo', 'excelente', 'gostei', 'perfeito']
    negative_words = ['ruim', 'péssimo', 'horrível', 'odeio', 'terrível']
    
    text_lower = text.lower()
    positive_count = sum(1 for word in positive_words if word in text_lower)
    negative_count = sum(1 for word in negative_words if word in text_lower)
    
    if positive_count > negative_count:
        return 'positive'
    elif negative_count > positive_count:
        return 'negative'
    else:
        return 'neutral'
```

#### Detecção de Intenções
```python
def detect_intent(text):
    """
    Detecta a intenção da mensagem
    """
    intent_patterns = {
        'purchase_interest': ['comprar', 'preço', 'valor', 'quanto custa'],
        'support_request': ['ajuda', 'problema', 'suporte', 'não funciona'],
        'information_request': ['informação', 'detalhes', 'como funciona'],
        'complaint': ['reclamação', 'insatisfeito', 'problema'],
        'compliment': ['parabéns', 'excelente', 'muito bom']
    }
    
    text_lower = text.lower()
    detected_intents = []
    
    for intent, keywords in intent_patterns.items():
        if any(keyword in text_lower for keyword in keywords):
            detected_intents.append(intent)
    
    return detected_intents
```

#### Extração de Entidades
```python
def extract_entities(text):
    """
    Extrai entidades nomeadas do texto
    """
    import re
    
    entities = {}
    
    # Email
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)
    if emails:
        entities['emails'] = emails
    
    # Telefone
    phone_pattern = r'(?:\+55\s?)?(?:\(?[1-9]{2}\)?\s?)?(?:9\s?)?[0-9]{4}[-\s]?[0-9]{4}'
    phones = re.findall(phone_pattern, text)
    if phones:
        entities['phones'] = phones
    
    # Valores monetários
    money_pattern = r'R\$\s?(\d+(?:\.\d{3})*(?:,\d{2})?)'
    money_values = re.findall(money_pattern, text)
    if money_values:
        entities['money_values'] = money_values
    
    return entities
```

#### Sugestão de Respostas
```python
def suggest_responses(message, context=None):
    """
    Sugere respostas baseadas na mensagem e contexto
    """
    intent = detect_intent(message)
    sentiment = analyze_sentiment(message)
    
    suggestions = []
    
    if 'purchase_interest' in intent:
        suggestions.extend([
            "Ótimo! Temos várias opções disponíveis. Gostaria de ver nosso catálogo?",
            "Que bom que tem interesse! Posso enviar mais informações sobre nossos produtos.",
            "Perfeito! Vou te passar os detalhes e preços. Um momento..."
        ])
    
    if 'support_request' in intent:
        suggestions.extend([
            "Claro, estou aqui para ajudar! Pode me contar mais sobre o problema?",
            "Vou te ajudar a resolver isso. Pode me dar mais detalhes?",
            "Sem problemas! Vamos resolver juntos. O que está acontecendo?"
        ])
    
    if sentiment == 'negative':
        suggestions.extend([
            "Entendo sua preocupação. Vamos resolver isso juntos.",
            "Lamento pelo inconveniente. Como posso ajudar a melhorar a situação?",
            "Peço desculpas pelo problema. Vou fazer o possível para resolver."
        ])
    
    return suggestions[:3]  # Retorna até 3 sugestões
```

### Machine Learning (Futuro)

#### Classificação de Mensagens
- Treinamento com histórico de conversas
- Classificação automática de tipos de mensagem
- Melhoria contínua com feedback

#### Análise Preditiva
- Previsão de conversão de leads
- Identificação de clientes em risco
- Otimização de timing de mensagens

#### Personalização
- Respostas personalizadas por cliente
- Adaptação ao estilo de comunicação
- Recomendações de produtos

---

## Deploy e Produção

### Preparação para Produção

#### Configurações de Ambiente
```bash
# Variáveis de ambiente de produção
export FLASK_ENV=production
export SECRET_KEY=your-super-secret-production-key
export DATABASE_URL=postgresql://user:pass@host:port/dbname
export WHATSAPP_ACCESS_TOKEN=your-production-token
export WHATSAPP_VERIFY_TOKEN=your-production-verify-token
export REDIS_URL=redis://localhost:6379/0
```

#### Banco de Dados
```python
# Migração para PostgreSQL em produção
pip install psycopg2-binary

# Configuração no main.py
if os.environ.get('DATABASE_URL'):
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL')
else:
    app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'database', 'app.db')}"
```

#### Servidor WSGI
```python
# wsgi.py
from src.main import app

if __name__ == "__main__":
    app.run()
```

```bash
# Instalação do Gunicorn
pip install gunicorn

# Execução em produção
gunicorn --bind 0.0.0.0:5000 --workers 4 wsgi:app
```

### Docker

#### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copiar requirements e instalar dependências Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código da aplicação
COPY src/ ./src/
COPY wsgi.py .

# Criar usuário não-root
RUN useradd --create-home --shell /bin/bash app
USER app

# Expor porta
EXPOSE 5000

# Comando de inicialização
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "wsgi:app"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=**************************************/whatsapp_ai
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=whatsapp_ai
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web

volumes:
  postgres_data:
  redis_data:
```

### Nginx Configuration

#### nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    upstream app {
        server web:5000;
    }

    server {
        listen 80;
        server_name yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # Configurações SSL
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;

        # Headers de segurança
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

        location / {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Configuração para WebSocket (se necessário)
        location /ws {
            proxy_pass http://app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
```

### Monitoramento

#### Logs
```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    if not os.path.exists('logs'):
        os.mkdir('logs')
    
    file_handler = RotatingFileHandler('logs/whatsapp_ai.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    
    app.logger.setLevel(logging.INFO)
    app.logger.info('WhatsApp AI Agent startup')
```

#### Health Check
```python
@app.route('/health')
def health_check():
    """Endpoint para verificação de saúde da aplicação"""
    try:
        # Verificar conexão com banco
        db.session.execute('SELECT 1')
        
        # Verificar outras dependências
        status = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'database': 'connected'
        }
        
        return jsonify(status), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500
```

### Backup e Recuperação

#### Backup do Banco de Dados
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="whatsapp_ai"

# Criar backup
pg_dump $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# Manter apenas os últimos 7 backups
find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete

echo "Backup criado: backup_$DATE.sql"
```

#### Cron Job para Backup Automático
```bash
# Adicionar ao crontab
0 2 * * * /path/to/backup.sh
```

---

## Troubleshooting

### Problemas Comuns

#### 1. Erro de Conexão com WhatsApp API

**Sintomas:**
- Mensagens não são enviadas
- Erro 401 ou 403 na API

**Soluções:**
```python
# Verificar token de acesso
def verify_whatsapp_token():
    headers = {'Authorization': f'Bearer {WHATSAPP_ACCESS_TOKEN}'}
    response = requests.get(
        f'https://graph.facebook.com/v18.0/{WHATSAPP_PHONE_NUMBER_ID}',
        headers=headers
    )
    return response.status_code == 200

# Renovar token se necessário
if not verify_whatsapp_token():
    print("Token inválido - renovar no Meta for Developers")
```

#### 2. Webhook Não Recebe Mensagens

**Verificações:**
1. URL do webhook está correta
2. Certificado SSL válido
3. Verify token correto
4. Endpoint responde com status 200

```python
# Teste do webhook
@app.route('/test-webhook')
def test_webhook():
    return jsonify({
        'webhook_url': request.url_root + 'api/whatsapp/webhook',
        'verify_token': WHATSAPP_VERIFY_TOKEN,
        'ssl_valid': request.is_secure
    })
```

#### 3. Banco de Dados Não Conecta

**Soluções:**
```python
# Verificar conexão
try:
    db.session.execute('SELECT 1')
    print("Banco conectado com sucesso")
except Exception as e:
    print(f"Erro de conexão: {e}")
    
# Recriar tabelas se necessário
with app.app_context():
    db.create_all()
```

#### 4. Frontend Não Carrega

**Verificações:**
1. Build do React foi executado
2. Arquivos estão no diretório static
3. Rotas do Flask estão corretas

```bash
# Rebuild do frontend
cd whatsapp-dashboard
pnpm run build
cp -r dist/* ../whatsapp-ai-agent/src/static/
```

#### 5. Erro de CORS

**Solução:**
```python
from flask_cors import CORS

# Configurar CORS corretamente
CORS(app, origins=['http://localhost:3000', 'https://yourdomain.com'])
```

### Logs e Debugging

#### Ativar Logs Detalhados
```python
import logging

# Configurar nível de log
logging.basicConfig(level=logging.DEBUG)

# Log específico para WhatsApp
whatsapp_logger = logging.getLogger('whatsapp')
whatsapp_logger.setLevel(logging.DEBUG)
```

#### Monitorar Performance
```python
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        app.logger.info(f'{func.__name__} executou em {end_time - start_time:.2f}s')
        return result
    return wrapper

@monitor_performance
def send_whatsapp_message(data):
    # Implementação
    pass
```

### Comandos Úteis

#### Verificar Status dos Serviços
```bash
# Docker
docker-compose ps
docker-compose logs web

# Processos
ps aux | grep gunicorn
ps aux | grep nginx

# Portas
netstat -tlnp | grep :5000
```

#### Reiniciar Serviços
```bash
# Docker
docker-compose restart web
docker-compose restart nginx

# Systemd
sudo systemctl restart whatsapp-ai
sudo systemctl restart nginx
```

#### Verificar Logs
```bash
# Logs da aplicação
tail -f logs/whatsapp_ai.log

# Logs do sistema
sudo journalctl -u whatsapp-ai -f

# Logs do Nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Contato e Suporte

Para suporte técnico ou dúvidas sobre a implementação:

- **Email**: <EMAIL>
- **Documentação**: https://docs.whatsapp-ai-agent.com
- **Issues**: https://github.com/your-repo/whatsapp-ai-agent/issues

---

## Conclusão

O WhatsApp AI Agent é uma solução robusta e escalável para automação de interações no WhatsApp, oferecendo todas as funcionalidades necessárias para um atendimento eficiente e personalizado. Com recursos avançados de IA, segurança e conformidade com a LGPD, o sistema está preparado para atender desde pequenas empresas até grandes corporações.

A arquitetura modular permite fácil extensão e personalização, enquanto a documentação completa facilita a implementação e manutenção. O sistema está pronto para produção e pode ser facilmente deployado em diferentes ambientes.

**Versão da Documentação**: 1.0.0  
**Data de Atualização**: Janeiro 2024  
**Autor**: Equipe de Desenvolvimento WhatsApp AI Agent

---

